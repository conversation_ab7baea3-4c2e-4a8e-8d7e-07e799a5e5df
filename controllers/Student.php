<?php

/**
 * @description :  学生控制器
 * <AUTHOR>
 * Date: 17/9/25
 */
class Controller_Student extends Ap_Controller_Abstract{

	public $actions = array(
		'modify'            => 'actions/student/v1/Modify.php', //修改学生信息
		//'gethomework'       => 'actions/student/v1/GetHomework.php' ,//学生课程下作业情况
		'getmentoring'      => 'actions/student/v1/GetMentoring.php' , //学生课程答疑情况
		
		'studentindex'            => 'actions/student/v1/StudentIndex.php',        //学员详情首页(同步) by kouxiaofeng
		'studenttraderecordindex' => 'actions/student/v1/StudentTradeRecordIndex.php',//学生报名记录首页(异步) by kouxiaofeng
		//'studentserviceinfo'      => 'actions/student/v1/StudentServiceInfo.php',  //服务接触情况(异步) by koux<PERSON>of<PERSON>
		'studentcallinfo'         => 'actions/student/v1/StudentCallInfo.php',     //通话记录详情接口(异步) by kouxiaofeng
		//'studentclassingtestreport'=> 'actions/student/v1/StudentClassingTestReport.php',//课中测试报告(同步) by kouxiaofeng

		'getchat'           => 'actions/student/v1/GetChat.php' , //学生章节聊天情况
		'getscore'          => 'actions/student/v1/GetScore.php' , //学生章节学分明细
		'getonlinestat'     => 'actions/student/v1/GetOnlineStat.php' , //学生章节到课详情

		'exercisedetail'    => 'actions/student/v1/ExerciseDetail.php',    //获取章节学生习题(互动题详情)
		'getplayback'       => 'actions/student/v1/GetPlayback.php',    //获取章节学生回放详细

		'studentexercisedetail'     =>  'actions/student/v1/StudentExerciseDetail.php',//学生互动题弹窗

        'searchstudentinfo'         =>  'actions/student/v1/SearchStudentInfo.php',//搜索学员信息  by yangxiaoli
	);
}