<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2019/5/29
 * Time: 10:25 AM
 * 接收 Ares 推送过来的任务
 */
class Service_Page_Api_Ares_ReceiveTask{

    private $_objDsAresCourseTask;
    private $_objDsAssistantNewCourse;

    public function __construct() {
        $this->_objDsAresCourseTask     = new Service_Data_AresCourseTask();
        $this->_objDsAssistantNewCourse = new Service_Data_AssistantNewCourse();
    }

    public function execute($arrInput) {

        $taskId     = intval($arrInput['taskId']);
        $type       = intval($arrInput['type']);
        $courseInfo = $arrInput['courseInfo'] ? json_decode($arrInput['courseInfo'],true) : [];
        $courseId   = intval($courseInfo['courseId']);

        //建课信息转发到tower
        try {
            $info =[
                'courseId'      => $courseInfo['courseId'],
                'createTime'    => time(),
                'taskId'        => $arrInput['taskId'],
                'newCourseType' => $courseInfo['courseType'],
                'remark'        => '',
                'courseTags'    => $courseInfo['pullNewDuty'] ?? [],
            ];
            // $switch = AssistantDesk_Base_Ncm::getNcmData(AssistantDesk_Base_Ncm::TOWER_COURSE_ARES_SWITCH);
//            $whiteCourseList = AssistantDesk_Base_Ncm::getNcmData(AssistantDesk_Base_Ncm::TOWER_APPROVAL_COURSE_LIST);

            //开关打开 并且在白名单 则透传到tower
            $switch = 1;
            if( $switch == 1 ) {
                Bd_Log::addNotice("TOWER_ARES","命中白名单转发请求到tower,".$courseInfo['courseId']);
                Api_Tower_TowerDesk::addApproval($info);
            }else{
                Bd_Log::addNotice("TOWER_ARES","未中白名单,".$courseInfo['courseId'].",开关：$switch,whiteCourseList:".json_encode($whiteCourseList));
            }

        }catch (Exception $e){
            //加报警
            Api_Tower_DingDing::disTechAlarm('辅导desk转发建课任务失败,'.$e->getMessage());
            throw $e;
        }

        // 20250515 ncm开关已经全量 直接返回
        //失败抛异常了，成功return true
        return Api_Assignclass::turnReceiveTask($arrInput);
        /*
        $whiteFlag = AssistantDesk_Base_CourseAccessWhiteRule::isWitheCourse($courseId);
        //在白名单，转发请求到class
        if($whiteFlag)
        {
            Bd_Log::addNotice('命中白名单转发,whiteFlag_'.$courseId,$whiteFlag);
            //失败抛异常了，成功return true
            return Api_Assignclass::turnReceiveTask($arrInput);
        }

        Bd_Log::notice(__LINE__.":notice:[Ares-Data], Detail:[".json_encode($arrInput)."]");

        if(0 >= $taskId || 0 >= $courseId || 0 >= $type){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'Ares任务接收参数异常', $arrInput);
        }
        //如果当前taskID已经存在则直接返回oK
        $arrConds = [
            'taskId' => $taskId,
        ];
        $checkTask = $this->_objDsAresCourseTask->getList($arrConds);
        if(false === $checkTask){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '获取任务ID数据异常', $arrInput);
        }
        if($checkTask){
            Bd_Log::warning("Ares warning:[任务已有],Detail:[taskId:$taskId]");
            return ['ret' => true];
        }

        //如果当前课程-当前类型的任务已有，直接异常处理
        $arrConds = [
            'courseId' => $courseId,
            'type'     => $type,
            'deleted'  => Service_Data_AresCourseTask::DELETED_NO,
        ];
        $checkType = $this->_objDsAresCourseTask->getList($arrConds);
        if(false === $checkType){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '获取任务数据异常', $arrInput);
        }
        if($checkType){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::TASK_FINISH, '当前课程当前类型任务已存在', $arrInput);
        }

        $db = AssistantDesk_Db::getDbObj(AssistantDesk_Db::ZYB_FUDAO);
        $db->startTransaction();

        //任务入库
        $arrParams = [
            'taskId'    => $taskId,
            'courseId'  => $courseId,
            'type'      => $type,
            'status'    => Service_Data_AresCourseTask::STATUS_WAITING,
            'createTime'  => time(),
            'updateTime'  => time(),
            'operatorUid' => 100001,
            'deleted'     => Service_Data_AresCourseTask::DELETED_NO,
        ];

        $ret = $this->_objDsAresCourseTask->insert($arrParams);
        Bd_Log::notice(__LINE__.":notice:[Ares-DB], Detail:[".json_encode([$arrInput, $arrParams, $ret])."]");
        if(false === $ret){
            $db->rollback();
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'Ares任务接收新增失败', $arrInput);
        }

        $checkHave = $this->_objDsAssistantNewCourse->getListByConds(['courseId' => $courseId]);
        if(false === $checkHave){
            $db->rollback();
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '课程校验信息失败', $arrInput);
        }
        if(empty($checkHave)){
            //如果是实时排班/离线排班 任务，课程信息入表AssistantNewCourse
            if(Service_Data_AresCourseTask::TYPE_ASSIGN_CLASS_REAL == $type || Service_Data_AresCourseTask::TYPE_ASSIGN_CLASS_DELAY == $type){
                //处理课程信息
                $courseData = $this->_objDsAssistantNewCourse->getListByConds(['courseId' => $courseId]);
                if(false === $courseData){
                    $db->rollback();
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, 'DB校验课程信息失败', $arrInput);
                }

                $arrFields = $this->filterParams($courseInfo);

                //已提交状态
                $arrFields['submit'] = Service_Data_AssistantNewCourse::SUBMIT_YES;

                if($courseData){
                    $res = $this->_objDsAssistantNewCourse->updateAssistantNewCourse($courseId, $arrFields);
                }else{
                    $arrFields['courseId'] = $courseId;
                    $res = $this->_objDsAssistantNewCourse->addAssistantNewCourse($arrFields);
                }

                Bd_Log::notice(__LINE__.":notice:[Ares-DB], Detail:[".json_encode([$arrInput, $courseId, $arrFields, $ret])."]");
                if(false === $res){
                    $db->rollback();
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "同步课程信息失败", $arrInput);
                }
            }
        }

        //如果是群服务任务直接更新DB并回调Ares
        if(Service_Data_AresCourseTask::TYPE_SERVICE_GROUP == $type){
            $ret = AssistantDesk_Ares::ServiceDone([$taskId], 100001);
            if($ret === false){
                $db->rollback();
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, "服务配置失败", $arrInput);
            }
        }

        $db->commit();

        return ['ret' => true];
        */
    }

    /**
     * @param $arrParams
     * @return array
     * 参数接收
     */
    private function filterParams($arrParams){

        $arrFields = $extData = [];
        //参数的courseType是新课程类型，需要映射
        //读取ares提供ncm，读不到时使用本地映射
        $localMap   = '{"0":"0","1":"1","2":"2","3":"3","4":"4","6":"6","7":"7","8":"8","9":"9","10":"10","11":"11","12":"0","13":"0","14":"14","15":"15","16":"16","17":"17","18":"18","19":"0","20":"20","21":"21","22":"0","23":"0","25":"0","26":"0","27":"0","28":"0","29":"2","30":"30","31":"31","32":"0","33":"0","34":"0","35":"2","36":"36","37":"37","38":"38","39":"39","40":"40","41":"0","42":"0","43":"0","44":"0","45":"0","46":"0","47":"0","48":"0","49":"0","50":"2","51":"0","52":"0"}';
        $jsonTypeMap= Zb_Service_NCM::get(Zb_Service_NCM::APP_YIKE, 'miscourse', 'common', 'zb_new_old_course_type') ?: $localMap;
        $courseTypeNew2Old  = json_decode($jsonTypeMap, true);

        if (isset($arrParams['courseType'])) {
            $arrFields['newCourseType'] = intval($arrParams['courseType']);
            $arrFields['type']    = $courseTypeNew2Old[$arrParams['courseType']] ?? 0;
        }

        //判断课程名
        if(isset($arrParams['courseName'])){
            $arrFields['courseName'] = strval($arrParams['courseName']);
        }

        //课程开始时间
        if(isset($arrParams['startTime']) && $arrParams['startTime']){
            $arrFields['startTime']  = $arrParams['startTime'];
        }

        //课程结束时间
        if(isset($arrParams['stopTime']) && $arrParams['stopTime']){
            $arrFields['stopTime']   = $arrParams['stopTime'];
        }

        //判断年级 目前只接收主年纪的课程
        if(isset($arrParams['intGradeId'])){
            $arrFields['grade'] = $arrParams['intGradeId'];
        }

        //判断学科
        if(isset($arrParams['intSubjectId'])){
            $arrFields['subject'] = $arrParams['intSubjectId'];
        }

        //判断人数
        if(isset($arrParams['studentMaxCnt'])){
            $arrFields['studentMaxCnt'] = intval($arrParams['studentMaxCnt']);
        }

        //判断内外部课
        if(isset($arrParams['isInner'])){
            $arrFields['isInner'] = intval($arrParams['isInner']);
        }

        //判断学季
        if(isset($arrParams['learnSeason'])){
            $learnSeason = explode('_',Const_Season::getLearnSeasonIdNameFullMap()[intval($arrParams['learnSeason'])]);
            $arrFields['season'] = strval($learnSeason[0]);
            $arrFields['seasonNum'] = intval($learnSeason[1]);
        }

        //判断年份
        if(isset($arrParams['year'])){
            $arrFields['seasonYear'] = intval($arrParams['year']);
        }

        //是否是VIP课程
        if(isset($arrParams['isVipClass'])){
            $arrFields['isVipClass'] = intval($arrParams['isVipClass']);
        }

        //课程的拉新属性, bitmap 存储
        if(isset($arrParams['pullNewDuty'])){
            $arrFields['pullNewDuty'] = Service_Data_AssistantNewCourse::getPullNewDutyBit($arrParams['pullNewDuty']);
        }

        //判断章节
        $lessonList     = $arrParams['lessonList'];
        if($lessonList && is_array($lessonList)){

            $extLesson      = array();
            foreach($lessonList as $v){

                if(!$this->filterLesson($arrParams['courseType'], $v['courseLessonType'])){
                    //continue;
                }

                $extLesson[$v['lessonId']] = array(
                    'lessonId'  => $v['lessonId'],
                    'startTime' => $v['startTime'],
                    'stopTime'  => $v['stopTime'],
                    'status'    => Service_Data_AssistantNewCourse::LESSON_STATUS_OK,
                    'type'      => $v['courseLessonType'],
                );
            }

            $extData['lessonList'] = $extLesson;
        }

        if($extData){
            $arrFields['extData'] = $extData ? $extData : [];
        }

        return $arrFields;
    }

    /**
     * 判断章节是否需要保留
     * 班课保留核心课(主题课章节),短训班暂时都保留
     * @param int $courseType   课程类型
     * @param int $lessonType   章节类型
     * @return bool
     */
    private function filterLesson($courseType, $lessonType){

        $courseType = intval($courseType);
        $lessonType = intval($lessonType);

        //班课并且非主题课不保留
        if(Zb_Const_Course::TYPE_PRIVATE_LONG == $courseType && Zb_Const_Course::TYPE_COURSELESSONTYPE_CORE != $lessonType ){

            return false;
        }

        return true;
    }
}