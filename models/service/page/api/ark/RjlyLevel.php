<?php

class Service_Page_Api_Ark_RjlyLevel
{
    use AssistantDesk_Ark_Singleton;

    public function __construct() {
    }

    public function execute($arrInput) {
        $courseId     = $arrInput['courseId'];
        $rjylLeveInfoMap = Common_Singleton::getInstanceData("AssistantDesk_Rjyl", "unitStageSelectDataFilter", [$courseId]);
        $dataList = [];
        foreach ($rjylLeveInfoMap as $key => $val) {
            $dataList[] = [
                'id'    => $val,
                'name'  => $key,
            ];
        }

        return [
            'list' => $dataList,
        ];
    }
}