<?php
/**
 * @file DatabaseCallDetail.php
 * <AUTHOR>
 * @Date   2018年7月10日
 * @brief 资料库-电话录音详情
 */
class Service_Page_Check_V3_DatabaseCallDetail
{
    private $_libCheck;
    private $_objAssistantCallRecord;
    private $_objStudentInterview;
    private $_objAssistantNewCourse;
    private $_objCallScoreTable;
    private $_objScoreTable;

    public function __construct() {
        $this->_libCheck               = new AssistantDesk_Check();
        $this->_objAssistantCallRecord = new Service_Data_AssistantCallRecord();
        $this->_objStudentInterview    = new Service_Data_StudentInterview();
        $this->_objAssistantNewCourse  = new Service_Data_AssistantNewCourse();
        $this->_objCallScoreTable      = new Service_Data_CallScoreTable();
        $this->_objScoreTable          = new Service_Data_ScoreTable();
    }

    public function execute($arrInput) {
        $arrOutput = array();

        // 参数校验 && 获取录音信息
        $callInfo = $this->checkParams($arrInput);
        if (! $callInfo) {
            return $arrOutput;
        }

        // 录音类型转为十进制
        $sourceType = $this->_objAssistantCallRecord->reverseDbBitMap($callInfo['sourceType']);
        $teacherUid = $callInfo['fromUid'];
        $studentUid = $callInfo['toUid'];

        $arrOutput = array(
            'callInfo'         => array(),
            'assistantInfo'    => array(),
            'studentInfo'      => array(),
            'type'             => array(),
            'tableDepartment'  => array(),
            'warningLine'      => array(),
            'interviewTrue'    => array(),
            'studentInterview' => array(),
            'checkInfo'        => array(),
            'callScore'        => array(),
            'callInfoList'     => array(),
        );

        // 录音信息
        $arrOutput['callInfo'] = $this->formatCallInfo($callInfo);

        // 辅导老师信息
        $assistantInfo = Service_Data_Check_Teacher::getAssistantInfo($teacherUid);
        if (false === $assistantInfo) {
            Bd_Log::warning("Error:[getAssistantInfo error], Detail:[teacherUid: $teacherUid]");
        }
        $arrOutput['assistantInfo'] = $this->formatAssistantInfo($assistantInfo);

        // 辅导老师大组
        $gradeGroup = $assistantInfo['gradeGroupVal'];

        // 评分表筛选项
        $filterItem = $this->getFilterItem($gradeGroup, $sourceType[0]);
        $arrOutput['type']            = $filterItem['type'];
        $arrOutput['tableDepartment'] = $filterItem['tableDepartment'];
        $arrOutput['warningLine']     = $filterItem['warningLine'];
        $arrOutput['interviewTrue']   = $filterItem['interviewTrue'];


        // 操作人信息
        $checkInfo = $this->getOperatorInfo($callInfo['callId']);
        $arrOutput['checkInfo'] = $checkInfo;

        // 合规性
        $callScore = $this->getCallRecordScore($callInfo['callId'], $callInfo['checkStatus']);
        $arrOutput['callScore'] = $callScore;

        // 家访记录
        $studentInterview = array();
        if (!empty($callScore)) {
            $studentInterview = $this->getStudentInterview($callInfo['callId']);
            if (!empty($studentInterview)) {
                $studentInterview = $this->formatInterview($studentInterview);
            }
        }
        $arrOutput['studentInterview'] = $studentInterview;
        $arrOutput['callInfoList'] = $this->getCallInfo($callInfo['callId']);
        return $arrOutput;
    }

    /**
     * 获得录音转文字信息
     * @param int $callId
     * @return array
     */
    private function getCallInfo($callId)
    {
        $callInfo = (new Service_Data_AssistantCallTable())->getTextMapByCallId($callId);
        return $callInfo ? array_values($callInfo[$callId]) : array();
    }

    /**
     * 整理访谈记录返回格式
     * @param $studentInterview
     * @return array
     */
    private function formatInterview($studentInterview) {
        // 获取课程id对应课程名称
        $courseInfo = $this->_objAssistantNewCourse->getAssistantNewCourse($studentInterview['courseId'], array('courseId', 'courseName'));
        if (false === $courseInfo) {
            Bd_Log::notice("get assistant new course false, courseId = ". $studentInterview['courseId']);
            return [];
        }

        $type = AssistantDesk_Check::changeStudentInterviewType($studentInterview['type']);
        $courseId = $studentInterview['courseId'];

        $result = array(
            "id"                => $studentInterview['id'],
            "studentUid"        => $studentInterview['studentUid'],
            "interviewTime"     => date('Y-m-d H:i:s', $studentInterview['interviewTime']),
            "type"              => $type,
            "content"           => $studentInterview['content'],
            "operator"          => $studentInterview['operator'],
            "courseId"          => $courseId,
            "channelType"       => $studentInterview['channelType'],
            "courseName"        => $studentInterview['courseName'],
            "interviewRemark"   => $studentInterview['interviewRemark'],
            "interviewTrue"     => $studentInterview['interviewTrue'],
        );

        return $result;
    }

    /**
     * 获取家访记录
     * @param $callId
     * @return array|mix
     */
    private function getStudentInterview($callId) {

        $callRecord = $this->_objCallScoreTable->getCallScoreTableInfo($callId);
        if ($callRecord === false) {
            Bd_Log::notice("get call score table false, callId = ". $callId);
            return [];
        }

        $interviewId = $callRecord['interviewId'];

        if (empty($interviewId)) {
            return array();
        }

        $arrField = array(
            "interviewTime",
            "type",
            "content",
            "channelType",
            "operator",
            "courseId",
        );
        $interviewInfo = $this->_objStudentInterview->getStudentInterviewInfo($interviewId , $arrField);
        if ($interviewInfo === false) {
            Bd_Log::notice("get student interview false, interviewId = ". $interviewId);
            return [];
        }

        if (count($interviewInfo) <= 0 ) {
            return [];
        }

        // 获取课程名称
        //$courseInfo = $this->_objAssistantNewCourse->getAssistantNewCourse($interviewInfo['courseId'], ['courseName']);
        $courseData = Api_Dal::getCourseBaseByCourseIds([$interviewInfo['courseId']]);
        $courseInfo = $courseData[$interviewInfo['courseId']];

        // 添加备注和真实性
        $interviewInfo['interviewRemark'] = $callRecord['interviewRemark'];
        $interviewInfo['interviewTrue'] = $callRecord['interviewTrue'];
        // 课程名称
        $interviewInfo['courseName'] = $courseInfo['courseName'];
        $interviewInfo['channelType'] = $interviewInfo['channelType'] == 1 ? "手机" : "微信";

        return $interviewInfo;
    }

    /**
     * 获取质检是否完成
     * @param $callId
     * @return bool
     */
    private function _isFinshCheckByCallId($callId) {
        $arrFields = array('checkStatus');
        $info = $this->_objAssistantCallRecord->getAssistantCallRecordInfo($callId , $arrFields);
        if ($info === false) {
            Bd_Log::notice("get call record false, callId = ". $callId);
            return false;
        }
        if (count($info) === 0  ) {
            return false;
        }

        if (in_array($info['checkStatus'] , AssistantDesk_Check::$CHECK_RESULT_STATUS_MAP[AssistantDesk_Check::CHECK_QUALIFIED])
        || in_array($info['checkStatus'] , AssistantDesk_Check::$CHECK_RESULT_STATUS_MAP[AssistantDesk_Check::CHECK_UNQUALIFIED]) ) {
            return true;
        }
        return false;
    }

    /**
     * 录音合规性
     * @param $callId
     * @return array|bool
     */
    private function getCallRecordScore($callId, $checkStatus) {
        //获取质检是否完成
        $rt = $this->_isFinshCheckByCallId($callId);
        if (!$rt) {
            return array();
        }

        $arrConds = array(
            'callId' => $callId,
            'status' => Service_Data_CallScoreTable::STATUS_OK,
            '0'   => 'type in (1,2,3,4)',                          // 只获取质检完成的
        );
        $arrFields = array('scoreTableId','scoreItem','remark', 'type','warningLine' , 'interviewId');
        $scoreTable = $this->_objCallScoreTable->getScoreTableListByConds($arrConds , $arrFields);
        if ($scoreTable === false) {
            Bd_Log::notice("get call score table false, conds = ". json_encode($arrConds));
            return [];
        }
        if (count($scoreTable) <= 0 ) {
            return array();
        }
        $scoreTable = AssistantDesk_Tools::getNewKeyArray($scoreTable , 'type');

        //确认环节，因未记录违规信息，故需要取复检的
        if (AssistantDesk_Check::CHECK_STATUS_CONFIRM_UNQUALIFIED == $checkStatus) {
            $checkResult = $scoreTable[2];
        } else {
            ksort($scoreTable);
            // 获取最大的索引值的数组 （代表质检环节最多）
            $checkResult = array_pop($scoreTable);
        }

        // 获取录音类型  大组  课类型
        $arrFields = array('scoreTableName',"scoreTableDepartment" , 'type');
        $scoreTableInfo = $this->_objScoreTable->getScoreTableInfo($checkResult['scoreTableId'],$arrFields);

        $result = array();

        $result['type']             = $scoreTableInfo['type'];
        $result['tableDepartment']  = $scoreTableInfo['scoreTableDepartment'];
        $result['scoreTableName']   = $scoreTableInfo['scoreTableName'];
        $result['scoreItem']        = $checkResult['scoreItem'];
        $result['remark']           = $checkResult['remark'];
        $result['warningLine']      = $checkResult['warningLine'];
        $result['interviewId']      = $checkResult['interviewId'];

        return $result;
    }

    /**
     * 权限验证 & 参数校验
     * @throws Assistant_Unit_Exception
     * @param array $arrInput
     * @return array|bool
     */
    private function checkParams($arrInput) {
        if ($arrInput['callId'] <= 0) {
            Bd_Log::warning("Error:[checkParams error], Detail:[callId: {$arrInput['callId']}]");
            return false;
        }

        // 获取录音信息
        $arrFields = array('checkStatus', 'callId', 'sourceType', 'fromUid','fromPhone', 'toUid', 'toPhone', 'recordFile', 'courseId', 'startTime', 'stopTime', 'duration', 'checkStatusTime');
        $arrCallInfo = $this->_objAssistantCallRecord->getAssistantCallRecordInfo($arrInput['callId'], $arrFields);
        if (false === $arrCallInfo) {
            Bd_Log::warning("Error:[checkParams-getAssistantCallRecordInfo error], Detail:[callId: {$arrInput['callId']}]");
            return false;
        }
        if (empty($arrCallInfo)) {
            Bd_Log::warning("Error:[callId error], Detail:[callId: {$arrInput['callId']}]");
            return false;
        }
        return $arrCallInfo;
    }


    /**
     * 整理录音信息返回格式
     * @param $callInfo
     * @return array
     * @throws Exception
     */
    private function formatCallInfo($callInfo)
    {

        //获取课程名称
        $courseId  = $callInfo['courseId'];
        /*
        $coursInfo = $this->_objAssistantNewCourse->getAssistantNewCourse($courseId, array('courseId', 'courseName'));
        if (false === $coursInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR);
        }
        */
        $courseData = Api_Dal::getCourseBaseByCourseIds([$courseId]);
        if (false === $courseData) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SELECT_ERROR);
        }
        $coursInfo = $courseData[$courseId];

        $result = array(
            'callId'          => $callInfo['callId'],
            'callUrl'         => Hkzb_Util_FuDao::getFileUrl($callInfo['recordFile']),
            'toPhone'         => substr_replace($callInfo['toPhone'], '****', 3, 4),
            'fromPhone'       => substr_replace($callInfo['fromPhone'], '****', 3, 4),
            'sourceType'      => AssistantDesk_Check::changeCallRecordType($callInfo['sourceType']),
            'startTime'       => date('Y-m-d H:i:s', $callInfo['startTime'] / 1000),
            'stopTime'        => date('Y-m-d H:i:s', $callInfo['stopTime'] / 1000),
            'duration'        => intval($callInfo['duration'] / 1000) . 's',
            'checkStatusTime' => date('Y-m-d H:i:s', $callInfo['checkStatusTime']),
            'courseName'      => isset($coursInfo['courseName']) ? $coursInfo['courseName'] : '-',
        );

        return $result;
    }

    /**
     * 整理辅导老师信息返回格式
     * @param $assistantInfo
     * @return array
     */
    private function formatAssistantInfo($assistantInfo) {
        $result = array();
        if (empty($assistantInfo)) {
            return $result;
        }

        $result = array(
            'teacherName' => $assistantInfo['teacherName'],
            'nickname' => $assistantInfo['nickname'],
            'gradeGroup' => $assistantInfo['gradeGroup'],
            'position' => $assistantInfo['position'],
            'superior' => $assistantInfo['superior'],
        );

        return $result;
    }

    /**
     * 返回筛选项
     * @param $gradeGroup
     * @param $sourceType
     * @return array
     */
    private function getFilterItem($gradeGroup, $sourceType) {
        $type = array();
        $tableDepartment = array();
        $warningLine = array();
        $interviewTrue = array();

        //部门
        foreach(AssistantDesk_Check::$GRADE_GROUP as $k=>$v){
            if($gradeGroup == $k){
                $tableDepartment[] = array(
                    'id'     => $k,
                    'name'   => $v,
                    'choose' => 1,
                );
            }else{
                $tableDepartment[] = array(
                    'id'     => $k,
                    'name'   => $v,
                );
            }
        }
        //评分表的类型
        foreach(Service_Data_ScoreTable::$callSourceTypeMap as $k=>$v){
            if($sourceType == $k){
                $type[] = array(
                    'id'     => $k,
                    'name'   => $v,
                    'choose' => 1,
                );
            }else{
                $type[] = array(
                    'id'     => $k,
                    'name'   => $v,
                );
            }
        }

        // 红黄线
        foreach (Service_Data_CallScoreTable::$WARNING_LINE as $k => $v) {
            $warningLine[] = array(
                'id'   => $k,
                'name' => $v,
            );
        }

        // 记录是否真实性
        foreach (Service_Data_CallScoreTable::$INTERVIEW_TRUE as $k => $v) {
            $interviewTrue[] = array(
                'id'   => $k,
                'name' => $v,
            );
        }

        return array(
            'type'            => $type,
            'tableDepartment' => $tableDepartment,
            'warningLine'     => $warningLine,
            'interviewTrue'   => $interviewTrue,
        );
    }




    /**
     * 获取操作人信息
     * @param $callId
     * @param $type
     * @return array
     */
    private function getOperatorInfo($callId, $type = 0) {
        $result = array();
        $arrConds = array(
            'callId' => $callId,
            'status' => Service_Data_CallScoreTable::STATUS_OK,
        );
        $arrFields = array('operator', 'updateTime', 'type');
        switch ($type) {
            case Service_Data_CallScoreTable::TYPE_CHECK_DUPLICATE:
                $arrConds['type'] = Service_Data_CallScoreTable::TYPE_CHECK_INITIAL;
                break;
            case Service_Data_CallScoreTable::TYPE_CHECK_CONFIRM:
                $strType = Service_Data_CallScoreTable::TYPE_CHECK_INITIAL . ', ' . Service_Data_CallScoreTable::TYPE_CHECK_DUPLICATE;
                $arrConds[] = "type in ($strType)";
                break;
            case Service_Data_CallScoreTable::TYPE_CHECK_FINAL:
                $strType = Service_Data_CallScoreTable::TYPE_CHECK_INITIAL . ', ' . Service_Data_CallScoreTable::TYPE_CHECK_DUPLICATE . ', ' . Service_Data_CallScoreTable::TYPE_CHECK_CONFIRM;
                $arrConds[] = "type in ($strType)";
                break;
        }

        $arrCheckList = $this->_objCallScoreTable->getScoreTableListByConds($arrConds, $arrFields);
        if (false === $arrCheckList) {
            Bd_Log::warning('Error:[getCheckInfo] Details:[arrConds: ' . json_encode($arrConds) . ']');
            return $result;
        }
        if (empty($arrCheckList)) {
            return $result;
        }

        // 格式转换
        $map = array(
            Service_Data_CallScoreTable::TYPE_CHECK_INITIAL   => 'check',
            Service_Data_CallScoreTable::TYPE_CHECK_DUPLICATE => 'recheck',
            Service_Data_CallScoreTable::TYPE_CHECK_CONFIRM   => 'confirm',
            Service_Data_CallScoreTable::TYPE_CHECK_FINAL     => 'final',
        );
        foreach ($arrCheckList as $checkInfo) {
            $key = $map[$checkInfo['type']];
            $result[$key] = array(
                'name' => $checkInfo['operator'],
                'time' => date('Y-m-d H:i:s', $checkInfo['updateTime']),
            );
        }

        return $result;
    }
}
