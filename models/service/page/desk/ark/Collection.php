<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2019/8/26
 * Time: 19:44
 */
class Service_Page_Desk_Ark_Collection{
    use AssistantDesk_Ark_Singleton;

    public function __construct(){

    }

    /**
     * @param $arrInput
     * @return array
     * @throws Common_Exception
     */
    public function execute($arrInput){
        $assistantUid   = $arrInput['assistantUid'];
        $tplId          = $arrInput['tplId'];
        $serviceId      = $arrInput['serviceId'];

        $result = [
            'collection'    => [],
            'overview'      => [],
        ];

        if (0 >= $assistantUid || 0 >= $tplId) {
            Bd_Log::warning('参数异常');
            return $result;
        }

        // 1. 获取课程模板
        list($targetConfig, $overviewConfig) = $this->getTemplate($tplId, $serviceId);

        // 1.1 运营样式
        $sort = [];
        if (Api_Assistantdeskgo_Api::grayHit($arrInput['personUid'], Api_Assistantdeskgo_Const::GRAY_KEY_NEW_ARK)) {
            $arkGoArrInput = array(
                "tplId"             => $tplId,
                "serviceId"         =>$serviceId,
                "assistantUid" =>$assistantUid,
                "forBase"=>Const_Ark::GET_ARK_CONFIG_PRE_DATA
            );

            $arkGoOutput = Api_ArkGo::getTeacherTargetOverview($arkGoArrInput);
            $sort = $arkGoOutput['sort'];
            $targetConfig = $arkGoOutput['target'];
            $overviewConfig = $arkGoOutput['overview'];
        }

        // 2. 获取规则
        list($targetRules,$overviewRules,$dataPanelRules)    = (new Service_Page_Desk_Coursedata_Collection)->getRules();

        // 3. 获取所有数据方法并查询数据
        $adlData = AssistantDesk_Ark_Collection::getADLData($targetConfig, $overviewConfig, $targetRules, $overviewRules, $arrInput);

        // 4. 计算任务指标数据
        $collection = AssistantDesk_Ark_Collection::getTargetData($targetConfig, $targetRules, $adlData, $arrInput);

        // 5. 计算任务概览数据
        $overview   = AssistantDesk_Ark_Collection::getOverviewData($overviewConfig, $overviewRules, $adlData, $arrInput);


        Bd_Log::notice("add collection ".json_encode($collection));
        return [
            'collection'    => $collection,
            'overview'      => $overview,
            'sort'=>$sort
        ];
    }

    /**
     * 获取课程绑定模板
     * @param $courseId
     * @param $serviceId
     * @return array
     */
    private function getTemplate($tplId, $serviceId){
        $courseTargetsConfig  = AssistantDesk_Ark_ArkTemplate::getTemplateServiceModuleInfo($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET);
        $courseOverviewConfig = AssistantDesk_Ark_ArkTemplate::getTemplateServiceModuleInfo($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW);
        return [$courseTargetsConfig, $courseOverviewConfig];
    }
}
