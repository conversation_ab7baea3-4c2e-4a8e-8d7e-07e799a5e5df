<?php

/**
 * 方舟规则新增
 * User: liuxian<PERSON>@zuoyebang.com
 * Date: 2021/03/26
 * Time: 17:03
 */

class Service_Page_Desk_Ark_AddRule
{
    use AssistantDesk_Ark_Singleton;

    public function execute($arrInput)
    {
        $personUid = $arrInput['uid'];
        $ruleId    = $arrInput['id'];
        $moduleId  = $arrInput['moduleId'];
        $name      = $arrInput['name'];
        $remark    = $arrInput['remark'];
        $rule      = json_decode($arrInput['rule'], true) ? : [];

        $businessLine = AssistantDesk_Ark_ArkConfig::$arkModuleMapBusinessLine[$moduleId] ?? null;
        //添加权限控制
        $rightRes = Assistant_Common_Service_User::checkRole($personUid);
        if (!$rightRes) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NO_PRIVILEGE, '账号没有权限');
        }

        if(!$moduleId || !$name || !$rule || !$businessLine){
            throw new Common_Exception(Common_ExceptionCodes::PARAM_ERROR);
        }

        //检查
        list($check, $failAttr) = AssistantDesk_Ark_RuleParser::checkRule($moduleId, $rule);
        if(!$check){
            throw new Common_Exception(Common_ExceptionCodes::PARAM_ERROR, $failAttr . '规则格式不符');
        }

        if (!$this->checkRuleKey($ruleId, $moduleId, $rule)) {
            throw new Common_Exception(Common_ExceptionCodes::PARAM_ERROR, 'key重复，key和维度组合必须唯一');
        }

        if($ruleId){
            $oldRule = $this->getObjDsArkConfigRules()->getRecord([
                'id'    => $ruleId
            ]);
            if($oldRule['moduleId'] !== $moduleId){
                throw new Common_Exception(Common_ExceptionCodes::PARAM_ERROR, '模块不符');
            }
            //留下对rule的修改日志
            Bd_Log::notice("oldRule" . json_encode($oldRule));
        }

        $detail['version']    = 1;
        $detail['moduleId']   = $moduleId;
        $detail['businessLine']   = $businessLine;
        $detail['rule']       = json_encode($rule);
        $detail['name']       = $name;
        $detail['remark']     = $remark;
        $detail['status']     = Service_Data_Ark_ArkConfigRules::STATUS_ONLINE;
        $detail['updater']    = $personUid;
        $detail['updateTime'] = time();

        if($ruleId){
            $detail['id']     = $ruleId;
            $ret = $this->getObjDsArkConfigRules()->update(['id'  =>  $ruleId], $detail);
        }else{
            $detail['creator']    = $personUid;
            $detail['createTime'] = time();
            $ret = $this->getObjDsArkConfigRules()->insert($detail);
        }

        $this->delCache($moduleId);

        // arkGo cache
        if ($ruleId) {
            // del
            $inputRule['rule'] = $rule;
            $this->delArkGoCache($moduleId, $ruleId, $inputRule);
        }

        return [
            'ret'    => $ret,
        ];
    }

    private function checkRuleKey($ruleId, $moduleId, $rule) {
        //任务指标、任务概览、功能工具、字段列校验key和type组合必须唯一
        $checkModuleIds = [
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS,
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_NO_COURSE_FIELDS,
        ];

        if (in_array($moduleId, $checkModuleIds)) {
            $type   = $rule['type'];
            $key    = '';
            $keyStr = '';
            if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET) {
                $key    = $rule['taskKey'];
                $keyStr = 'taskKey';
            } else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW) {
                $key    = $rule['key'];
                $keyStr = 'key';
            } else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES) {
                $key    = $rule['componentKey'];
                $keyStr = 'componentKey';
            } else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS) {
                $key    = $rule['key'];
                $keyStr = 'key';
            } else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS) {
                $key    = $rule['key'];
                $keyStr = 'key';
            }else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS) {
                $key    = $rule['key'];
                $keyStr = 'key';
            }else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_NO_COURSE_FIELDS) {
                $key    = $rule['key'];
                $keyStr = 'key';
            }else if ($moduleId == AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL) {
                $key    = $rule['key'];
                $keyStr = 'key';
            }

            $rules = $this->getObjDsArkConfigRules()->getList(['id != ' . intval($ruleId), 'moduleId' => $moduleId, 'version' => 1, 'status' => 0]);
            if ($rules === false) {
                throw new Common_Exception(Common_ExceptionCodes::SELECT_FAILED, '获取规则失败');
            }
            Bd_Log::notice('checkRuleKey:' . json_encode([$type, $keyStr]));
            $typeKeys = [];
            Bd_Log::notice('checkRuleKey_rules:' . json_encode($rules));
            foreach ($rules as $one) {
                $tmpRule    = $one['rule'];
                $typeKeys[] = $tmpRule['type'] . '_' . $tmpRule[$keyStr];
            }
            Bd_Log::notice('typeKeys:' . json_encode($typeKeys));
            if (in_array($type . '_' . $key, $typeKeys)) {
                return false;
            }
        }
        return true;
    }


    public function delCache($moduleId) {
        $objRedis = Common_Redis::getInstance(Common_Redis::ZYB_CODIS_ZHIBO);
        $objRedis->del(sprintf(Service_Data_Ark_ArkConfigRules::RULES_CACHE_KEY, $moduleId));
    }

    public function delArkGoCache($moduleId, $id, $rule)
    {
        $keys[] = strval($id);
        $objRedis = Common_Redis::getInstance(Common_Redis::ZYB_CODIS_ZHIBO);
        $objRedis->del(sprintf(Service_Data_Ark_ArkConfigRules::RULES_CACHE_KEY_ARK_GO, $moduleId, $id));

        $mainKey = "";
        switch ($moduleId) {
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_NO_COURSE_FIELDS:
                $mainKey = "key";
                break;
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES:
                $mainKey = "componentKey";
                break;
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET:
                $mainKey = "taskKey";
                break;
        }

        if ($mainKey) {
            $keys[] = $rule['rule'][$mainKey];
            $objRedis->del(sprintf(Service_Data_Ark_ArkConfigRules::RULES_CACHE_KEY_ARK_GO, $moduleId, $rule['rule'][$mainKey]));
        }

        if (count($keys) > 0) {
            Api_ArkGo::reset([
                "moduleID" => $moduleId ?? 0,
                "keys"     => $keys ?? [],
            ]);
        }
    }
}