<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2019/4/8
 * Time: 11:38 AM
 * 返回页面MAP，供前端使用
 */
class Service_Page_Desk_Ark_TmplMaps{

    use AssistantDesk_Ark_Singleton;
    private $uid;
    public function execute($arrInput){

        $uid  = intval($arrInput['uid']);
        $businessLine  = intval($arrInput['businessLine']);
        $version  = intval($arrInput['version']);

        if(0 >= $uid){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误!');
        }

        $this->uid = $uid;

        return [
            'userTypeMap'       => $this->formatUserTypeMap($businessLine),      //组装用户类型MAP
            'transferTypeMap'   => $this->formatTransferTypeMap($businessLine),  //组装插班生MAP
            'homepageMap'       => $this->formatHomepageMap($businessLine),      //组装是否首页展示MAP
            'serviceConfigMap'  => Service_Data_Ark_ArkTemplate::isTogetherVersion($version) ? $this->formatServiceConfigMapV1() : $this->formatServiceConfigMap($businessLine), //组装服务配置MAP
            'serviceTypeList'   => $this->formatServiceTypeList($businessLine, $version),
        ];
    }

    private function formatServiceTypeList($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT, $version = \Service_Data_Ark_ArkTemplate::TMPL_VERSION_0): array {
        $map = [
            [
                'key'   => AssistantDesk_Config::TYPE_COURSE,
                'value' => '学员课程维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_COURSE],
            ],
            [
                'key'   => AssistantDesk_Config::TYPE_LESSON,
                'value' => '学员章节维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_LESSON],
            ],
            [
                'key'   => AssistantDesk_Config::TYPE_CONTRACT,
                'value' => '学员合约维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_CONTRACT],
            ],
            [
                'key'   => AssistantDesk_Config::TYPE_CONTRACT_COURSE,
                'value' => '学员合约维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_CONTRACT_COURSE],
            ],
            [
                'key'   => AssistantDesk_Config::TYPE_CONTRACT_LESSON,
                'value' => '学员合约维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_CONTRACT_LESSON],
            ],
        ];
        if ($businessLine == Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC
            || $businessLine == Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_DEER_PROGRAM
            || Service_Data_Ark_ArkTemplate::isTogetherVersion($version)
        ) {
            $map[] = [
                'key'   => AssistantDesk_Config::TYPE_SEND_COURSE,
                'value' => '学员赠课维度',
                'field' => AssistantDesk_Ark_ArkConfig::$serviceDimension[AssistantDesk_Config::TYPE_SEND_COURSE],
            ];
        }
        return $map;
    }

    /**
     * @param int $businessLine
     * @return array
     */
    private function formatUserTypeMap($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT): array {
        $userTypeMap = [];
        foreach (AssistantDesk_Ark_ArkConfig::$userType as $userType => $val){
            $ret['key']         = $userType;
            $ret['userType']    = $val;
            $userTypeMap[]      = $ret;
        }
        return $userTypeMap;
    }

    /**
     * @param int $businessLine
     * @return array
     */
    private function formatTransferTypeMap($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $userTypeMap = [];
        foreach (AssistantDesk_Ark_ArkConfig::$transferType as $userType => $val){
            $ret['key']          = $userType;
            $ret['transferType'] = $val;
            $userTypeMap[]       = $ret;
        }
        return $userTypeMap;
    }

    /**
     * @param int $businessLine
     * @return array
     */
    private function formatHomepageMap($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $homepageMap = [];
        foreach (AssistantDesk_Ark_ArkConfig::$homepageType as $key => $val){
            $ret['key']          = $key;
            $ret['value']        = $val;
            $homepageMap[]       = $ret;
        }
        return $homepageMap;
    }

    /**
     * @param int $businessLine
     * @return array
     */
    private function formatServiceConfigMap($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $allRules = $this->getObjDsArkConfigRules()->getRulesGroupByModuleId(0, 1, $businessLine);

        if(!$allRules){
            return [];
        }
        foreach($allRules as $modelId => $rules) {
            $allRules[$modelId] = array_filter($rules, function ($v) use ($businessLine) {
                if (!self::fieldEnableInOld($v['rule']['togetherType'] ?? 0)) {
                    return false;
                }
                return $v['businessLine'] == $businessLine;
            });
        }

        return [
            'taskTarget'    => $this->formatTaskTarget($allRules, $businessLine),
            'fieldConfig'   => $this->formatFieldConfig($allRules, $businessLine),
            'featureConfig' => $this->formatFeatureConfig($allRules, $businessLine),
            'overviewConfig' => $this->formatOverviewConfig($allRules, $businessLine),
            'courseDataConfig' => $this->formatCourseDataConfig($allRules, $businessLine),
        ];
    }

    public static function fieldEnableInV1($togetherType) {
        return in_array(intval($togetherType), [\AssistantDesk_Ark_ArkConfig::TOGETHER_TYPE_0, \AssistantDesk_Ark_ArkConfig::TOGETHER_TYPE_2]);
    }
    public static function fieldEnableInOld($togetherType) {
        return in_array(intval($togetherType), [\AssistantDesk_Ark_ArkConfig::TOGETHER_TYPE_0, \AssistantDesk_Ark_ArkConfig::TOGETHER_TYPE_1]);
    }
    /**
     * @return array
     */
    private function formatServiceConfigMapV1() {
        $fieldFromLine = [Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT, Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC];
        $allRules      = $this->getObjDsArkConfigRules()->getRulesGroupByModuleId(0, 1, $fieldFromLine);
        if (!$allRules) {
            return [];
        }
        foreach ($allRules as $modelId => $rules) {
            $afterRules = [];
            foreach($rules as $rule) {
                if (!self::fieldEnableInV1($rule['rule']['togetherType'] ?? 0)) {
                    continue;
                }
                if (!in_array($rule['businessLine'], $fieldFromLine)) {
                    continue;
                }
                $afterRules[] = $rule;
            }
            $allRules[$modelId] = $afterRules;
        }

        $dataPanel = $this->formatDataPanel($allRules);
        if(!in_array($this->uid, [2457697323, 3000165201,2457697323,3000039349])) {
            //灰度期间隐藏数据面板在模版上的展示
            $dataPanel["course"] = [];
            $dataPanel["lesson"] = [];
        }
        return [
            'taskTarget'       => $this->formatTaskTargetV1($allRules),
            'fieldConfig'      => $this->mergeToFieldsConfigList(
                $this->formatFieldConfigV1($allRules, Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT),
                $this->formatFieldConfigV1($allRules, Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC)
            ),
            'featureConfig'    => $this->formatFeatureConfigV1($allRules),
            'overviewConfig'   => $this->formatOverviewConfigV1($allRules),
            'dataPanel'        => $dataPanel,
            'courseDataConfig' => new stdClass(),
        ];
    }

    private function mergeToFieldsConfigList($config1, $config2) {
        $mergeList = [];
        foreach ($config1 as $type => $list) {
            foreach ($list as $value) {
                $fieldTypeNum                    = $value['fieldTypeNum'];
                $mergeList[$type][$fieldTypeNum] = $value;
            }
        }
        foreach ($config2 as $type => $list) {
            foreach ($list as $value) {
                $fieldTypeNum = $value['fieldTypeNum'];
                if (isset($mergeList[$type][$fieldTypeNum])) {
                    $mergeList[$type][$fieldTypeNum]['fields'] = array_merge($mergeList[$type][$fieldTypeNum]['fields'], $value['fields']);
                } else {
                    $mergeList[$type][$fieldTypeNum] = $value;
                }
            }
        }

        foreach ($mergeList as $type => $list) {
            ksort($list);
            $mergeList[$type] = array_values($list);
        }

        return $mergeList;
    }

    private function formatTaskTarget($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET,
            AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST
        );
    }

    private function formatFieldConfig($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $moduleId = AssistantDesk_Ark_ArkConfig::$arkBusinessLineMapFieldType[$businessLine];
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[$moduleId],
            $moduleId,
            AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST
        );
    }

    private function formatFeatureConfig($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES,
            AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST
        );
    }

    private function formatOverviewConfig($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW,
            AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST
        );
    }

    private function formatTaskTargetV1($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET,
            [AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST,AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA]
        );
    }

    private function formatDataPanel($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL,
            [AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST,AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA]
        );
    }

    private function formatFieldConfigV1($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $moduleId = AssistantDesk_Ark_ArkConfig::$arkBusinessLineMapFieldType[$businessLine];
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[$moduleId],
            $moduleId,
            [AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST,AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA]
        );
    }

    private function formatFeatureConfigV1($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES,
            [AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST,AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA]
        );
    }

    private function formatOverviewConfigV1($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        return AssistantDesk_Ark_RuleParser::formatRules(
            $allRules[AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW,
            [AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST,AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA]
        );
    }

    private function formatCourseDataConfig($allRules, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT){
        $map = [];
        foreach (AssistantDesk_Ark_ArkConfig::$arkModuleMap as $moduleId => $value) {
            foreach (AssistantDesk_Ark_ArkConfig::$serviceDimension as $type => $typeValue){
                $map[$value][$typeValue] = [];
            }
        }

        foreach ($allRules as $moduleId => $rules){
            $map[AssistantDesk_Ark_ArkConfig::$arkModuleMap[$moduleId]] = AssistantDesk_Ark_RuleParser::formatRules($rules, $moduleId, AssistantDesk_Ark_ArkConfig::ARK_APP_COURSE_DATA);
        }

        return $map;
    }
}