<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2019/4/9
 * Time: 4:46 PM
 * 模板新增编辑
 */
class Service_Page_Desk_Ark_TmplAdd{

    use AssistantDesk_Ark_Singleton;

    private $serviceNames = [];

    public function __construct(){
    }

    /**
     * @param $arrInput
     * @return array
     * @throws Common_Exception
     */
    public function execute($arrInput){
        $uid            = intval($arrInput['uid']);         //私人UID
        $tplId          = intval($arrInput['tplId']);
        $version        = intval($arrInput['version']);
        $bizLine        = intval($arrInput['bizLine']);
        $priceTagId     = intval($arrInput['priceTagId']);
        $sourceId       = intval($arrInput['sourceId']);    //从哪条复制过来的
        $type           = intval($arrInput['type']);        // 模板类型 0专题课 2班课
        $businessLine           = intval($arrInput['businessLine']);
        $name           = trim($arrInput['name']);
        $description    = trim($arrInput['description']);
        $serviceConfig  = $arrInput['serviceConfig'] ? @json_decode($arrInput['serviceConfig'], true) : [];
        $courseDataConfig  = $arrInput['courseDataConfig'] ? @json_decode($arrInput['courseDataConfig'], true) : [];

        if(0 >= $uid || empty($name) || !is_array($serviceConfig)){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '信息填写未完善!');
        }

        if ($version == Service_Data_Ark_ArkTemplate::TMPL_VERSION_1) {
            // 校验居左列是否配置，不配置居左列，不允许保存
            $leftRuleKeyMapName = [];
            $rules              = (new Service_Page_Desk_Filter_GetFilterMap())->getArkFieldConfig(Service_Data_Ark_ArkTemplate::TMPL_VERSION_1);
            foreach ($rules as $key => $rule) {
                if (($rule['feConfig']['fixed'] ?? '') == 'left') {
                    $leftRuleKeyMapName[$key] = $rule['oriName'] ?? $key;
                }
            }
            foreach ($serviceConfig as $taskList) {
                $serviceName = $taskList['serviceName'];
                $fields      = $taskList['field'];
                // 5. 处理fields
                $hasLeftField = false;
                foreach ($fields as $field) {
                    if (isset($leftRuleKeyMapName[$field['key']])) {
                        $hasLeftField = true;
                        break;
                    }
                }
                if (!$hasLeftField) {
                    throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, "服务项：{$serviceName},没有配置居左列，当前居左列有：" . implode(',', $leftRuleKeyMapName));
                }
            }
        }

        // 1. 开启事务
        $db = $this->getObjDb();
        $db->startTransaction();
        try {
            // 2. 新增或者编辑模板
            $tplId = $this->addServiceTmpl($uid, $tplId, $sourceId, $type, $name, $description, $businessLine, $version, $bizLine, $priceTagId);

            // 3. 格式化模板内容-并进行入库
            $services = $this->formatData($tplId, $serviceConfig, $type, false, $version);

            // 3. 格式化班级数据模板内容-并进行入库
            $courseDataServices = $this->formatData($tplId, $courseDataConfig, $type, true);

            // 4. 处理模板和服务绑定关系
            $this->bindServiceTemplate($tplId, $services, $courseDataServices);

            $db->commit();

            return [
               'tmplId'  => $tplId
            ];
        } catch (Exception $e) {
            $db->rollback();
            throw new Common_Exception($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @param $uid
     * @param $tplId
     * @param $sourceId
     * @param $type
     * @param $name
     * @param $description
     * @param int $businessLine
     * @param int $version
     * @param int $bizLine
     * @param int $priceTagId
     * @return bool | array
     * @throws Common_Exception
     */
    private function addServiceTmpl($uid, $tplId, $sourceId, $type, $name, $description, $businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT, $version =Service_Data_Ark_ArkTemplate::TMPL_VERSION_0 , $bizLine = 0, $priceTagId = 0){
        // 1. 获取人员信息
        $userInfo = Api_UserProfile::getUserInfoByPersonUid($uid);

        // 2.校验参数
        $arrConds = [
            'name' => $name,
            // 'businessLine' => $businessLine, // 名称重复db限制了名称不能重复，所以不同业务线，名字重复也会报错，需要报出来
            'tpl_id != '.$tplId,
        ];
        $tmplData = $this->getObjDsArkTemplate()->getList($arrConds);
        if($tmplData){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '服务配置名称重复!');
        }

        // 3.组装参数
        $arrParams = [
            'name'        => $name,
//            'sourceId'    => $sourceId,
            'type'        => $type,
            'description' => $description,
            'operatorUid' => $uid,
            'operator'    => $userInfo['record']['userName'] ? $userInfo['record']['userName'] : "",
            'updateTime'  => time(),
        ];

        //过滤0值
        if ($sourceId){
            $arrParams['sourceId'] = $sourceId;
        }

        // 4. 新增或编辑
        if($tplId){
            // 上线状态和下线状态的模板不可编辑
            $record = $this->getObjDsArkTemplate()->getRecord(['tplId' => $tplId]);
            if(!$record){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '模板信息查询失败!');
            }

            $ret = $this->getObjDsArkTemplate()->update(['tplId' => $tplId], $arrParams);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新服务模板失败!');
            }
            return $tplId;
        }

        $arrParams['businessLine']     = $businessLine; // 新增加入业务线 更新保留原来的
        $arrParams['version']          = $version;
        $arrParams['bizLine']          = $bizLine;
        $arrParams['priceTagId']       = $priceTagId;
        $arrParams['status']           = Service_Data_ServiceTmpl::TMPL_SAVE;
        $arrParams['createTime']       = time();
        $ret = $this->getObjDsArkTemplate()->insert($arrParams);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增服务模板失败!');
        }
        $tplId = $this->getObjDsArkTemplate()->getInsertId();
        return  $tplId;
    }

    /**
     * @param $tplId
     * @param $serviceConfig
     * @param $type
     * @param bool $isCourseData
     * @param int $version
     * @return array|bool
     * @throws Common_Exception
     */
    private function formatData($tplId, $serviceConfig, $type, $isCourseData = false, $version = Service_Data_Ark_ArkTemplate::TMPL_VERSION_0){
        $ret = [];
        if(empty($serviceConfig) || !is_array($serviceConfig)){
            return $ret;
        }

        $allNameMap = $this->getServiceNameMap($serviceConfig);
        $services = [];
        foreach ($serviceConfig as $taskList){

            $serviceName        = $taskList['serviceName'];
            $parentServiceName  = $taskList['parentServiceName'] ? $taskList['parentServiceName'] : '';
            $serviceType        = $taskList['serviceType'];
            $targets            = $taskList['target'];
            $fields             = $taskList['field'];
            $features           = $taskList['feature'];
            $overviews          = $taskList['overview'];
            $dataPanels          = $taskList['dataPanel'];
            $serviceId        = $taskList['serviceId'];
            $parentServiceId        = $taskList['parentServiceId'];


            // 1.1 处理服务id
            if ($serviceId == 0){
                $serviceId = $this->getServiceIdNew($serviceName,$allNameMap,false);
                //$serviceId = $this->getServiceId($serviceName,$serviceType,$isCourseData,$version);

            }
            // 1.2 处理父服务id
            if ($parentServiceId == 0 ) {
                $parentServiceId = $this->getServiceIdNew($parentServiceName,$allNameMap,true);
                //$parentServiceId = $this->getServiceId($parentServiceName);

            }
            // 2.拼装服务基本信息
            $service = [
                'serviceId'         =>  $serviceId,
                'parentServiceId'   =>  $parentServiceId,
                'parentServiceName' =>  $parentServiceName,
                'serviceName'       =>  $serviceName,
                'serviceType'       =>  $serviceType,
            ];
            $services[] = $service;
            // 3. 处理classCard 废弃
            // 4. 处理target
            $this->addServiceTarget($tplId, $serviceId, $targets, $type);
            // 5. 处理fields
            $this->addServiceFields($tplId, $serviceId, $fields);
            // 6. 处理features
            $this->addServiceFeatures($tplId, $serviceId, $features);
            // 7. 处理overviews
            $this->addServiceOverviews($tplId, $serviceId, $overviews);
            // 8. 处理dataPanels
            $this->addServiceDataPanel($tplId, $serviceId, $dataPanels, $type);
        }

        return $services;
    }

    /**
     * 获取服务名称id
     * @param $serviceName
     * @param int $serviceType
     * @param bool $isCourseData
     * @param int $version
     * @return integer
     * @throws Common_Exception
     */
    private function getServiceId($serviceName, $serviceType = 0, $isCourseData = false, $version = Service_Data_Ark_ArkTemplate::TMPL_VERSION_0){
        //班级数据serviceId
        if($isCourseData){
            return $this->getCourseDataServiceId($serviceType);
        }
        if (\Service_Data_Ark_ArkTemplate::isTogetherVersion($version)
            && $courseServiceId = $this->getCourseDataServiceIdV1($serviceName, $serviceType)) {
            return $courseServiceId;
        }
        //缓存已查过的服务（主要用于有父服务的场景）
        if(array_key_exists(md5($serviceName), $this->serviceNames)){
            return $this->serviceNames[md5($serviceName)];
        }
        if(!$serviceName){
            return 0;
        }
        $record = $this->getObjDsArkTemplateServiceName()->getRecord(['name' => $serviceName]);
        if(false === $record){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '查询模板名称失败!');
        }
        if($record['id']) {
            $this->serviceNames[md5($serviceName)] = $record['id'];
            return $record['id'];
        }
        //不存在就新建
        $arrParams = [
            'name' => $serviceName,
            'createTime' => time(),
            'updateTime' => time(),
        ];
        $ret = $this->getObjDsArkTemplateServiceName()->insert($arrParams);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板名称失败!');
        }

        $serviceId = $this->getObjDsArkTemplateServiceName()->getInsertId();
        $this->serviceNames[md5($serviceName)] = $serviceId;
        return $serviceId;
    }

    private function getServiceIdNew($serviceName,$allNameMap,$forParent){
        if (($serviceName == '') && !$forParent){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '模板名称为空');
        }

        if ($serviceName == ''){
            return 0;
        }

        return $allNameMap[$serviceName];
    }

    private function getServiceNameMap($serviceConfig): array
    {
        // 收集 serviceName
        $serviceNameMap = [];
        $createNames = [];
        foreach ($serviceConfig as $taskList){
            $serviceNameMap[$taskList['serviceName']] = $taskList['serviceId'];

            if ($taskList['serviceId'] == 0){
                $createNames[] = $taskList['serviceName'];
            }
        }

        foreach ($createNames as $serviceName){
            //不存在就新建
            $arrParams = [
                'name' => $serviceName,
                'createTime' => time(),
                'updateTime' => time(),
            ];
            $ret = $this->getObjDsArkTemplateServiceName()->insert($arrParams);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板名称失败!');
            }

            $serviceId = $this->getObjDsArkTemplateServiceName()->getInsertId();
            $serviceNameMap[$serviceName] = $serviceId;
        }
        return $serviceNameMap;
    }

    /**
     * 绑定服务和模板
     * @param $tplId
     * @param $services
     * @return bool
     * @throws Common_Exception
     */
    private function bindServiceTemplate($tplId, $services, $courseDataServices){
        if(!$services){
            return false;
        }

        if($courseDataServices){
            $services = array_merge($services, $courseDataServices);
        }

        $arrConds = [
            'tplId'    => $tplId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceRelation()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新绑定模版失败!');
        }

        foreach ($services as $service){
            $service = [
                'tplId'       => $tplId,
                'serviceId'   => $service['serviceId'],
                'serviceName' => $service['serviceName'],
                'serviceType' => $service['serviceType'],
                'parentServiceId'   => $service['parentServiceId'],
                'parentServiceName' => $service['parentServiceName'],
                'createTime'  => time(),
                'updateTime'  => time(),
            ];
            $ret = $this->getObjDsArkTemplateServiceRelation()->insert($service);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增绑定模版失败!');
            }
        }
    }

    /**
     * 新增指标配置
     * @param $tplId
     * @param $serviceId
     * @param $targets
     * @param $type
     * @return array
     * @throws Common_Exception
     */
    private function addServiceTarget($tplId, $serviceId, $targets, $type){

        $arrConds = [
            'tplId'    => $tplId,
            'serviceId' => $serviceId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceTarget()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新模版指标数据失败!');
        }

        //删除缓存
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET);

        if(!$targets || !is_array($targets)){
            return [];
        }

        foreach ($targets as $key => $target){
            if(empty($target)){
                continue;
            }
            if($target['list'] && is_array($target['list'])){
                foreach ($target['list'] as $val){
                    foreach ($val['userType'] as $userType){
                        if(!in_array($userType, array_keys(AssistantDesk_Ark_ArkConfig::$userType))){
                            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数不合法!');
                        }
                    }
                    //非班课插班生类型默认为10
                    if( Zb_Const_Course::TYPE_PRIVATE == $type){
                        $val['transferType'] = 10;
                    }
                    $arrParams = [
                        'tplId'      =>  $tplId,
                        'serviceId'  =>  $serviceId,
                        'targetKey'  =>  $target['taskKey'],
                        'targetName' =>  $val['taskName'],
                        'userType'   =>  json_encode($val['userType']),
                        'transferType'   =>  intval($val['transferType']),
                        'targetHomepage' =>  intval($val['targetHomepage']),
                        'targetHover'    =>  strval($val['targetHover']),
                        'createTime' => time(),
                        'updateTime' => time(),
                    ];
                    $ret = $this->getObjDsArkTemplateServiceTarget()->insert($arrParams);
                    if(false === $ret){
                        throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板指标数据失败!');
                    }
                }
            }
        }
    }

    private function addServiceDataPanel($tplId, $serviceId, $dataPanels, $type){

        $arrConds = [
            'tplId'    => $tplId,
            'serviceId' => $serviceId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceDataPanel()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新模版指标数据失败!');
        }

        //删除缓存
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL);

        if(!$dataPanels || !is_array($dataPanels)){
            return [];
        }

        $rules = $this->getObjDsArkConfigRules()->getRules(AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL);
        $dataPanelRule = Tools_Array::getNewKeyArray($rules, 'key');

        foreach ($dataPanels as $key => $dataPanel){
            if(empty($dataPanel)){
                continue;
            }
            if($dataPanel['list'] && is_array($dataPanel['list'])){
                foreach ($dataPanel['list'] as $val){
                    foreach ($val['userType'] as $userType){
                        if(!in_array($userType, array_keys(AssistantDesk_Ark_ArkConfig::$userType))){
                            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数不合法!');
                        }
                    }
                    //非班课插班生类型默认为10
                    if( Zb_Const_Course::TYPE_PRIVATE == $type){
                        $val['transferType'] = 10;
                    }
                    $arrParams = [
                        'tplId'      =>  $tplId,
                        'serviceId'  =>  $serviceId,
                        'arkKey'  =>  $dataPanel['key'],
                        'name' =>  $val['name'] ? $val['name']:$dataPanelRule[$dataPanel['key']]['name'],
                        'userType'   =>  json_encode($val['userType'])!='null'?json_encode($val['userType']):'[999]',
                        'transferType'   =>  intval($val['transferType'])? intval($val['transferType']):10,
                        'homepage' =>  intval($val['homepage']),
                        'hover'    =>  strval($val['hover'])?strval($val['hover']):'',
                        'createTime' => time(),
                        'updateTime' => time(),
                    ];
                    $ret = $this->getObjDsArkTemplateServiceDataPanel()->insert($arrParams);
                    if(false === $ret){
                        throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板数据看板数据失败!');
                    }
                }
            }
        }
    }



    /**
     * 新增字段列
     * @param $tplId
     * @param $serviceId
     * @param $fields
     * @return array|bool
     * @throws Common_Exception
     */
    private function addServiceFields($tplId, $serviceId, $fields){
        $arrConds = [
            'tplId'    => $tplId,
            'serviceId' => $serviceId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceField()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新模板字段数据失败!');
        }

        //删除缓存
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS);
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS);
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS);

        $ret = [];
        if(!$fields || !is_array($fields)){
            return $ret;
        }
        foreach ($fields as $key => $field){
            $arrParams = [
                'tplId'     => $tplId,
                'serviceId'  => $serviceId,
                'fieldKey'   => $field['key'],
                'fieldName'  => $field['name'],
                'fieldHover' => strval($field['hover']),
                'fieldHide'  => intval($field['hide']),
                'createTime' => time(),
                'updateTime' => time(),
            ];
            $ret = $this->getObjDsArkTemplateServiceField()->insert($arrParams);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板字段数据失败!');
            }
        }
    }

    /**
     * 新增功能工具配置
     * @param $tplId
     * @param $serviceId
     * @param $features
     * @return array|bool
     * @throws Common_Exception
     */
    private function addServiceFeatures($tplId, $serviceId, $features){
        $arrConds = [
            'tplId'    => $tplId,
            'serviceId' => $serviceId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceFeature()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新模板功能工具数据失败!');
        }

        //删除缓存
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES);

        $ret = [];
        if(!$features || !is_array($features)){
            return $ret;
        }
        foreach ($features as $key => $feature){
            $arrParams = [
                'tplId'        => $tplId,
                'serviceId'    => $serviceId,
                'componentKey' => $feature['key'],
                'options'      => json_encode($feature['value']),
                'createTime'   => time(),
                'updateTime'   => time(),
            ];
            $ret = $this->getObjDsArkTemplateServiceFeature()->insert($arrParams);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板功能工具数据失败!');
            }
        }
    }

    /**
     * 新增任务概览配置
     * @param $tplId
     * @param $serviceId
     * @param $overviews
     * @return array|bool
     * @throws Common_Exception
     */
    private function addServiceOverviews($tplId, $serviceId, $overviews){
        $arrConds = [
            'tplId'    => $tplId,
            'serviceId' => $serviceId,
        ];
        // 把之前绑定过的服务全部置为无效 再进行新增
        $ret = $this->getObjDsArkTemplateServiceOverview()->update($arrConds, ['status' => 1, 'updateTime' => time()]);
        if(false === $ret){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_UPDATE_FAILED, '更新模板任务概览数据失败!');
        }

        //删除缓存
        AssistantDesk_Ark_ArkTemplate::delCacheConfig($tplId, $serviceId, AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW);

        $ret = [];
        if(!$overviews || !is_array($overviews)){
            return $ret;
        }
        foreach ($overviews as $key => $overview){
            $arrParams = [
                'tplId'        => $tplId,
                'serviceId'    => $serviceId,
                'overviewKey'  => $overview,
                'createTime'   => time(),
                'updateTime'   => time(),
            ];
            $ret = $this->getObjDsArkTemplateServiceOverview()->insert($arrParams);
            if(false === $ret){
                throw new Common_Exception(Common_ExceptionCodes::DESK_DB_INSERT_FAILED, '新增模板任务概览数据失败!');
            }
        }
    }

    /**
     *
     * @param $serviceType
     * @return int
     */
    private function getCourseDataServiceId($serviceType) {
        return $serviceType ? AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_LESSON : AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_COURSE;
    }

    private function getCourseDataServiceIdV1($serviceName, $serviceType) {
        if ($serviceName == '学员章节表现' && $serviceType == \AssistantDesk_Ark_ArkConfig::CONFIG_TYPE_LESSON) {
            return AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_LESSON;
        }
        if ($serviceName == '学员课程表现' && $serviceType == \AssistantDesk_Ark_ArkConfig::CONFIG_TYPE_COURSE) {
            return AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_COURSE;
        }

        return null;
    }
}