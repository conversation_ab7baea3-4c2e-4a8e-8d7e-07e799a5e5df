<?php
/**
 * Created by PhpStorm.
 * User: pangjuncha<PERSON>@zuoyebang.com
 * Date: 2019/4/10
 * Time: 5:03 PM
 * 根据模板ID获取模板内容
 */
class Service_Page_Desk_Ark_TmplData{

    use AssistantDesk_Ark_Singleton;

    public function __construct(){
    }

    public function execute($arrInput){

        $uid   = intval($arrInput['uid']);
        $tplId = intval($arrInput['tplId']);
        $version = intval($arrInput['version']);
        $forTeacherConfig = intval($arrInput['forTeacherConfig']);


        if(0 >= $uid || 0 >= $tplId){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误!');
        }

        $ret = [];
        $arrConds = [
            'tplId' => $tplId,
        ];
        $tplData = $this->getObjDsArkTemplate()->getRecord($arrConds);

        $ret['name']        = $tplData['name'];
        $ret['businessLine']= $tplData['businessLine'];
        $ret['type']        = $tplData['type'];
        $ret['description'] = $tplData['description'];
        list($ret['content'], $ret['courseDataContent'])     = $this->formatContent($tplId, $version,$forTeacherConfig);
        return ['data' => $ret];
    }

    /**
     * @param $tplId
     * @param int $version
     * @return array
     * 格式化数据输出
     */
    private function formatContent($tplId, $version = Service_Data_Ark_ArkTemplate::TMPL_VERSION_0,$forTeacherConfig = 0){
        $arrConds = [
            'tplId' => $tplId,
            'status'=> 0,
        ];
        $tplServiceRelation = $this->getObjDsArkTemplateServiceRelation()->order($arrConds, [], ["id" => 'asc']);//getList($arrConds);

        $tplServiceRelation = Tools_Array::getNewKeyArray($tplServiceRelation, 'serviceId');
        $serviceIds = array_keys($tplServiceRelation);
        $arrConds[] = 'service_id in ('. implode(',', $serviceIds) . ')';

        $targets     = $this->getObjDsArkTemplateServiceTarget()->getList($arrConds);
        $fields      = $this->getObjDsArkTemplateServiceField()->getList($arrConds);
        $features    = $this->getObjDsArkTemplateServiceFeature()->getList($arrConds);
        $overviews   = $this->getObjDsArkTemplateServiceOverview()->getList($arrConds);
        // 数据看板
        $dataPanels   = $this->getObjDsArkTemplateServiceDataPanel()->getList($arrConds);


        $targets   = Tools_Array::groupByColumn($targets, 'serviceId');
        $fields    = Tools_Array::groupByColumn($fields, 'serviceId');
        $features  = Tools_Array::groupByColumn($features, 'serviceId');
        $overviews = Tools_Array::groupByColumn($overviews, 'serviceId');
        $dataPanels = Tools_Array::groupByColumn($dataPanels, 'serviceId');

        $taskList = $courseData = [];
        foreach ($tplServiceRelation as $serviceId => $service){
            $row['serviceId']    = $service['serviceId'];
            $row['serviceName']  = $service['serviceName'];
            $row['serviceType']  = $service['serviceType'];
            $row['parentServiceId']    = $service['parentServiceId'];
            $row['parentServiceName']  = $service['parentServiceName'];

            $row['target']       = $this->formatClassTarget($targets[$serviceId]);
            $row['field']        = $this->formatClassField($fields[$serviceId],$forTeacherConfig);
            $row['feature']      = $this->formatClassFeature($features[$serviceId]);
            $row['overview']     = $this->formatClassOverview($overviews[$serviceId]);
            $row['dataPanel']     = $this->formatClassDataPanel($dataPanels[$serviceId]);


            //班级数据的id
            if(in_array($serviceId, [
                AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_COURSE,
                AssistantDesk_Ark_ArkConfig::COURSE_DATA_SERVICE_ID_LESSON
            ])
             && !Service_Data_Ark_ArkTemplate::isTogetherVersion($version)
            ){
                $courseData[] = $row;
                continue;
            }
            $taskList[] = $row;
        }
        return [$taskList, $courseData];
    }

    private function formatClassTarget($targets){
        $ret = [];
        if(!$targets){
            return $ret;
        }

        list($targetRules,$overviewRules,$dataPanelRules)    = (new Service_Page_Desk_Coursedata_Collection)->getRules();

        $targets = Tools_Array::groupByColumn($targets, 'targetKey');
        foreach ($targets as  $targetKey => $targetKeyArr){

            if (empty($targetRules[$targetKey])){
                continue;
            }

            $row['taskKey'] = $targetKey;
            $row['list'] = [];
            foreach ($targetKeyArr as $target){
                $_row['taskName']     = $target['targetName'];
                $_row['userType']     = $target['userType'];
                $_row['transferType'] = $target['transferType'];
                $_row['targetHomepage']     = $target['targetHomepage'];
                $_row['targetHover']        = $target['targetHover'];

                $row['list'][] =$_row;
            }
            $ret[] = $row;
        }
        return $ret;
    }

    private function formatClassDataPanel($dataPanels){
        $ret = [];
        if(!$dataPanels){
            return $ret;
        }

        list($targetRules,$overviewRules,$dataPanelRules)    = (new Service_Page_Desk_Coursedata_Collection)->getRules();

        $dataPanels = Tools_Array::groupByColumn($dataPanels, 'arkKey');
        foreach ($dataPanels as $dataPanelKey => $dataPanelKeyArr){

            if (empty($dataPanelRules[$dataPanelKey])){
                continue;
            }

            $row['key'] = $dataPanelKey;
            $row['list'] = [];
            foreach ($dataPanelKeyArr as $dataPanel){
                $_row['name']     = $dataPanel['name'];
                $_row['userType']     = $dataPanel['userType'];
                $_row['transferType'] = $dataPanel['transferType'];
                $_row['homepage']     = $dataPanel['homepage'];
                $_row['hover']        = $dataPanel['hover'];

                $row['list'][] =$_row;
            }
            $ret[] = $row;
        }
        return $ret;
    }

    private function formatClassField($fields,$forTeacherConfig = 0){
        $ret = [];
        if(!$fields){
            return $ret;
        }
        foreach ($fields as $field){
            // 老师环节配置不能选方舟模板上隐藏的字段
            if (($forTeacherConfig == 1) && $field['fieldHide'] == 1){
                continue;
            }
            $row['key']     = $field['fieldKey'];
            $row['name']    = $field['fieldName'];
            $row['hover']   = $field['fieldHover'];
            $row['hide']    = $field['fieldHide'];
            $ret[] = $row;
        }
        return $ret;
    }

    private function formatClassFeature($features){
        $ret = [];
        if(!$features){
            return $ret;
        }
        foreach ($features as $feature){
            $row['componentKey'] = $feature['componentKey'];
            $row['options']      = $feature['options'];
            $ret[] = $row;
        }
        return $ret;
    }

    private function formatClassOverview($overviews){
        list($targetRules,$overviewRules,$dataPanelRules)    = (new Service_Page_Desk_Coursedata_Collection)->getRules();
        $ret = [];
        if(!$overviews){
            return $ret;
        }
        foreach ($overviews as $overview){
            if (empty($overviewRules[$overview['overviewKey']])){
                continue;
            }

            $ret[] = $overview['overviewKey'];
        }
        return $ret;
    }
}