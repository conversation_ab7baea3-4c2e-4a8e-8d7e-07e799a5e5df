<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2019/7/10
 * Time: 20:09
 * 维系详情页是否展示手动点亮微信按钮
 */

class Service_Page_Desk_Student_GetWxBindInfo{

    private $objDsNewCourse      = null;
    private $objDsShowWxCourse   = null;

    public function __construct(){
        $this->objDsNewCourse      = new Service_Data_AssistantNewCourse();
        $this->objDsShowWxCourse   = new Assistant_Ds_ShowWxCourse();
    }

    private static $showWxCourseIds = [427097,427070,427065,427060,427055,427050];

    public function execute($arrInput){
        $assistantUid   = $arrInput['assistantUid'];
        $studentUid     = $arrInput['studentUid'];
        $courseId       = $arrInput['courseId'];

        if (0 >= $assistantUid || 0 >= $studentUid || 0 >= $courseId){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误');
        }

        $isShowBtn = 0;
        // 20250515 该表中经查询 加油课业务最后更新时间为2023-02-15 15:52:18  之后业务线就下线了
        $courseInfo = [];
        /*
        $courseInfo = $this->objDsNewCourse->getAssistantNewCourse($courseId);
        if (false === $courseInfo){
            Bd_Log::warning('获取课程信息错误');
            $courseInfo = [];
        }
        */

        //加油课展示微信
        if ($courseInfo['type'] == Zb_Const_Course::TYPE_PRIVATE &&
            ($courseInfo['pullNewDuty'] & Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_2286) > 0){
            $isShowBtn = 1;
        }

        $courseList = $this->objDsShowWxCourse->getList(['courseId' => $courseId, 'status' => Assistant_Ds_ShowWxCourse::STATUS_VALID]);
        if ($courseList === false || !is_array($courseList)) {
            Bd_Log::warning('手动点亮微信课程查询失败');
            $courseList = [];
        }
        if (!empty($courseList)) {
            $isShowBtn = 1;
        }

        //根据微信类型获取辅导老师手动标记微信关联信息
        $appId = AssistantDesk_KunpengEnterprise::weChatAppid($assistantUid);
        $bindData = AssistantDesk_KunpengEnterprise::manualBindData($assistantUid, $studentUid, $appId);
        $isBind = !empty($bindData) ? $bindData[0]['isBind'] : 0;

        return ["isBind" => $isBind, "isShow" => $isShowBtn];
    }
}