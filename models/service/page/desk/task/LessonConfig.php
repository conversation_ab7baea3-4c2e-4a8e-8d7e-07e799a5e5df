<?php
/**
 * Create From PhpStrom
 * User: GN <<EMAIL>>
 * CreateTime:  2019/12/5 19:21
 * FileName: LessonConfig.php
 * Ds:  */

class Service_Page_Desk_Task_LessonConfig {

    const BTN_HIDDEN  = 0;//0;//隐藏按钮
    const BTN_DISABLED_TRUE  = 1;//1;// 按钮置灰
    const BTN_DISABLED_FALSE = 2;//按钮可以点击

    private $_objDsFirstLineTeacherCourse;

    public function __construct() {
        $this->_objDsFirstLineTeacherCourse = new Assistant_Ds_FirstLineTeacherCourse();
    }

    public function execute($arrInput) {
        $assistantUid = $arrInput['assistantUid'];
        $lessonId     = $arrInput['lessonId'];
        $stageId      = $arrInput['stageId'];
        $courseId     = $arrInput['courseId'];
        $personUid     = $arrInput['personUid'];

        $buttonList = [
            'prevButton'      => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW,
                'name'        => '催预习',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW,
                'componentKey' => 'broadcastButton'
            ],
            'preAttendButton' => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREATTEND,
                'name'        => '一键预到课',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '上一章节下课后才可执行预到课操作',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREATTEND,
            ],
            'attendButton'    => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND,
                'name'        => '催到课',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '开课前30min才可执行催到课操作',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND,
            ],
            'attendButtonGuanjia'    => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND,
                'name'        => '催到课',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '开课前30min才可执行催到课操作',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND,
            ],
            'playbackButton'  => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK,
                'name'        => '催回放',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '章节未下课，无法催回放',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK,
            ],
            'notesButton'     => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES,
                'name'        => '发课堂笔记',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES,
            ],
            'homeworkButton'  => [
                'val'         => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HOMEWORK,
                'name'        => '催巩固练习',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HOMEWORK,
            ],
            'previewGroupSend'=> [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_7,
                'name'        => '群发预习点评',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7,
            ],
            'previewAnalysis' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_8,
                'name'        => '预习解析',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
            ],
            'checkTestAnalyse'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ANALYSE_CHECK_TEST,
                'name'        => '题目解析',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'previewAnalyse'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ANALYSE_PREVIEW,
                'name'        => '题目解析',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'inClassAnalyse'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ANALYSE_IN_CLASS_TEST,
                'name'        => '题目解析',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'homeworkAnalyse'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ANALYSE_HOMEWORK,
                'name'        => '题目解析',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'stageTestAnalyse'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ANALYSE_STAGE_TEST,
                'name'        => '题目解析',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'checkTestExpound'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPOUND_CHECK_TEST,
                'name'        => '题目讲解',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'previewExpound'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPOUND_PREVIEW,
                'name'        => '题目讲解',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'inClassExpound'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPOUND_IN_CLASS_TEST,
                'name'        => '题目讲解',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'homeworkExpound'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPOUND_HOMEWORK,
                'name'        => '题目讲解',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'stageTestExpound'=> [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPOUND_STAGE_TEST,
                'name'        => '题目讲解',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'submitHomeworkButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBMIT_HOMEWORK,
                'name'        => '催提交巩固练习',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK,
            ],
            'submitHomeworkLikeButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBMIT_HOMEWORK_LIKE,
                'name'        => '催提交相似题',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LIKE,
            ],
            'excellentHomeworkButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXCELLENT_HOMEWORK,
                'name'        => '优秀习作',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '仅限巩固练习中有作文题的章节可用',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXCELLENT_HOMEWORK,
            ],
            'compositionReportButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_COMPOSITION_REPORT,
                'name'        => '作文报告反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '仅限巩固练习中有作文题的章节可用',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT,
            ],
            'reviseHomeworkButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_REVISE_HOMEWORK,
                'name'        => '催订正巩固练习',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK,
            ],
            'reviseHomeworkLikeButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_REVISE_HOMEWORK_LIKE,
                'name'        => '催订正相似题',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_LIKE,
            ],
            'hxPreClassPracticeButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_HX_PRE_CLASS_PRACTICE,
                'name'        => '催课前练习',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HX_PRE_CLASS_PRACTICE,
            ],
            'hxPowerChallengeButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_HX_POWER_CHALLENGE,
                'name'        => '催能力挑战',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HX_POWER_CHALLENGE,
            ],
            'attendOverviewButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ATTEND_OVERVIEW,
                'name'        => '跟课详情反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '下课后开启',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW,
            ],
            'attendOverviewButtonPlayback' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ATTEND_OVERVIEW_PLAYBACK,
                'name'        => '伴学跟课详情反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '下课后开启',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK,
            ],
            'stageSubmit' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_STAGE_SUBMIT,
                'name'        => '催提交阶段测',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_SUBMIT,
            ],
            'stageResultFeedback' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_STAGE_RESULT_FEEDBACK,
                'name'        => '阶段测结果反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK,
            ],
            'stageResultFeedbackSenior' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_STAGE_RESULT_FEEDBACK_SENIOR,
                'name'        => '阶段测结果反馈(高中)',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR,
            ],
            'labelButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_INTERVIEW_LABEL,
                'name'        => '催家访问卷',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_16,
            ],
            'satisfactionButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SATISFACTION_QUE,
                'name'        => '催满意度问卷',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SATISFACTION_QUE,
            ],
            'submitOralQuestionBtn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBMIT_ORAL_QUESTION,
                'name'        => '催口述题',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '下课后开启',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_ORAL_QUESTION,
            ],
            'oralQuestionFeedbackBtn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ORAL_QUESTION_FEEDBACK,
                'name'        => '口述题反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '下课后开启',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ORAL_QUESTION_FEEDBACK,
            ],
            'correctFeedbackButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CORRECT_FEEDBACK,
                'name'        => '批改结果反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK,
            ],
            'correctFeedbackButtonDeer' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CORRECT_FEEDBACK_DEER,
                'name'        => '批改结果反馈（小鹿）',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER,
            ],
            'correctFeedbackLikeButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CORRECT_FEEDBACK_LIKE,
                'name'        => '相似题批改结果反馈',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_LIKE,
            ],
            'dakaButton'      => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_DA_KA,
                'name'        => '催打卡',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DA_KA,
            ],
            'createKsClassButton'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON__TYPE_CREATE_KS_CLASS,
                'name'        => '创建打卡班级',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
            ],
            'syncKsClassButton'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON__TYPE_SYNC_KS_CLASS,
                'name'        => '同步打卡班级',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
            ],
            'monthlyExamBtn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBMIT_MONTHLY_EXAM,
                'name'        => '催月考',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_MONTHLY_EXAM,
            ],
            'qwAddGroup' => [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_QW_ADD_GROUP,
                'name'        => '快速拉群',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_QW_ADD_GROUP,
            ],
            'cuotiben1Btn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CUOTIBEN_JIEXI,
                'name'        => '发送解析版错题本',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI,
            ],
            'cuotiben2Btn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CUOTIBEN_ZUODA,
                'name'        => '发送作答版错题本',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA,
            ],
            'rjylButton'      => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_RJYL,
                'name'        => '催日积月累',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL,
            ],
            'unitRateButton'      => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_UNIT_RATIO_BOARD,
                'name'        => '单元正确率看板',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '',
            ],
            'multiDxCorrect'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_DX_CORRECT,
                'name'        => '批量定向批改',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'multiCancelDxCorrect' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CANCEL_DX_CORRECT,
                'name'        => '取消定向批改',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
            ],
            'mrylPreviewButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_TOPIC_VIEW,
                'name'        => '当周题目预览',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '无法查看',
            ],
            'mrylRightRateButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_TOPIC_RIGHT_RATE,
                'name'        => '当周题目正确率看板',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '无法查看',
            ],
            'mrylSendMessageButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_REMIND_MRYL,
                'name'        => '催每日一练',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '无法发送',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MRYL,
            ],
            'addNewTeacherButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ADD_NEW_TEACHER,
                'name'        => '催加新老师',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '企微用户，并且本学季课程最后一章节结束前14天至结束后30天内可点击。其它情况不可用',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ADD_NEW_TEACHER,
            ],
            'addFriendsQWButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ADD_FRIENDS_QW,
                'name'        => '催个微好友加企微',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '无法发送',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SINGLEFRIEND_ADD_QIWEI,
            ],
            'addWechatButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ADD_WECHAT,
                'name'        => '催加微',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => 0,
            ],
            'joinGroupButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_JOIN_GROUP,
                'name'        => '催入群',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => 0,
            ],
            'exportExcleButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXPORT_EXCLE,
                'name'        => '导出Excle',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '只能导出当前服务项的字段信息',
                'sendType'    => 0,
            ],
            'monthlyExamReportBtn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_MONTHLYEXAM_REPORT,
                'name'        => '反馈阶段测报告',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MONTHLYEXAM_REPORT,
            ],
            'monthlyHighlightBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_HIGHLIGHT,
                'name'        => '群发高光时刻',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT,
            ],
            'introducePostBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_INTRODUCEPOST,
                'name'        => '群发转介绍海报',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_INTRODUCEPOSTER,
            ],
            'recruitPostBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_RECRUITPOST,
                'name'        => '生成招募海报',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => 0,
            ],
            'previewLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PREVIEW_LPC,
                'name'        => '催预习（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW_LPC,
            ],
            'playbackLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PLAYBACK_LPC,
                'name'        => '催回放（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK_LPC,
            ],
            'noteLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_NOTES_LPC,
                'name'        => '群发课堂笔记（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES_LPC,
            ],

            'bottomTestReportLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_BOTTOM_TEST_REPORT,
                'name'        => '群发摸底测报告（lpc）',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT,
            ],
            'bottomTestReportFdBtn' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_BOTTOM_TEST_REPORT_FD,
                'name'        => '群发摸底测报告（辅导）',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT_FD,
            ],

            'bottomTestLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_BOTTOM_TEST,
                'name'        => '催完成摸底测（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST,
            ],

            'bottomTestFudaoBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_BOTTOM_TEST_FUDAO,
                'name'        => '催完成摸底测（辅导）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_FUDAO,
            ],

            'periodExamLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PERIOD_EXAM,
                'name'        => '催完成阶段测（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERIOD_EXAM,
            ],

            'exerciseReportLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EXERCISE_REPORT,
                'name'        => '群发巩固练习报告（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_REPORT,
            ],

            'submitHomeworkLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBMIT_HOMEWORK_LPC,
                'name'        => '催完成巩固练习（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LPC,
            ],
            'reviseHomeworkCommonButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_REVISE_HOMEWORK_COMMON,
                'name'        => '催订正巩固练习(通用)',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_COMMON,
            ],
            'compositionReportCommonButton' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_COMPOSITION_REPORT_COMMON,
                'name'        => '作文报告反馈(通用)',
                'status'      => self::BTN_DISABLED_TRUE,
                'copywriting' => '仅限巩固练习中有作文题的章节可用',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT_COMMON,
            ],
            'addWxLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_ADD_WX_LPC,
                'name'        => '催加微（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ADD_WX_LPC,
            ],

            'serviceAddWxLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SERVICE_ADD_WX_LPC,
                'name'        => '服务期催加微（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SERVICE_ADD_WX_LPC,
            ],

            'highlightLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_HIGHLIGHT_LPC,
                'name'        => '群发高光时刻（lpc）',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT_LPC,
            ],

            'surveyLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SURVEY,
                'name'        => '群发挖需问卷（lpc）',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SURVEY,
            ],

            'classReportLpcBtn'   => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CLASS_REPORT,
                'name'        => '群发课堂报告（lpc）',
                'status'      => self::BTN_HIDDEN,
                'copywriting' => '未配置',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CLASS_REPORT,
            ],

            'deerOrder'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_DEER_ORDER,
                'name'        => '催预约',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_ORDER,
                'copywriting' => '',
            ],
            'deerContinue'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_DEER_CONTINUE,
                'name'        => '催续报',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CONTINUE,
                'copywriting' => '',
            ],
            'classPracticeStatus'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_COMMIT_49,
                'name'        => '催提交堂堂练',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_49,
                'copywriting' => '',
            ],
            'caseReflectionStatus'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_COMMIT_50,
                'name'        => '催提交举一反三',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_50,
                'copywriting' => '',
            ],
            'powerUpsStatus'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_COMMIT_51,
                'name'        => '催提交能力提升',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_51,
                'copywriting' => '',
            ],
            'weakSpecialStatus'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_COMMIT_52,
                'name'        => '催提交薄弱专项',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_52,
                'copywriting' => '',
            ],
            'classPracticeStatus_Re'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_RE_COMMIT_49,
                'name'        => '催订正交堂堂练',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_49,
                'copywriting' => '',
            ],
            'caseReflectionStatus_Re'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_RE_COMMIT_50,
                'name'        => '催订正举一反三',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_50,
                'copywriting' => '',
            ],
            'powerUpsStatus_Re'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_RE_COMMIT_51,
                'name'        => '催订正能力提升',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_51,
                'copywriting' => '',
            ],
            'weakSpecialStatus_Re'  =>  [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_GUANJIA_RE_COMMIT_52,
                'name'        => '催订正薄弱专项',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_52,
                'copywriting' => '',
            ],
            'lpcAddScore'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_ADD_SCORE,
                'name'        => '加学分工具',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
//            'lpcFollow'  =>  [
//                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_FOLLOW,
//                'name'        => '主讲关注学员',
//                'status'      => self::BTN_DISABLED_FALSE,
//                'sendType'    => 0,
//                'copywriting' => '',
//            ],
            'lpcDownClassNote'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_DOWN_CLASS_NOTE,
                'name'        => '下载课堂笔记',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'lpcCopyPlaybackLink'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_COPY_PLAYBACK_LINK,
                'name'        => '复制回放链接',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'lpcReviewImage'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_REVIEW_IMAGE,
                'name'        => '预览知识打点图',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'guanJiaExportAnalyze'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_GUANJIA_EXPORT_ANALYZE,
                'name'        => '诊断结果导出',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'lpcSmsAdd_wx'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_SMS_ADD_WX,
                'name'        => '发送用户加微短信',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX,
                'copywriting' => '',
            ],
            'lpcSmsAdd_wx_apply'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_SMS_ADD_WX_APPLY,
                'name'        => '发送加用户微信申请',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_APPLY,
                'copywriting' => '',
            ],
            'lpcSmsAdd_wx_card'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_SMS_ADD_WX_CARD,
                'name'        => '发送用户名片给我',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_CARD,
                'copywriting' => '',
            ],
            'lpcSmsAttend'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_SMS_ATTEND,
                'name'        => '催到课短信',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND,
                'copywriting' => '',
            ],
            'lpcRsyncContact'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_RSYNC_CONTACT,
                'name'        => 'LPC同步通讯录',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RSYNC_CONTACT,
                'copywriting' => '',
            ],
            'introBindingNew'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_INTRO_BINDING_NEW,
                'name'        => '录入转介绍新用户',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'introPoster'  =>  [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_INTRO_POSTER,
                'name'        => '生成老用户转介绍海报',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'fnDemandSurvey'          => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_FN_DEMAND_SURVEY,
                'name'        => '蜂鸟挖需问卷',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],

            "fdLearnReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LEARN_REPORT_FD,
                'name'        => '群发课堂报告(辅导)',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_FD,
                'copywriting' => '',
            ],
            "unitReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_UNIT_REPORT_DYD,
                'name'        => '群发单元报告(大阅读)',
                'status'      => self::BTN_HIDDEN,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_UNIT_REPORT_DYD,
                'copywriting' => '',
            ],
            "sportWeekReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SPORT_WEEK_REPORT,
                'name'        => '群发运动周报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT,
                'copywriting' => '',
            ],
            "semesterReportSenior" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SEMESTER_REPORT_SENIOR,
                'name'        => '学期报告反馈（高中）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_SEMESTER_REPORT_SENIOR,
                'copywriting' => '',
            ],
            "lessonReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LESSON_REPORT,
                'name'        => '群发章节报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LESSON_REPORT,
                'copywriting' => '',
            ],
            "courseReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_COURSE_REPORT,
                'name'        => '群发课堂报告（小鹿）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_REPORT,
                'copywriting' => '',
            ],
            "weeklyReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_WEEKLY_REPORT,
                'name'        => '群发周报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_WEEKLY_REPORT,
                'copywriting' => '',
            ],
            "overclass" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CONTRACT_AI_OVER_CLASS,
                'name'        => 'AI课催完课',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_AI_OVER_CLASS,
                'copywriting' => '',
            ],
            "remedialClass" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CONTRACT_REMEDIAL_CLASS,
                'name'        => '催补课',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_REMEDIAL_CLASS,
                'copywriting' => '',
            ],
            "lpcAiAttend" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LPC_SMS_AI_ATTED,
                'name'        => 'AI课催到课',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_AI_ATTED,
                'copywriting' => '',
            ],
            "learningReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_LEARNING_REPORT,
                'name'        => '发起学情测评（高中）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARNING_REPORT,
                'copywriting' => '',
            ],
            "subjectDiagTest" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBJECT_DIAG,
                'name'        => '群发学科测评（初中）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG,
                'copywriting' => '',
            ],
            "probeReportQuery" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PROBE_REPORT_QUERY,
                'name'        => '学情报告查询',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PROBE_REPORT_QUERY,
                'copywriting' => '高中学情报告查询',
            ],
            "probeReportSend" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SEND_TYPE_EDU_PROBE_PDF,
                'name'        => '群发学情报告（高中）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EDU_PROBE_PDF,
                'copywriting' => '',
            ],
            "subjectDiagReport" => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SUBJECT_DIAG_REPORT,
                'name'        => '群发学科诊断报告（初中）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG_REPORT,
                'copywriting' => '',
            ],
            'personalLearnFeedback' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PERSONAL_LEARN_FEEDBACK,
                'name'        => '个性化学情反馈',
                'status'      => self::BTN_HIDDEN,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK,
                'copywriting' => '',
            ],
            'correctReport' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CORRECT_REPORT,
                'name'        => '群发章节报告（人工批改）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::sEND_TYPE_CORRECT_REPORT,
                'copywriting' => '',
            ],
            'evaluate' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_EVALUATE,
                'name'        => '发起调研问卷',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EVALUATE,
                'copywriting' => '',
            ],
            'courseTimeTable' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_COURSE_TIME_TABLE,
                'name'        => '群发课表',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_TIME_TABLE,
                'copywriting' => '',
            ],
            'deerProgramReport' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_DEER_PROGRAM_REPORT,
                'name'        => '群发课堂报告（编程通用）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT,
                'copywriting' => '',
            ],
            'deerProgramReportSchool' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_DEER_PROGRAM_REPORT_YOUKETANG,
                'name'        => '群发课堂报告（进校app）',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG,
                'copywriting' => '',
            ],
            'stageResultFeedbackCard' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_STAGE_RESULT_FEEDBACK_CARD,
                'name'        => '阶段测结果反馈（辅导）',
                'status'      => self::BTN_DISABLED_TRUE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD,
                'copywriting' => '',
            ],
            'sendRjylReport' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_RJYL_REPORT,
                'name'        => '群发日积月累报告',
                'status'      => self::BTN_HIDDEN,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL_REPORT,
                'copywriting' => '日积月累报告',
            ],
            'sendExerciseNoteTask' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SEND_EXERCISE_NOTE_TASK,
                'name'        => '发送错题任务',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK,
                'copywriting' => '日积月累报告',
            ],
            'sendCleanTask' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SEND_EXERCISE_NOTE_CLEAN_REPORT,
                'name'        => '发送错清报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT,
                'copywriting' => '日积月累报告',
            ],
            'classMistake' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_CLASS_MISTAKE_TI,
                'name'        => '班级易错题',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => 0,
                'copywriting' => '',
            ],
            'batchCreateAssistantCourse' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_SEND_ASSISTANT_COURSE_QUICK_TASK,
                'name'        => '批量创建小灶课',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendType'    => AssistantDesk_ArkConfig::BUTTON_TYPE_SEND_ASSISTANT_COURSE_QUICK_TASK,
                'copywriting' => '',
            ],
            'pronunciationReport' => [
                'val'         => AssistantDesk_ArkConfig::BUTTON_TYPE_PRONUNCIATION_REPORT,
                'name'        => '纠音报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '该章节未绑定纠音报告',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT,
            ],
            "cambridgeEnglishLearnReport" => [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH,
                'name'        => '群发课堂报告(剑桥英语)',
                'status'      => self::BTN_DISABLED_FALSE,
                'sendSms'     => true,
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH,
                'copywriting' => '',
            ],
            'pronunciationGroupReport' => [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_PRONUNCIATION_REPORT_GROUP,
                'name'        => '个性化纠音报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '该章节未绑定个性化纠音',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP,
            ],
            'voiceGroupReport' => [
                'val'         => AssistantDesk_ArkConfig::SEND_TYPE_VOICE_REPORT_GROUP,
                'name'        => '配音小达人报告',
                'status'      => self::BTN_DISABLED_FALSE,
                'copywriting' => '该章节未绑定配音小达人报告',
                'sendType'    => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP,
            ],
        ];

        //默认返回值
        $result = ['buttonList' => array_values($buttonList)];

        if (0 >= $assistantUid || 0 >= $courseId || 0 >= $lessonId) {
            Bd_Log::warning('参数错误');
            return $result;
        }

        $courseLessonInfo = Api_Dal::getCourseLessonInfoByCourseIds($courseId);
        if (false === $courseLessonInfo) {
            Bd_Log::warning('dal章节信息获取失败');
            return $result;
        }
        // 章节基础数据
        $lessonList = $courseLessonInfo['lessonList'];
        $lessonList = Tools_Array::sortByMultiCols($lessonList, ['startTime' => SORT_ASC]);
        $lessonList = array_values($lessonList);
        $lastLessonInfo = [];
        $firstLessonInfo = [];
        foreach ($lessonList as  $k => $lessonInfo) {
            $lastLessonInfo = $lessonInfo;
            !$firstLessonInfo && $firstLessonInfo = $lessonInfo;
            if ($lessonId == $lessonInfo['lessonId']) {
                $i = $k - 1;
                if($i >= 0) $prevLessonInfo = $lessonList[$i];
                $currentLessonInfo = $lessonInfo;
            }
        }

        // 当前时间
        $currentTime = time();

        // 章节预习数据 根据课程所属阶段 1 小学 /20 初中 /30 高中
        $department = Zb_Const_GradeSubject::$GRADEMAPXB[$courseLessonInfo['mainGradeId']];
        $prevIsOpen = 0;
        switch ($department) {
            case Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL : //新增低幼学部
            case Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY :
                // 小学预习情况
                $bindKey  = 'lesson_'.$lessonId.":".Api_Exam::BIND_TYPE_PREVIEW;
                $bindList = [$bindKey];
                $examRelationList = Api_Examcore::getRelation($bindList);
                $examRelationList = $examRelationList[$bindKey];
                $examIds = is_array($examRelationList) ? array_unique(array_column($examRelationList, 'examId')) : [];

                if(intval($examIds[0])){
                    $prevIsOpen = $currentTime > (strtotime(date("Y-m-d", $currentLessonInfo['startTime'])) - (7 * 24 * 3600)) ? 1 : 0;
                }
                break;
            case Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR || Zb_Const_GradeSubject::GRADE_STAGE_SENIOR :
                // 初中预习情况 | 高中预习情况
                $apiLessonPreviewInfo = Api_Jx::getHighGradePreviewInfo([$lessonId]);
                $prevIsOpen           = $apiLessonPreviewInfo[$lessonId]['isOpen'];
                break;
        }

        //获取章节巩固练习绑定情况
        $hwBindExams = AssistantDesk_ExamBind::lessonBindExams(
            [$lessonId],
            [
                Api_Exam::BIND_TYPE_HOMEWORK,
                Api_Exam::BIND_TYPE_TEST_IN_CLASS,
                Api_Exam::BIND_TYPE_STAGE,
                Api_Exam::BIND_TYPE_EXAM_TYPE_PRONUNCIATION,
                API_EXAM::BIND_TYPE_EXAM_TYPE_VOICE,
            ]
        );
        $isBindHw = !empty($hwBindExams[$lessonId][Api_Exam::BIND_TYPE_HOMEWORK]);
        // 阶段测是否绑定
        $isBindStage = !empty($hwBindExams[$lessonId][Api_Exam::BIND_TYPE_STAGE]);
        $isBindPronunciation = !empty($hwBindExams[$lessonId][Api_Exam::BIND_TYPE_EXAM_TYPE_PRONUNCIATION]);
        $isBindVoice = !empty($hwBindExams[$lessonId][Api_Exam::BIND_TYPE_EXAM_TYPE_VOICE]);

        if ($isBindStage) {
            $buttonList['stageResultFeedback']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['stageResultFeedbackCard']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['stageResultFeedbackSenior']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['stageSubmit']['status']         = self::BTN_DISABLED_FALSE;
        } else {
            $buttonList['stageResultFeedback']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['stageResultFeedbackCard']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['stageSubmit']['status']              = self::BTN_DISABLED_TRUE;
            $buttonList['stageResultFeedback']['copywriting'] = '绑定阶段测后才可执行操作';
            $buttonList['stageResultFeedbackCard']['copywriting'] = '绑定阶段测后才可执行操作';
            $buttonList['stageSubmit']['copywriting']         = '绑定阶段测后才可执行操作';
            $buttonList['stageResultFeedbackSenior']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['stageResultFeedbackSenior']['copywriting'] = '绑定阶段测后才可执行操作';
        }

        if ($isBindPronunciation) {
            $buttonList['pronunciationReport']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['pronunciationGroupReport']['status'] = self::BTN_DISABLED_FALSE;
        }else{
            $buttonList['pronunciationReport']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['pronunciationGroupReport']['status'] = self::BTN_DISABLED_TRUE;
        }
        if ($isBindVoice) {
            $buttonList['voiceGroupReport']['status'] = self::BTN_DISABLED_FALSE;
        }else{
            $buttonList['voiceGroupReport']['status'] = self::BTN_DISABLED_TRUE;
        }

        $keyQuick = 'assistantcoursego_quick_create_gray_config';
        $keySmall = 'assistantcoursego_quick_create_small_course_gray_config';

        if (Api_Assistantdeskgo_Api::grayHit($personUid,$keyQuick) && Api_Assistantdeskgo_Api::grayHit($personUid,$keySmall)) {
            $buttonList['batchCreateAssistantCourse']['status'] = self::BTN_DISABLED_FALSE;
        }else{
            $buttonList['batchCreateAssistantCourse']['status'] = self::BTN_DISABLED_TRUE;
        }

        //是否有摸底测
        $cpuId = $courseLessonInfo['cpuId'];
        if ($cpuId > 0) {
            $cpuExamBind = AssistantDesk_ExamBind::cpuBindExams([$cpuId], Api_Exam::BIND_TYPE_SURVEY);
            $examId = 0;
            if (isset($cpuExamBind[$cpuId]) && is_array($cpuExamBind[$cpuId])) {
                $examIds = array_keys($cpuExamBind[$cpuId]);
                $examId = intval(end($examIds));
            }
            if ($examId) {
                if ($currentTime > $courseLessonInfo['lastLessonStopTime']) {
                    // 当期课程绑定摸底但已完课：功能置灰
                    $buttonList['bottomTestFudaoBtn']['status']      = self::BTN_DISABLED_TRUE;
                    $buttonList['bottomTestFudaoBtn']['copywriting'] = "当前课程已完课";
                } else {
                    // 当前课程绑定摸底测且未完课：支持点击
                    $buttonList['bottomTestFudaoBtn']['status']      = self::BTN_DISABLED_FALSE;
                    $buttonList['bottomTestFudaoBtn']['copywriting'] = "";
                }
            } else {
                // 当前课程未绑定摸底测：功能置灰
                $buttonList['bottomTestFudaoBtn']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['bottomTestFudaoBtn']['copywriting'] = "当前课程未绑定摸底测";
            }
        } else {
            $buttonList['bottomTestFudaoBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['bottomTestFudaoBtn']['copywriting'] = "课程cpuId信息获取失败，当前课程可能未绑定摸底测";
        }

        // 催预习按钮
        if ($prevIsOpen) {
            $buttonList['prevButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['prevButton']['copywriting'] = "点击可一键发送催预习信息到学员微信";
        } else {
            $buttonList['prevButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['prevButton']['copywriting'] = "预习开启后才可执行操作";
        }
        if ($currentTime < $currentLessonInfo['startTime']) {
            $buttonList['previewLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['previewLpcBtn']['copywriting'] = "点击可一键发送催预习信息到学员微信";
        } else {
            $buttonList['previewLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['previewLpcBtn']['copywriting'] = "催预习仅章节开课前可以发送";
        }
        if ($currentTime > $currentLessonInfo['stopTime']) {
            $buttonList['playbackLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['playbackLpcBtn']['copywriting'] = "";
            $buttonList['periodExamLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['periodExamLpcBtn']['copywriting'] = "";
            $buttonList['exerciseReportLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['exerciseReportLpcBtn']['copywriting'] = "";
            $buttonList['submitHomeworkLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['submitHomeworkLpcBtn']['copywriting'] = "";
            $buttonList['classReportLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['classReportLpcBtn']['copywriting'] = "";
        } else {
            $buttonList['playbackLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['playbackLpcBtn']['copywriting'] = "催回放仅章节结束后可发送";
            $buttonList['periodExamLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['periodExamLpcBtn']['copywriting'] = "催完成阶段测仅章节结束后可以发送";
            $buttonList['exerciseReportLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['exerciseReportLpcBtn']['copywriting'] = "章节课程结束后,可群发作文报告";
            $buttonList['submitHomeworkLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['submitHomeworkLpcBtn']['copywriting'] = "该章节课程结束后,可群发催提交巩固练习";
            $buttonList['classReportLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['classReportLpcBtn']['copywriting'] = "该章节课程结束后,可群发课堂报告";
        }
        if ($currentTime > $firstLessonInfo['startTime']) {
            $buttonList['bottomTestLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['bottomTestLpcBtn']['copywriting'] = "首章节开课后,不可催完成摸底测";
        } else {
            $buttonList['bottomTestLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['bottomTestLpcBtn']['copywriting'] = "";
        }


        // 一键预到课按钮
        if ($currentTime <= $prevLessonInfo['stopTime']) {
            $buttonList['preAttendButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['preAttendButton']['copywriting'] = "上一章节下课后才可执行预到课操作";
        } elseif ($prevLessonInfo['stopTime'] > $currentTime && $currentTime < $currentLessonInfo['startTime']) {
            $buttonList['preAttendButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['preAttendButton']['copywriting'] = "点击可一键发送预到课询问到学员微信";
        } elseif($currentLessonInfo['startTime'] < $currentTime) {
            $buttonList['preAttendButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['preAttendButton']['copywriting'] = "该章节已开课，无法进行预到课";
        }

        // 小学和高中；由开课前提前30分钟调整为提前1小时 https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=312278361
        $pointBeforeStartTime = intval($currentLessonInfo['startTime']) - 36 * 60 * 60; // 开课前30分钟
        $copyWriting = '开课前36小时才可执行催到课操作';
        if (in_array($department, [
            Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY,
            Zb_Const_GradeSubject::GRADE_STAGE_SENIOR,
        ])) {
            $pointBeforeStartTime = intval($currentLessonInfo['startTime']) - 36 * 60 * 60; // 开课前60分钟
            $copyWriting = '开课前36小时才可执行催到课操作';
        }

        // 催到课按钮
        if ($currentTime < $pointBeforeStartTime) {
            $buttonList['attendButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['attendButton']['copywriting'] = $copyWriting;
        } else if ($currentLessonInfo['stopTime'] < $currentTime) {
            $buttonList['attendButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['attendButton']['copywriting'] = "该章节已开课，无法进行催到课";
        } elseif ($pointBeforeStartTime <= $currentTime && $currentTime <= $currentLessonInfo['stopTime']) {
            $buttonList['attendButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['attendButton']['copywriting'] = "点击可一键发送催到课信息到学员到学员微信";
        }
        // 催到课按钮
        if ($currentTime < $pointBeforeStartTime) {
            $buttonList['attendButtonGuanjia']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['attendButtonGuanjia']['copywriting'] = $copyWriting;
        } elseif ($currentLessonInfo['stopTime'] < $currentTime) {
            $buttonList['attendButtonGuanjia']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['attendButtonGuanjia']['copywriting'] = "该章节已开课，无法进行催到课";
        } elseif ($pointBeforeStartTime <= $currentTime && $currentTime <= $currentLessonInfo['stopTime']) {
            $buttonList['attendButtonGuanjia']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['attendButtonGuanjia']['copywriting'] = "点击可一键发送催到课信息到学员到学员微信";
        }

        //崔巩固练习按钮
        if (!$isBindHw) {
            $buttonList['homeworkButton']['status']       = self::BTN_DISABLED_TRUE;
            $buttonList['submitHomeworkButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['submitHomeworkLikeButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['reviseHomeworkButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['reviseHomeworkCommonButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['reviseHomeworkLikeButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['correctFeedbackButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['correctFeedbackLikeButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['compositionReportButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['compositionReportCommonButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['excellentHomeworkButton']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['homeworkButton']['copywriting']       = "章节巩固练习未开启/未配置，无法催巩固练习";
            $buttonList['submitHomeworkButton']['copywriting'] = "章节巩固练习未开启/未配置，无法催提交巩固练习";
            $buttonList['submitHomeworkLikeButton']['copywriting'] = "章节巩固练习未开启/未配置，无法催提交相似题";
            $buttonList['reviseHomeworkButton']['copywriting'] = "章节巩固练习未开启/未配置，无法催订正巩固练习";
            $buttonList['reviseHomeworkCommonButton']['copywriting'] = "章节巩固练习未开启/未配置，无法催订正巩固练习";
            $buttonList['reviseHomeworkLikeButton']['copywriting'] = "章节巩固练习未开启/未配置，无法催订正相似题";
            $buttonList['correctFeedbackButton']['copywriting'] = "该章节未配置巩固练习";
            $buttonList['correctFeedbackLikeButton']['copywriting'] = "该章节未配置巩固练习";
            $buttonList['compositionReportButton']['copywriting'] = "该章节未配置巩固练习";
            $buttonList['compositionReportCommonButton']['copywriting'] = "该章节未配置巩固练习";
            $buttonList['excellentHomeworkButton']['copywriting'] = "该章节未配置巩固练习";
        } else {
            if ($currentTime <= $currentLessonInfo['stopTime']) {
                $buttonList['homeworkButton']['status']            = self::BTN_DISABLED_TRUE;
                $buttonList['submitHomeworkButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['submitHomeworkLikeButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkCommonButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkLikeButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['correctFeedbackButton']['status']     = self::BTN_DISABLED_TRUE;
                $buttonList['correctFeedbackLikeButton']['status']     = self::BTN_DISABLED_TRUE;
                $buttonList['compositionReportButton']['status']     = self::BTN_DISABLED_TRUE;
                $buttonList['compositionReportCommonButton']['status']     = self::BTN_DISABLED_TRUE;
                $buttonList['excellentHomeworkButton']['status']     = self::BTN_DISABLED_TRUE;
                $buttonList['homeworkButton']['copywriting']       = "章节巩固练习未开启，无法催巩固练习";
                $buttonList['submitHomeworkButton']['copywriting'] = "章节巩固练习未开启，无法催提交巩固练习";
                $buttonList['submitHomeworkLikeButton']['copywriting'] = "章节巩固练习未开启，无法催提交相似题";
                $buttonList['reviseHomeworkButton']['copywriting'] = "章节巩固练习未开启，无法催订正巩固练习";
                $buttonList['reviseHomeworkCommonButton']['copywriting'] = "章节巩固练习未开启，无法催订正巩固练习";
                $buttonList['reviseHomeworkLikeButton']['copywriting'] = "章节巩固练习未开启，无法催订正相似题";
                $buttonList['correctFeedbackButton']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
                $buttonList['correctFeedbackLikeButton']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
                $buttonList['compositionReportButton']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
                $buttonList['compositionReportCommonButton']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
                $buttonList['excellentHomeworkButton']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
            } elseif (($currentTime - $currentLessonInfo['stopTime']) > ( 14 * 24 * 3600)) {
                $buttonList['homeworkButton']['status']            = self::BTN_DISABLED_TRUE;
                $buttonList['submitHomeworkButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['submitHomeworkLikeButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkCommonButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['reviseHomeworkLikeButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['homeworkButton']['copywriting']       = "课程已经结课超过14天，无法催巩固练习";
                $buttonList['submitHomeworkButton']['copywriting'] = "课程已经结课超过14天，无法催提交巩固练习";
                $buttonList['submitHomeworkLikeButton']['copywriting'] = "课程已经结课超过14天，无法催提交相似题";
                $buttonList['reviseHomeworkButton']['copywriting'] = "课程已经结课超过14天，无法催订正巩固练习";
                $buttonList['reviseHomeworkCommonButton']['copywriting'] = "课程已经结课超过14天，无法催订正巩固练习";
                $buttonList['reviseHomeworkLikeButton']['copywriting'] = "课程已经结课超过14天，无法催订正相似题";
            } else {
                $buttonList['homeworkButton']['status']            = self::BTN_DISABLED_FALSE;
                $buttonList['submitHomeworkButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['submitHomeworkLikeButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['reviseHomeworkButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['reviseHomeworkCommonButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['reviseHomeworkLikeButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['homeworkButton']['copywriting']       = "点击可一键发送催巩固练习到学员微信";
                $buttonList['submitHomeworkButton']['copywriting'] = "点击可一键发送催提交巩固练习到学员微信";
                $buttonList['submitHomeworkLikeButton']['copywriting'] = "点击可一键发送催提交相似题到学员微信";
                $buttonList['reviseHomeworkButton']['copywriting'] = "点击可一键发送催订正巩固练习到学员微信";
                $buttonList['reviseHomeworkCommonButton']['copywriting'] = "点击可一键发送催订正巩固练习到学员微信";
                $buttonList['reviseHomeworkLikeButton']['copywriting'] = "点击可一键发送催订正相似题到学员微信";
            }
        }

        // 催回放按钮 课堂笔记按钮
        if ($currentTime <= $currentLessonInfo['stopTime']) {
            $buttonList['playbackButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['playbackButton']['copywriting'] = "章节未下课，无法催回放";

            $buttonList['notesButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['notesButton']['copywriting'] = "章节未下课，无法发送课堂笔记";
            $buttonList['noteLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['noteLpcBtn']['copywriting'] = "章节未下课，无法发送课堂笔记";
            $buttonList['lpcDownClassNote']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['lpcDownClassNote']['copywriting'] = "章节未下课，无法下载课堂笔记";
        } else {
            $buttonList['playbackButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['playbackButton']['copywriting'] = "点击可一键发送催回放到学员微信";


            // 课堂笔记为空时
            if (empty($currentLessonInfo['classNoteUri'])) {
                $buttonList['notesButton']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['notesButton']['copywriting'] = "当前章节课堂笔记不存在，请联系讲师上传";
                $buttonList['noteLpcBtn']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['noteLpcBtn']['copywriting'] = "当前章节课堂笔记不存在，请联系讲师上传";
                $buttonList['lpcDownClassNote']['status']      = self::BTN_DISABLED_TRUE;
                $buttonList['lpcDownClassNote']['copywriting'] = "当前章节课堂笔记不存在，请联系讲师上传";
            } else {
                $buttonList['notesButton']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['notesButton']['copywriting'] = "点击可一键发送课堂笔记到学员微信";
                $buttonList['noteLpcBtn']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['noteLpcBtn']['copywriting'] = "点击可一键发送课堂笔记到学员微信";
                $buttonList['lpcDownClassNote']['status']      = self::BTN_DISABLED_FALSE;
                $buttonList['lpcDownClassNote']['copywriting'] = "";
            }
        }

        // 预习群发点评
        if ($prevIsOpen) {
            $buttonList['previewGroupSend']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['previewGroupSend']['copywriting'] = "点击可按照预习结果分组发送";
        } else {
            $buttonList['previewGroupSend']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['previewGroupSend']['copywriting'] = "预习开启后才可执行操作";
        }

        // 预习解析
        if ($prevIsOpen) {
            $buttonList['previewAnalysis']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['previewAnalysis']['copywriting'] = "点击可查看预习解析";
        } else {
            $buttonList['previewAnalysis']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['previewAnalysis']['copywriting'] = "预习开启后才可执行操作";
        }

        // 浣熊相关
        $mixedRet = Api_Hx::getLessonPracticeConf([$lessonId]);
        if (!empty($mixedRet)) {
            if (isset($mixedRet[$lessonId])) {
                if (1 === $mixedRet[$lessonId]['before']['config']) {
                    if (1 === $mixedRet[$lessonId]['before']['do']) {
                        $buttonList['hxPreClassPracticeButton']['status'] = self::BTN_DISABLED_FALSE;
                    } else {
                        $buttonList['hxPreClassPracticeButton']['copywriting'] = '该章节未开启课前练习';
                    }
                } else {
                    $buttonList['hxPreClassPracticeButton']['copywriting'] = '该章节未配置课前练习';
                }

                if (1 === $mixedRet[$lessonId]['after']['config']) {
                    if (1 === $mixedRet[$lessonId]['after']['do']) {
                        $buttonList['hxPowerChallengeButton']['status'] = self::BTN_DISABLED_FALSE;
                    } else {
                        $buttonList['hxPowerChallengeButton']['copywriting'] = '该章节未开启能力挑战';
                    }
                } else {
                    $buttonList['hxPowerChallengeButton']['copywriting'] = '该章节未配置能力挑战';
                }
            }
        }

        // 口述题
        $hasOralQuestion = AssistantDesk_Tools::isOralQuestionCourse([$lessonId]);
        if (!$hasOralQuestion) {
            $buttonList['submitOralQuestionBtn']['copywriting'] = '该章节未配置口述题';
            $buttonList['oralQuestionFeedbackBtn']['copywriting'] = '该章节未配置口述题';
        }

        // 跟课详情群发
        if ($currentTime > $currentLessonInfo['stopTime']) {
            $buttonList['attendOverviewButton']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['attendOverviewButton']['copywriting'] = '';
            $buttonList['attendOverviewButtonPlayback']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['attendOverviewButtonPlayback']['copywriting'] = '';

            if ($hasOralQuestion) {
                $buttonList['submitOralQuestionBtn']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['submitOralQuestionBtn']['copywriting'] = '';
                $buttonList['oralQuestionFeedbackBtn']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['oralQuestionFeedbackBtn']['copywriting'] = '';
            }
        }

        //批改结果反馈
        if ($isBindHw && $currentTime > $currentLessonInfo['stopTime']) {
            $hasSubjectiveQuestion = $this->hasHomeworkSubjectiveQuestion($lessonId, Api_Exam::BIND_TYPE_HOMEWORK);
            if ($hasSubjectiveQuestion) {
                $buttonList['correctFeedbackButton']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['correctFeedbackButton']['copywriting'] = '';
                $buttonList['correctFeedbackLikeButton']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['correctFeedbackLikeButton']['copywriting'] = '';
            } else {
                $buttonList['correctFeedbackButton']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['correctFeedbackButton']['copywriting'] = '本章节暂无主观题';
                $buttonList['correctFeedbackLikeButton']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['correctFeedbackLikeButton']['copywriting'] = '本章节暂无主观题';
            }
        }

        //作文报告反馈、优秀习作 - 章节巩固练习有作文题 - 并且已经下课
        if ($isBindHw && $currentTime > $currentLessonInfo['stopTime']) {
            $lessonCompositionConf = Api_PcAssistant::lessonCompositionConf([$lessonId]);
            if (is_array($lessonCompositionConf[$lessonId]['tidList']) && !empty($lessonCompositionConf[$lessonId]['tidList'])) {
                $buttonList['compositionReportButton']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['compositionReportButton']['copywriting'] = '';
                $buttonList['excellentHomeworkButton']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['excellentHomeworkButton']['copywriting'] = '';
                $buttonList['compositionReportCommonButton']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['compositionReportCommonButton']['copywriting'] = '';
            }
        }

        // 创建打卡班级按钮
        $isCreated = $this->getCreateKsClassButtonStatus($assistantUid, $courseId);
        if ($isCreated) {
            $buttonList['createKsClassButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['createKsClassButton']['copywriting'] = '打卡班级已创建';
            $buttonList['syncKsClassButton']['status']        = self::BTN_DISABLED_FALSE;
            $buttonList['syncKsClassButton']['copywriting']   = '点击可同步打卡班级';
            $buttonList['dakaButton']['status']        = self::BTN_DISABLED_FALSE;
            $buttonList['dakaButton']['copywriting']   = '催打卡';
        } else {
            $buttonList['createKsClassButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['createKsClassButton']['copywriting'] = '点击可创建打卡班级';
            $buttonList['syncKsClassButton']['status']        = self::BTN_DISABLED_TRUE;
            $buttonList['syncKsClassButton']['copywriting']   = '点击可同步打卡班级';
        }

        //群发单元报告
        $lessonInfo = Api_Dal::getLessonBaseByLessonIds($currentLessonInfo["lessonId"]);
        $serviceIds = array_column($lessonInfo["serviceInfo"], 'serviceId');
        if ( in_array(Api_Dal::DYD_SERVICE_ID, $serviceIds) ) {
            if ($currentTime <= $currentLessonInfo['stopTime']) {
                $buttonList['unitReport']['status']        = self::BTN_DISABLED_TRUE;
                $buttonList['unitReport']['copywriting'] = "该章节未下课，请等待课程下课后再尝试";
            } else {
                $buttonList['unitReport']['status']        = self::BTN_DISABLED_FALSE;
                $buttonList['unitReport']['copywriting']   = '点击发送单元报告';
            }
        }
            // 日积月累按钮配置
        $unitList = Common_Singleton::getInstanceData('AssistantDesk_Rjyl', 'getUnitListByCourseId', [$courseId]);
        if ($unitList) {
            $buttonList['rjylButton']['status']          = self::BTN_DISABLED_FALSE;
            $buttonList['rjylButton']['copywriting']     = '点击可催未完成全部任务的学生';
            $buttonList['unitRateButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['unitRateButton']['copywriting'] = '点击可查看相关题目错误率';
        }else{
            $buttonList['rjylButton']['status']          = self::BTN_DISABLED_TRUE;
            $buttonList['rjylButton']['copywriting']     = '无法查看';
            $buttonList['unitRateButton']['status']      = self::BTN_DISABLED_TRUE;
            $buttonList['unitRateButton']['copywriting'] = '无法查看';
        }

        // 群发日积月累报告，控制按钮是否可用
        if (!empty($stageId) && !empty($unitList)) {
            $unitId = Common_Singleton::getInstanceData(AssistantDesk_Rjyl::class, 'getUnitIdByStageId', [$unitList,$stageId]);
            $rjylReportEnable = \AssistantDesk_Rjyl::isCourseUnitRjylReportEnable($courseId, $unitId);
            if ($rjylReportEnable) {
                $buttonList['sendRjylReport']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['sendRjylReport']['copywriting'] = '群发日积月累报告';
            } else {
                $buttonList['sendRjylReport']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['sendRjylReport']['copywriting'] = '日积月累报告当前不可用';
            }
        }


        // 每日一练按钮配置
        $weekList = Common_Singleton::getInstanceData('AssistantDesk_Rjyl', 'getDailyListByCourseId', [$courseId]);
        if ($stageId && $weekList) {
            foreach ($weekList as $weekInfo){
                $dayList = $weekInfo['dayList'] ?: [];
                foreach ($dayList as $dayInfo){
                    if($dayInfo['stageId'] == $stageId && $dayInfo['isHas']){
                        $buttonList['mrylPreviewButton']['status']      = self::BTN_DISABLED_FALSE;
                        $buttonList['mrylPreviewButton']['copywriting'] = '点击查看当周题目预览';
                    }
                    if($dayInfo['stageId'] == $stageId && $dayInfo['isOpen']){
                        $buttonList['mrylRightRateButton']['status']          = self::BTN_DISABLED_FALSE;
                        $buttonList['mrylRightRateButton']['copywriting']     = '点击查看当周正确率看板';
                        $buttonList['mrylSendMessageButton']['status']          = self::BTN_DISABLED_FALSE;
                        $buttonList['mrylSendMessageButton']['copywriting']     = '点击可催未完成全部任务的学生';
                    }
                }
            }
        }

        // 小英月考
        $mixedRet = Api_Hx::getMonthlyExamConfig($courseId);
        if ($mixedRet && $mixedRet['monthlytest'][$courseId]) {
            $buttonList['monthlyExamBtn']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['monthlyExamBtn']['copywriting'] = '';
        }
        //催个微加企微好友   //当前班主任为企微时可使用该按钮
        if(AssistantDesk_KunpengEnterprise::isEnterpriseWxType($assistantUid)){
            $buttonList['addFriendsQWButton']['status'] = self::BTN_DISABLED_FALSE;
            $buttonList['addFriendsQWButton']['copywriting'] = '';
        }

        // 催加新老师按钮展示逻辑[企微用户，并且本学季课程最后一章节结束前14天至结束后30天内可点击。其它情况不可用]
        if (
            AssistantDesk_KunpengEnterprise::isEnterpriseWxType($assistantUid)
            && (intval($lastLessonInfo['stopTime']) - (14 * 86400)) < $currentTime
            && $currentTime < (intval($lastLessonInfo['stopTime']) + (30 * 86400))
        ) {
            $buttonList['addNewTeacherButton']['status']      = self::BTN_DISABLED_FALSE;
            $buttonList['addNewTeacherButton']['copywriting'] = '';
        }

        // 月考报告
        if (Service_Data_MonthlyExamReport::getMonthlyExamReportLesson($courseId, [$lessonId], $courseLessonInfo, $hwBindExams)) {
            if ($currentTime <= $prevLessonInfo['stopTime']) {
                $buttonList['monthlyExamReportBtn']['copywriting'] = '下课后开启';
            } else {
                $buttonList['monthlyExamReportBtn']['status'] = self::BTN_DISABLED_FALSE;
                $buttonList['monthlyExamReportBtn']['copywriting'] = '点击可一键发送月考报告到学员微信';
            }
        } else {
            $buttonList['monthlyExamReportBtn']['copywriting'] = "1.只有浣熊英语、小数和学前数学可以使用该功能； 2.请检查是否配置章节对应的话术； 3.章节结课时间后显示； 4.绑了堂堂测的小英/小数章节";

        }

        //开放小初高，暂时注释，后面需要再打开
        //if (in_array($department, [Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL, Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY])) {
        $buttonList['introducePostBtn']['status'] = self::BTN_DISABLED_FALSE;
        //}
        $activityList = Api_Insight::getIntroduceConfigList($assistantUid);
        if (!empty($activityList)) {
            $buttonList['recruitPostBtn']['status'] = self::BTN_DISABLED_FALSE;
        }
        if (!empty($lessonId)) {
            $lessonInfo = Zb_Service_Dal_Lesson::getKVByLessonId([$lessonId], ['lessonId', 'playType', 'startTime', 'stopTime']);
            $lessonInfo = $lessonInfo['data'][$lessonId];
            if ($lessonInfo['playType'] == Sp_Dict_Course_PlayType::SPHD) {
                // AI章节发短信，屏蔽直播课催到课
                $buttonList['lpcSmsAttend']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['lpcSmsAttend']['copywriting'] = "非直播课章节，该按钮无法使用";
            } else {
                // 非直播章节发短信，屏蔽ai催到课
                $buttonList['lpcAiAttend']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['lpcAiAttend']['copywriting'] = "当前为直播章节，该按钮无法使用";
                if ($lessonInfo['playType'] == 1 && (date('Ymd', $lessonInfo['startTime']) != date('Ymd'))) {
                    // 直播章节，且如果当前时间大于章节开始时间，或者时间年月日不正确
                    $buttonList['lpcSmsAttend']['status'] = self::BTN_DISABLED_TRUE;
                    $buttonList['lpcSmsAttend']['copywriting'] = "当天未有直播课即将开课的章节暂时不可用";
                }
            }
        }

        // 学期报告反馈按钮
        $createSemesterReportRet = Api_JxReportHigh::getSemesterReportRule($courseId);
        $createSemesterReport = $createSemesterReportRet["createSemesterReport"] ?? false; // 课程是否会创建学习报告
        if ($createSemesterReport) {
            $semesterReportCountRet = Api_JxReportHigh::getSemesterReportCount($courseId);
            $semesterReportCount = $semesterReportCountRet["cnt"] ?? 0;
            if ($semesterReportCount == 0) {
                $buttonList['semesterReportSenior']['status'] = self::BTN_DISABLED_TRUE;
                $buttonList['semesterReportSenior']['copywriting'] = "暂无学期报告生成";
            }
        } else {
            $buttonList['semesterReportSenior']['status'] = self::BTN_DISABLED_TRUE;
            $buttonList['semesterReportSenior']['copywriting'] = "该课程未设置学期报告";
        }

        // 个性化学情反馈三期灰度
        if (Api_Assistantdeskgo_Api::grayHit($personUid, 'personal_learn_feedback_gray_conf')) {
            $buttonList['personalLearnFeedback']['status'] = self::BTN_DISABLED_FALSE;
        }





        $result['buttonList'] = $buttonList ? array_values($buttonList) : [];

        return $result;
    }

    /**
     * 查看章节下巩固练习是否包含主观题
     * @param $lessonId
     * @param $bindType
     * @return bool
     * @throws Common_Exception
     */
    private function hasHomeworkSubjectiveQuestion($lessonId, $bindType) {
        $hasSubjectiveQuestion = false;
        //获取绑定examId
        $bindKey = 'lesson_' . $lessonId . ':' . $bindType;
        $examRelationList = Api_Examcore::getRelation([$bindKey]);
        $examArr = isset($examRelationList[$bindKey]) ? $examRelationList[$bindKey] : [];
        if (is_array($examArr) && !empty($examArr)) {
            $examIds = array_unique(array_column($examArr, 'examId'));
            $examId = intval($examIds[0]);
            if ($examId) {
                $examInfos = Api_Examcore::getExam([$examId], true);
                if ($examInfos === false) {
                    Bd_Log::warning('批改结果反馈试卷信息获取异常');
                }
                $questionList = $examInfos[$examId]['questionList'] ?: [];
                if (is_array($questionList)) {
                    foreach ($questionList as $question) {
                        //主观题和互动课堂主观题
                        if (in_array($question['category'], [24, 5035])) {
                            $hasSubjectiveQuestion = true;
                        }
                    }
                }
            }
        }
        return $hasSubjectiveQuestion;
    }

    /**
     * 获取课程是否创建了打卡班级
     * @param $assistantUid
     * @param $courseId
     * @return int
     */
    private function getCreateKsClassButtonStatus($assistantUid, $courseId) {
        $isCreated = 0;
        $arrConds  = [
            "courseId"     => $courseId,
            "assistantUid" => $assistantUid,
            "isKsClass"    => 1,
            "status"       => Assistant_Ds_FirstLineTeacherCourse::STATUS_OK,
        ];
        $record    = $this->_objDsFirstLineTeacherCourse->getRecord($arrConds);
        if (is_array($record) && $record) {
            $isCreated = 1;
        }

        return $isCreated;
    }
}
