<?php
/**
 * Created by PhpStorm.
 * User: 庞俊超<<EMAIL>>
 * Date: 2018/3/14
 * Time: 17:16
 * 获取课程列表---辅导老师任务卡片根据不同的类型获取
 */
class Service_Page_Desk_Course_GetCourseList {

    private $_objDsAssistantNewTeacherCourse;
    private $_objDsAssistantNewCourse;
    private $_objDsAssistantCourseStudent;
    private $_objDsLessonStudent;
    private $_objDsServiceTmpl;
    private $_objDsTmplCourseBind;
    private $_objDsAssistantCourseGroup;
    private $courseSkuInfo;

    public function __construct(){
        $this->_objDsAssistantNewTeacherCourse  = new Service_Data_AssistantNewTeacherCourse();
        $this->_objDsAssistantNewCourse         = new Service_Data_AssistantNewCourse();
        $this->_objDsAssistantCourseStudent     = new Service_Data_AssistantCourseStudent();
        $this->_objDsLessonStudent              = new Service_Data_LessonStudent();
        $this->_objDsServiceTmpl                = new Service_Data_ServiceTmpl();
        $this->_objDsTmplCourseBind             = new Service_Data_TmplCourseBind();
        $this->_objDsAssistantCourseGroup       = new Assistant_Ds_AssistantCourseGroup();
    }

    public function execute($arrInput) {

        $assistantUid   = $arrInput['assistantUid'];
        $type           = $arrInput['type'];
        $learnSeasonId  = $arrInput['learnSeasonId'];
        $courseId       = $arrInput['courseId'];

        // 伴学出镜需求导致这里只能临时做一下兼容，改动最小
        $useLearnSeason = true;
        $year = null;
        $seasonId = null;
        $courseType = null;
        if (empty($learnSeasonId) && !empty($courseId)) {
            // 忽略学季
            $useLearnSeason = false;
        } else {
            $seasonInfo = AssistantDesk_Config::getYearSeasonList()[$learnSeasonId];
            $year       = $seasonInfo[1];
            $seasonId   = $seasonInfo[2];
            $courseType = $seasonInfo[3];
        }

        if(0 >= $assistantUid || 0 >= $type){
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数异常!', $arrInput);
        }

        $arrOutput = array();
        $arrOutput['list'] = array();

        //获取辅导老师下所有课程
        $courseList = $this->_objDsAssistantNewTeacherCourse->getTeacherCourseList($assistantUid);
        if(false === $courseList){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取辅导老师课程异常!', $arrInput);
        }

        if(empty($courseList)){
            return $arrOutput;
        }

        $assistantCourseIds = Tools_Array::getNewValueArray($courseList, 'courseId');
        $assistantCourseIds = array_values(array_unique($assistantCourseIds));

        // 过滤指定的课程
        if (!empty($courseId)) {
            $assistantCourseIds = array_values(array_filter($assistantCourseIds, function ($v) use ($courseId) {
                return $v == $courseId;
            }));
        }

        //先通过自己灌的表进行一次过滤
        $arrConds = ['course_id in ('. implode(',', $assistantCourseIds) .')'];
        if ($useLearnSeason) {
            $arrConds['seasonYear'] = $year;
        }
        $assistantNewCourse = $this->_objDsAssistantNewCourse->getListByConds($arrConds);

        if (false === $assistantNewCourse){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取课程信息异常!', $arrInput);
        }
        if (empty($assistantNewCourse) || !is_array($assistantNewCourse)){
            return $arrOutput;
        }

        // 获取课程信息
        $assistantNewCourseIds = array_column($assistantNewCourse, 'courseId');
        $courseInfos = [];
        if (!empty($assistantNewCourseIds)) {
            $courseInfos = Api_Dal::getCourseBaseByCourseIds($assistantNewCourseIds);
            if(false === $courseInfos){
                throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取课程信息异常!', $arrInput);
            }
        }

        $courseIds = array();
        foreach ($assistantNewCourse as $val){

            //去除已经删除的课程
            if(Zb_Const_Course::STATUS_DELETED == $val['status']){
                continue;
            }

            // 20250515 学季判断 从dal取值
            $intSeasonNum = $courseInfos[$val['courseId']]['season'];
            $strSeasonName = AssistantDesk_Season::$serviceSeasonNameMap[$intSeasonNum];
            $val['season'] = $strSeasonName;

            if($useLearnSeason && $val['season'] != Const_Season::getLearnSeasonIdNameFullMap()[$seasonId]){
                continue;
            }

            //课程类型判断
            $valCourseType = $val['type'];
            if (isset($courseInfos[$val['courseId']])) {
                // 优先用dal的，方便后续切换
                $valCourseType = $courseInfos[$val['courseId']]['courseType'] ?: $valCourseType;
            }
            if($useLearnSeason && $courseType != $valCourseType){
                continue;
            }

            $courseIds[] = $val['courseId'];
        }

        if(empty($courseIds)){
            return $arrOutput;
        }

        $courseIds = array_unique($courseIds);
        //获取所有课程的sku format信息
        $this->courseSkuInfo = AssistantDesk_Sku::getFormatedCourseSkuBaseInfo($courseIds);
        if(empty($this->courseSkuInfo)){
            Bd_Log::warning("获取课程sku信息异常,courseIds:".json_encode($courseIds));
        }

        $arrNewCourses = $this->_objDsAssistantNewCourse->getAssistantNewCourseArray($courseIds);
        if(!$arrNewCourses){
            return $arrOutput;
        }
        $arrNewCourses = Tools_Array::getNewKeyArray($arrNewCourses, 'courseId');

        //获取主讲老师信息
//        $teacherInfos = AssistantDesk_Teacher::getTeacherNameByCourseIds($courseIds);
        $teacherInfos = [];

        //获取辅导老师课程学员列表
        $studentList = $this->_objDsAssistantCourseStudent->getListByConds([
            'course_id in ('.implode(',', $courseIds).')',
            'assistantUid' => $assistantUid,
            'status' => Service_Data_AssistantCourseStudent::STATUS_OK,
        ]);
        if(false === $studentList){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取学员列表异常!', $arrInput);
        }
        if(empty($studentList)){
            return $arrOutput;
        }

        $studentList = Tools_Array::groupByColumn($studentList, 'courseId');
        //过滤掉没有学生的课程
        foreach ($courseIds as $key => $courseId){
            if(empty($studentList[$courseId])){
                unset($courseIds[$key]);
            }
        }

        //获取课程模板绑定关系--有小流量解决方案
        $skuCourseIdList = [];
        if(AssistantDesk_Config::TYPE_MARKETING_TOOL == $type){
            $courseSkuIdMap = Api_Dak::getCourseSkuIdMapByCourseIds($courseIds);
            if (is_array($courseSkuIdMap)) {
                $skuCourseIdList = $courseSkuIdMap;
            }
        }
        switch ($type){
            case AssistantDesk_Config::TYPE_WX_SEND:
                $list = $this->getWxSendList($courseIds, $courseInfos, $teacherInfos);
                break;
            case AssistantDesk_Config::TYPE_MARKETING_TOOL:
                $list = $this->getMarketingToolList($courseIds, $courseInfos,$skuCourseIdList, $arrNewCourses);
                break;
            case AssistantDesk_Config::TYPE_INTERVIEW_BBYY:
                $list = $this->getInterviewListBbyy($courseIds, $assistantUid, $courseInfos, $teacherInfos, $studentList, $arrNewCourses);
                break;
            case AssistantDesk_Config::TYPE_JIRA:
                $list = $this->getJiraCourseList($courseIds, $courseInfos);
                break;
            case AssistantDesk_Config::TYPE_RANK:
            case AssistantDesk_Config::TYPE_CHUNJI_PK:
                $list = $this->getRankCourseList($assistantUid, $courseIds, $courseInfos);
                break;
            case AssistantDesk_Config::TYPE_GROUP_BUY:
                $list = $this->getGroupBuyList($assistantUid, $courseInfos);
                break;
            case AssistantDesk_Config::TYPE_TOOL_CONTINUE:
                $list = $this->getToolContinueCourseList($courseIds, $courseInfos, $arrNewCourses);
                break;
            case AssistantDesk_Config::TYPE_TOOL_CONTACT:
                $list = $this->getContactCourseList($assistantUid, $courseIds, $courseInfos);
                break;
            default:
                throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '类型异常!', $arrInput);
        }

        $arrOutput['list'] = $list;

        return $arrOutput;

    }

    /**
     * @param $courseIds
     * @param $assistantUid
     * @param $courseInfos
     * @param $teacherInfos
     * @param $studentList
     * @param $arrNewCourses
     * @return array
     * 获取帮帮英语家访课程列表
     */
    private function getInterviewListBbyy($courseIds, $assistantUid, $courseInfos, $teacherInfos, $studentList, $arrNewCourses){

        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];
            //非班课不展示
            if ($courseInfo['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
                continue;
            }

            $courseNewInfo = $arrNewCourses[$courseId];
            if(($courseNewInfo['pullNewDuty'] & Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_1067) <= 0  ){
                continue;
            }
            $studentUids = Tools_Array::getNewValueArray($studentList[$courseId], 'studentUid');

            $sopData = [];
            if($studentUids && is_array($studentUids)){
                $fields = [
                    "isRefund",
                    'studentUid',
                    'interviewType',
                    'isInterviewByPhone',
                    'hasInterview',
                    'newUserType',
                ];
                $cuList = \Assistant_Common_Service_DataService_Query_CourseStudentData::getListByCourseIdsStudentUids([$courseId],$studentUids, $fields);
                foreach($cuList as $k => $v) {
                    if ($v['isRefund']) {
                        unset($cuList[$k]);
                    }
                }
                $sopData = array_values($cuList);
                $sopData = Tools_Array::getNewKeyArray($sopData, 'studentUid');
            }

            $unInterview = 0;
            $unInterviewByN1 = 0;
            $unInterviewByUnN1 = 0;
            if($studentUids && is_array($studentUids)){
                foreach ($studentUids as $studentUid){
                    $sopRow = $sopData[$studentUid];

                    //电话或微信是否合规
                    $interviewIsComplete        = $sopRow['interviewType'] ? ($sopRow['interviewType'] == 1 ? $sopRow['isInterviewByPhone'] : 1) : 0;
                    $isInterview     = $sopRow['hasInterview'] ? $sopRow['hasInterview'] : 0;

                    //家访状态是否已完成
                    $interview = 0;
                    if($isInterview && $interviewIsComplete){
                        $interview = 1;
                    }

                    if(!$interview){
                        if(in_array($sopRow['newUserType'], ["N1","N2","Z"])){
                            //计算N1+N2+Z未完成家访人数
                            $unInterview     ++;
                            $unInterviewByN1 ++;
                        }else{
                            //计算O1+O2未完成人数
                            $unInterview        ++;
                            $unInterviewByUnN1  ++;
                        }
                    }
                }
            }

            $arrFields = [
                "userType",
                "interviewNum",
                "beforeRegNum",
            ];
            //获取数据后台延迟汇总数据
            $dataCaSop = AssistantDesk_AssistantData::getCaSopData($courseInfo['year'], AssistantDesk_Season::$reverseServiceSeasonIdMap[$courseInfo['learnSeason']], [
                'courseId'      => $courseId,
                'assistantUid'  => $assistantUid,
                'saveTime'      => intval(date('Ymd')),
            ], $arrFields);


            $interview      = 0;   //N1+N2+Z家访数
            $interviewTotal = 0;   //N1+N2+Z家访总数

            if($dataCaSop && is_array($dataCaSop)){
                foreach ($dataCaSop as $val){
                    if(in_array($val['userType'], ['N1','N2','Z'])){
                        $interview      += $val['interviewNum'];
                        $interviewTotal += $val['beforeRegNum'];
                    }
                }
            }

            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'];
            $row['teacherName']     = $this->courseSkuInfo[$courseId] ? implode(',', $this->courseSkuInfo[$courseId]['teacherNameList']) : "";
            $row['onlineTime']      = $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            $row['unInterviewTotal']    = $unInterview;
            $row['unInterviewByN1']     = $unInterviewByN1;
            $row['unInterviewByUnN1']   = $unInterviewByUnN1;
            $row['interviewRateByN1']   = $interviewTotal ? intval($interview * 100 / $interviewTotal) : 0;
            $row['interviewRateName']   = '新学员电话或微信家访率';

            $ret[] = $row;
        }

        return $ret;
    }

    /**
     * @param $courseIds
     * @param $assistantUid
     * @param $courseInfos
     * @param $teacherInfos
     * @param $studentList
     * @return array
     * 获取预到课列表
     */
    private function getAttendList($courseIds, $assistantUid, $courseInfos, $teacherInfos, $studentList){
        $ret = [];
        if(!$courseIds){
            return $ret;
        }
        $lessonLists = Api_Dal::getLessonListByCourseIds($courseIds);
        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];

            $lessonList = $lessonLists[$courseId]['lessonList'];
            if(empty($lessonList) || !is_array($lessonList)){
                continue;
            }
            $lessonList = Tools_Array::sortByMultiCols($lessonList, array('startTime' => SORT_ASC));
            $curLessonInfo = [];
            foreach ($lessonList as $val){
                if($val['lessonType'] != Api_Dal::LESSON_TYPE_MAIN){
                    continue;
                }
                if($val['stopTime'] + 1800 < time()){
                    continue;
                }
                $curLessonInfo = $val;
                break;
            }

            if(empty($curLessonInfo)){
                continue;
            }

            $studentUids = Tools_Array::getNewValueArray($studentList[$courseId], 'studentUid');

            $lessonStudent = [];
            if($studentUids && is_array($studentUids)){
                $arrConds = array(
                    'lessonId' => $curLessonInfo['lessonId'],
                    'assistantUid' => $assistantUid,
                    'student_uid in ('. implode(',', $studentUids) .')',
                    'status'    =>  Service_Data_LessonStudent::STATUS_OK,
                );
                $lessonStudent = $this->_objDsLessonStudent->getListByConds($courseId, $arrConds);
                if($lessonStudent && is_array($lessonStudent)){
                    $lessonStudent = Tools_Array::getNewKeyArray($lessonStudent, 'studentUid');

                }
            }

            $total = 0;
            $leave = 0;
            $unContact = 0;

            if($studentUids && is_array($studentUids)){
                foreach ($studentUids as $studentUid){
                    $total ++ ;
                    if($lessonStudent[$studentUid]['preAttend'] == Service_Data_LessonStudent::PRE_ATTEND_LEAVE){
                        $leave ++;
                    }
                    if($lessonStudent[$studentUid]['preAttend'] == Service_Data_LessonStudent::PRE_ATTEND_NO_CONTACT){
                        $unContact ++;
                    }
                }
            }

            $row = [];
            $row['courseId']    = $courseId;
            $row['courseName']  = $courseInfo['courseName'];
            $row['courseType']  = $courseInfo['courseType'];
            $row['lessonId']    = $curLessonInfo['lessonId'];
            $row['lessonName']  = $curLessonInfo['lessonName'];
            $row['lessonTime']  = date('m月d日 H:i', $curLessonInfo['startTime']).'-'.date('H:i', $curLessonInfo['stopTime']);
            $row['inClass']     = 0;
            if($curLessonInfo['startTime'] - 1800 < time() && $curLessonInfo['stopTime'] + 1800 > time()){
                $row['inClass'] = 1;
            }
            $row['teacherName'] = $this->courseSkuInfo[$courseId] ? implode(',', $this->courseSkuInfo[$courseId]['teacherNameList']) : "";

            $row['total']       = $total;
            $row['leave']       = $leave;
            $row['unContact']   = $unContact;

            $ret[] = $row;
        }

        return $ret;
    }

    /**
     * @param $courseIds
     * @param $courseInfos
     * @param $teacherInfos
     * @param $studentList
     * @return array
     * 日常维护课程列表
     */
    private function getDailyTaskList($courseIds, $courseInfos, $teacherInfos, $studentList){

        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        foreach ($courseIds as $courseId) {

            $courseInfo = $courseInfos[$courseId];

            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'];
            $row['courseType']      = $courseInfo['courseType'];
            $row['teacherName']     = $this->courseSkuInfo[$courseId] ? implode(',', $this->courseSkuInfo[$courseId]['teacherNameList']) : "";
            $row['onlineTime']      = $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            //非班课
            if ($row['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
                $ret[] = $row;
                continue;
            }

            $studentUids = Tools_Array::getNewValueArray($studentList[$courseId], 'studentUid');

            $sopData = [];
            if($studentUids && is_array($studentUids)){
                $fields = [
                    "isRefund",
                    'studentUid',
                    'newUserType',
                ];
                $cuList = \Assistant_Common_Service_DataService_Query_CourseStudentData::getListByCourseIdsStudentUids([$courseId],$studentUids, $fields);
                foreach($cuList as $k => $v) {
                    if ($v['isRefund']) {
                        unset($cuList[$k]);
                    }
                }
                $sopData = array_values($cuList);
                $sopData = Tools_Array::getNewKeyArray($sopData, 'studentUid');
            }

            $unBackInterviewTotal = 0;      //未回访人数
            $unBackInterviewByN1 = 0;       //N1未回访人数
            $unBackInterviewByUnN1 = 0;     //非N1未回访人数

            $n1Num = 0; //N1人数
            $backInterviewByN1 = 0; //N1回访人数
            if($studentList[$courseId] && is_array($studentList[$courseId])){

                foreach ($studentList[$courseId] as $val){

                    $studentUid = $val['studentUid'];

                    if($val['backInterview']){
                        if($sopData[$studentUid]['newUserType'] == 'N1'){
                            $n1Num ++;
                            $backInterviewByN1 ++;
                        }
                    }else{
                        $unBackInterviewTotal ++;
                        if($sopData[$studentUid]['newUserType'] == 'N1'){
                            $n1Num ++;
                            $unBackInterviewByN1 ++;
                        }else{
                            $unBackInterviewByUnN1 ++;
                        }
                    }
                }
            }


            $row['unBackInterviewTotal'] = $unBackInterviewTotal;
            $row['unBackInterviewByN1'] = $unBackInterviewByN1;
            $row['unBackInterviewByUnN1'] = $unBackInterviewByUnN1;
            $row['backInterviewRateByN1'] = $n1Num ? intval($backInterviewByN1 * 100 / $n1Num) : 0;

            $ret[] = $row;
        }

        return $ret;
    }

    /**
     * @Notes:获取微信群发页面的课程列表
     * @param array | $courseIds
     * @param array | $courseInfos
     * @param array| $teacherInfos
     * @return array
     */
    private function getWxSendList($courseIds, $courseInfos, $teacherInfos){

        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];

            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'];
            $row['courseType']      = $courseInfo['courseType'];
            $row['teacherName']     = $this->courseSkuInfo[$courseId] ? implode(',', $this->courseSkuInfo[$courseId]['teacherNameList']) : "";
            $row['onlineTime']      = $this->courseSkuInfo[$courseId]['onlineFormatTime'];

            $ret[] = $row;
        }
        return $ret;
    }

    /**
     * @Notes:获取营销工具的课程列表
     * @param array | $courseIds
     * @param array | $courseInfos
     * @param array| $skuCourseIdList
     * @param array | $arrNewCourses
     * @return array
     */
    private function getMarketingToolList($courseIds, $courseInfos, $skuCourseIdList, $arrNewCourses){
        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];

            //营销工具只展示班课的课程
            if($courseInfo['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
                continue;
            }
            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'] . ' ' . $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            $row['skuId']           = $skuCourseIdList[$courseId];
            $row['gradeStage']      = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']];
            //添加课程年级信息和开课时间
            $row['firstLessonTime'] = $courseInfo['firstLessonTime'];
            $row['grade'] = $courseInfo['mainGradeId'];
            //判断课程品牌信息brand 1：一课 2:浣熊 3:帮帮英语
            $row['brand'] = 1;
            if (isset($arrNewCourses[$courseId]['pullNewDuty']) && ($arrNewCourses[$courseId]['pullNewDuty'] & Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_1067) > 0) {
                $row['brand'] = 3;
            }

            $ret[] = $row;
        }
        //按照开课时间升序排序
        $ret = Tools_Array::sortByMultiCols($ret, array('firstLessonTime' =>  SORT_ASC));
        return $ret;
    }



    /**
     * @Notes:获取工作站问题反馈入口
     * @param array | $courseIds
     * @param array | $courseInfos
     * @return array
     */
    private function getJiraCourseList($courseIds, $courseInfos){

        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        $lessonInfos = Api_Dal::getLessonListByCourseIds($courseIds,
            ['lessonId','lessonName', 'startTime']);

        if(false === $lessonInfos){
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取辅导老师课程章节数据异常!', $courseIds);
        }
        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];
            $lessonInfo = $lessonInfos[$courseId]['lessonList'];

            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'];
            $row['courseType']      = $courseInfo['courseType'];

            $row['onlineTime']      = $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            $row['lesson']          = [];
            if (is_array($lessonInfo)){
                foreach ($lessonInfo as $lessonId => $lesson){
                    $rowLesson = [];
                    $rowLesson['lessonId'] = $lessonId;
                    $rowLesson['lessonName'] = $lesson['lessonName'];
                    $row['lesson'][] = $rowLesson;
                }
            }
            $ret[] = $row;
        }
        return $ret;
    }

    /**
     * Notes:获取排行榜课程列表
     * 包含章节信息，只有班课和核心章节数据
     * @param $courseIds
     * @param $courseInfos
     * @return array
     * @throws Common_Exception
     */
    private function getRankCourseList($assistantUid, $courseIds, $courseInfos){
        $nowTime = time();

        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        $lessonInfos = Api_Dal::getLessonListByCourseIds($courseIds,
            ['lessonId','lessonName', 'startTime', 'stopTime',  'lessonType']);

        if(false === $lessonInfos){
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取辅导老师课程章节数据异常!', $courseIds);
        }
        foreach ($courseIds as $courseId){

            $mainGradeId      = $courseInfos[$courseId]['mainGradeId'];
            $mainGradeId      = Zb_Const_GradeSubject::$GRADEMAPXB[$mainGradeId];

            // 获取课程下的小班数据
            $classGroupNameMap = Api_Assignclass::getClassGroupNameMap($courseId, $assistantUid);
            if (empty($classGroupNameMap) || !is_array($classGroupNameMap)){
                throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取小班分班信息失败');
            }
            $classGroupNameMap = $this->getGroupNameFilterMap($classGroupNameMap);
            $courseGroup = $this->getWxGroupIList($assistantUid,$courseId) ?: [];

            $courseInfo = $courseInfos[$courseId];
            $lessonInfo = $lessonInfos[$courseId]['lessonList'];

            //排行榜只展示班课的课程
//            if($courseInfo['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
//                continue;
//            }

            $row = [];
            $row['courseId']        = $courseId;
            $row['courseName']      = $courseInfo['courseName'];
            $row['courseType']      = $courseInfo['courseType'];

            //小初展示学员分组筛选,初中不展示，前端根据此字段判断
            $row['mainGradeId']     = $mainGradeId;
            $row['groupFilter']     = $courseGroup;
            $row['classFilter']     = $classGroupNameMap;

            $row['courseTypeILab']  = 0;

            //如果是初二物理、初三物理则显示ilab对应的课程
            $acceptGrade = [3, 4];
            $acceptSubject = [Zb_Const_GradeSubject::PHYSICS];
            if(in_array($courseInfo['mainGradeId'], $acceptGrade) && in_array($courseInfo['mainSubjectId'], $acceptSubject)){
                $row['courseTypeILab']  = 1;
            }

            //判断课程是否符合春季PK项目要求,初中全部年级学科，高1英语、高2物理
            //2020-03-22 调整为初中全部年级学科，高2英语、高2物理
            $row['isChunJiPK'] = 0;
            if ($mainGradeId == Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR || ($courseInfo['mainGradeId'] == 6 && $courseInfo['mainSubjectId'] == 3) || ($courseInfo['mainGradeId'] == 6 && $courseInfo['mainSubjectId'] == 4)) {
                $row['isChunJiPK'] = 1;
            }

            $row['onlineTime']      = $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            if (is_array($lessonInfo)){
                //核心章节ID信息
                $coreLessonIds = [];
                foreach ($lessonInfo as $lessonId => $lesson) {
//                    //小学和学前非核心章节不展示
//                    if (in_array($mainGradeId, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY, Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL])
//                        && $lesson['lessonType'] != Zb_Const_Course::TYPE_COURSELESSONTYPE_CORE) {
//                        continue;
//                    }
                    // 小学也展示提升章节
                    if ($lesson['lessonType'] == Zb_Const_Course::TYPE_COURSELESSONTYPE_CORE) {
                        $coreLessonIds[] = $lessonId;
                    }
                    $rowLesson = [];
                    $rowLesson['lessonId']      = $lessonId;
                    $rowLesson['lessonName']    = $lesson['lessonName'];
                    //添加章节是否结束标识
                    $rowLesson['isLessonEnded'] = $nowTime > $lesson['stopTime'] ? 1 : 0;
                    $row['lesson'][] = $rowLesson;
                }

                //根据核心章节信息获取阶段测试绑定情况
                $lessonStageTestMap = $this->getStageTestInfo($coreLessonIds);

                //章节信息中添加阶段测试信息
                foreach ($row['lesson'] as &$rl) {
                    $rl['stageTestBind'] = 0;//未绑定阶段测试
                    $rl['stageTestOpen'] = 0;//未开放
                    if (isset($lessonStageTestMap[$rl['lessonId']])) {
                        $rl['stageTestBind'] = 1;//已绑定阶段测试
                        $rl['stageTestOpen'] = $lessonStageTestMap[$rl['lessonId']]['stageTestOpen'];
                        $rl['stageTest'] = $lessonStageTestMap[$rl['lessonId']]['stageTest'];
                        if ($rl['stageTestOpen']) {
                            $rl['lessonName'] = $rl['lessonName'] . '阶段测试已开放';
                        } else {
                            $rl['lessonName'] = $rl['lessonName'] . '阶段测试未开放';
                        }
                    }
                }
            }
            $ret[] = $row;
        }
        return $ret;
    }

    private function getStageTestInfo($coreLessonIds){
        $lessonStageTestMap = [];
        if (empty($coreLessonIds)) {
            return $lessonStageTestMap;
        }
        $bindList = [];
        foreach ($coreLessonIds as $lessonId){
            $bindKey  = 'lesson_'.$lessonId.":".Api_Exam::BIND_TYPE_STAGE;
            $bindList[] = $bindKey;
        }
        $stageTestBindInfos = Api_Examcore::getRelationMulti($bindList);
        if ($stageTestBindInfos === false || !is_array($stageTestBindInfos)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_API_ERROR, '章节阶段测试绑定信息获取失败');
        }
        $bindLessonIds = [];
        foreach ($stageTestBindInfos as $bindKey => $stageTestBindInfo) {
            if ($stageTestBindInfo) {
                //lesson_367492:9
                list($bindStr, $examType)  = explode(':',$bindKey);
                list($str, $lessonId) = explode('_', $bindStr);
                $bindLessonIds[] = $lessonId;
                $lessonStageTestMap[$lessonId] = [];
            }
        }
        if (empty($bindLessonIds)) {
            return $lessonStageTestMap;
        }

        //获取阶段测试详细信息
        $lessons = Api_Dal::getLessonBaseByLessonIds($bindLessonIds, ['stageTest']);
        if ($lessons === false) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_API_ERROR, '章节基础信息获取失败');
        }
        $nowTimeStamp = time();
        foreach ($bindLessonIds as $lessonId) {
            $lessonStageTestMap[$lessonId]['stageTestOpen'] = 0;//未开放
            $lessonStageTestMap[$lessonId]['stageTest'] = $lessons[$lessonId]['stageTest'];
            if (isset($lessons[$lessonId]['stageTest']['startTime']) && $lessons[$lessonId]['stageTest']['startTime'] <= $nowTimeStamp) {
                $lessonStageTestMap[$lessonId]['stageTestOpen'] = 1;//已开放
            }
        }
        return $lessonStageTestMap;
    }

    private function getGroupNameFilterMap($classGroupNameMap){
        $arrOutput = [];
        if (!is_array($classGroupNameMap) || empty($classGroupNameMap)){
            return $arrOutput;
        }

        foreach ($classGroupNameMap as $key => $item){
            $row = [];
            $row['name']   = $item;//界面显示的值.1组，2组
            $row['value']  = intval($key);//参数传递的值，classId
            $row['sort']   = intval($item);//用于排序组名
            $arrOutput[]   = $row;
        }
        $arrOutput = Tools_Array::sortByMultiCols($arrOutput, ['sort' => SORT_ASC]);
        return $arrOutput;
    }
    /**
     * @param $assistantUid
     * @param $courseInfos
     * @return array
     * @throws Common_Exception
     */
    private function getGroupBuyList($assistantUid, $courseInfos){
        $ret = [];
        // 1. 判断辅导老师是否参与拼团活动

        //小学	五、六年级	  语文	北京
        //初中	初三	数学、化学	西安
        //高中	高一	物理、化学	合肥

        // 1.1 获取辅导老师信息
        $arrAssistant = Api_UserProfile::getUserListByDeviceUids([$assistantUid]);
        if(false === $arrAssistant){
            throw new Common_Exception(Common_ExceptionCodes::NETWORK_ERROR, '获取辅导老师业务账号信息异常~', [
                'assistantUid'  => $assistantUid
            ]);
        }
        $arrAssistant = Tools_Array::getNewKeyArray($arrAssistant['list'], "userId");
        $arrAssistant = $arrAssistant[$assistantUid];

        // 1.2 获取辅导老师地区
        $cityMap = Api_UserProfile::getApiConf();
        if(false === $cityMap){
            throw new Common_Exception(Common_ExceptionCodes::NETWORK_ERROR, '获取辅导老师业务账号信息异常~', [
                'assistantUid'  => $assistantUid
            ]);
        }
        $cityMap = $cityMap['user']['cityMap'];
        $city = $cityMap[$arrAssistant['city']];

        // 1.3 获取辅导老师学科年级
        $grade = current($courseInfos)['mainGradeId'];
        $subject = current($courseInfos)['mainSubjectId'];

        // 默认无活动
        $hasActivity = 0;
        foreach (Api_Fenxiao::$pintuanMap as $stage => $map){
            if(in_array($grade, $map['grade'])
                && in_array($subject, $map['subject'])
                && in_array($city, $map['city'])
            ){
                $hasActivity = 1;
                break;
            }
        }
        if(!$hasActivity){
            return $ret;
        }
        // 2 获取活动url
        $ActivityInfo = Api_Fenxiao::getGroupBuyActivityInfo();

        foreach ($ActivityInfo as $key => $info){
            $row['grade']        = $key;
            $row['activityName'] = $info['activityName'];
            // 2.1 创建分销员
            $inviterInfo = Api_Fenxiao::addInviter($assistantUid, $info['configId']);
            if(!$inviterInfo['inviterInfo']['inviterUid']){
                throw new Common_Exception(Common_ExceptionCodes::NETWORK_ERROR, '创建分销员失败~', ['assistantUid' => $assistantUid]);
            }
            // 2.2 获取分销码
            $shareCode = Api_Fenxiao::getShareCodeByInviterUid($inviterInfo['inviterInfo']['inviterUid'], $info['configId']);
            if(!$shareCode['shareCode']){
                throw new Common_Exception(Common_ExceptionCodes::NETWORK_ERROR, '获取分销码失败~', ['assistantUid' => $assistantUid]);
            }
            // 2.3 拼接二维码
            $row['qrcodeUrl'] = $info['url'] . $shareCode['shareCode'];
            $ret[$key] = $row;
        };
        $currentInfo = $ret[$grade];
        if(!$currentInfo){
            return array_values($ret);
        }
        unset($ret[$grade]);
        array_unshift($ret, $currentInfo);

        return array_values($ret);
    }

    /**
     * 营销工具续报海报课程列表
     * @param $courseIds
     * @param $courseInfos`
     * @param $arrNewCourses
     * @return array
     */
    private function getToolContinueCourseList($courseIds, $courseInfos, $arrNewCourses){
        $ret = [];

        if(!$courseIds){
            return $ret;
        }

        foreach ($courseIds as $courseId){
            $courseInfo = $courseInfos[$courseId];

            //只展示班课的课程
            if($courseInfo['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
                continue;
            }

            //帮帮英语
            $isBangBang = 0;
            if (($arrNewCourses[$courseId]['pullNewDuty'] & Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_1067) > 0) {
                $isBangBang = 1;
            }
            $row = [];
            $row['courseId']        = $courseId;
            $row['grade']           = $courseInfo['mainGradeId'];
            $row['courseName']      = $courseInfo['courseName'] . ' ' . $this->courseSkuInfo[$courseId]['onlineFormatTime'];
            $row['isBangBang']      = $isBangBang;
            $row['department']      = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']] ?? 0;

            $ret[] = $row;
        }
        return $ret;
    }

    /**
     * Notes:
     * @param $assistantUid
     * @param $courseIds
     * @param $courseInfos
     * @return array
     * @throws Common_Exception
     */
    private function getContactCourseList($assistantUid, $courseIds, $courseInfos){
        $ret = [];
        if (empty($assistantUid) || empty($courseIds) || !is_array($courseIds)){
            return $ret;
        }
        $arrConds = [
            'course_id in ('.implode(',', $courseIds).')',
            'assistantUid' => $assistantUid,
            'status'       => Service_Data_AssistantCourseStudent::STATUS_OK,
        ];
        $studentList = $this->_objDsAssistantCourseStudent->getListByConds($arrConds, ['id', 'courseId']);
        if (false === $studentList){
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取课程学生数据异常，请重试');
        }
        if (empty($studentList)){
            return $ret;
        }

        $studentList = Tools_Array::groupByColumn($studentList, 'courseId');
        foreach ($courseIds as $courseId){

            //过滤非班课
            if ($courseInfos[$courseId]['courseType'] != Zb_Const_Course::TYPE_PRIVATE_LONG){
                continue;
            }
            $row = [];
            $row['courseId']   = $courseId;
            $row['courseName'] = $courseInfos[$courseId]['courseName'];
            $row['studentNum'] = count($studentList[$courseId]);
            $ret[] = $row;
        }
        return $ret;
    }

    /**
     * 获取微信群基础信息数据
     * @param $assistantUid
     * @param $courseId
     * @return array
     */
    private function getWxGroupIList($assistantUid, $courseId) {
        $weixinGroupList = [];

        // 获取课程绑定的微信群
        $arrConds    = [
            'assistantUid' => $assistantUid,
            'courseId'     => $courseId,
            'deleted'      => Assistant_Ds_AssistantCourseGroup::DELETED_STATUS_NO,
        ];
        $courseGroup = $this->_objDsAssistantCourseGroup->getList($arrConds, ['groupId', 'taskName']);
        if ($courseGroup == false || !is_array($courseGroup)) {
            return $weixinGroupList;
        }

        $weixinGroupIds = [];
        foreach ($courseGroup as $v) {
            if ($v['groupId']) {
                $weixinGroupIds[] = $v['groupId'];
            }
        }

        if (empty($weixinGroupIds)) {
            return $weixinGroupList;
        }

        $GroupInfos = Api_Kunpeng::getMultiGroupInfo($assistantUid, $weixinGroupIds);
        if ($GroupInfos == false || !is_array($GroupInfos)) {
            return $weixinGroupList;
        }

        $GroupInfos = $GroupInfos ? array_values(Tools_Array::sortByMultiCols($GroupInfos, ['weixinGroupName' => SORT_ASC])) : [];
        foreach ($GroupInfos as $key => $groupInfo) {
            $weixinGroupList[$key]['name']  = $groupInfo['weixinGroupName'];
            $weixinGroupList[$key]['value'] = (string)$groupInfo['weixinGroupId'];
            $weixinGroupList[$key]['sort']  = ($key + 1);
        }

        return $weixinGroupList;
    }
}
