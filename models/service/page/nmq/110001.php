<?php

/**
 * @description : 班课课程上线
 * <AUTHOR>
 * Date: 2018/10/13
 */
class Service_Page_Nmq_110001 extends Zb_Common_BaseCommitPage
{

    protected function process()
    {

        Bd_Log::notice('receiveMq:' . $this->intCmdNo . json_encode($this->arrCommand));
        Hk_Util_Log::setLog('arrCommand_Params', json_encode($this->arrCommand));
        if (AssistantDesk_Common_RmqMigGray::isNewRecieve($this->arrCommand)) {
            Bd_Log::notice('灰度开关已开，旧的跳过');
            return [];
        }
        $arrParams = $this->arrCommand;

        $courseId = intval($arrParams['courseId']);

        $whiteFlag = AssistantDesk_Base_CourseAccessWhiteRule::isWitheCourse($courseId);
        if($whiteFlag)
        {
            //在白名单，不处理
            Bd_Log::addNotice('命中黑名单，不处理 whiteFlag_110001'.$courseId,$whiteFlag);
            return [];
        }else{
            Bd_Log::addNotice('没有命中黑名单，处理 whiteFlag_110001'.$courseId,$whiteFlag);
        }

        if ($courseId <= 0) {

            AssistantDesk_ServiceTools::printWarningMsg($arrParams, __CLASS__, __LINE__);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "courseId error : {$courseId}", $arrParams);
        }

        //不是想要的课程直接返回
        if (!$this->filterCourse($arrParams)) {
            return [];
        }

        //这里获取是怕有重复上线的操作
        $objAssistantNewCourse = new Service_Data_AssistantNewCourse();
        $courseInfo = $objAssistantNewCourse->getAssistantNewCourse($courseId);

        if (false === $courseInfo) {

            AssistantDesk_ServiceTools::printWarningMsg($arrParams, __CLASS__, __LINE__);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "course get error : {$courseId}", $arrParams);
        }

        $arrFields = $this->filterParams($arrParams);

        if (!$arrFields) {
            return [];
        }

        //已提交状态
        $arrFields['submit'] = Service_Data_AssistantNewCourse::SUBMIT_YES;

        // 该MQ已经不再消费了
        /*
        if ($courseInfo) {
            $res = $objAssistantNewCourse->updateAssistantNewCourse($courseId, $arrFields);
        } else {
            $arrFields['courseId'] = $courseId;
            $res = $objAssistantNewCourse->addAssistantNewCourse($arrFields);
        }

        if (false === $res) {

            AssistantDesk_ServiceTools::printWarningMsg($arrParams, __CLASS__, __LINE__);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "sync tblAssistantNewCourse error : {$courseId}", $arrParams);
        }
        */

        return [];
    }

    private function filterParams($arrParams)
    {

        $arrFields = $extData = [];

        if (isset($arrParams['courseType'])) {
            $arrFields['type'] = intval($arrParams['courseType']);
        }

        //判断课程名
        if (isset($arrParams['courseName'])) {
            $arrFields['courseName'] = strval($arrParams['courseName']);
        }

        //课程开始时间
        if (isset($arrParams['startTime']) && $arrParams['startTime']) {
            $arrFields['startTime'] = $arrParams['startTime'];
        }

        //课程结束时间
        if (isset($arrParams['stopTime']) && $arrParams['stopTime']) {
            $arrFields['stopTime'] = $arrParams['stopTime'];
        }

        //判断年级 目前只接收主年纪的课程
        if (isset($arrParams['intGradeId'])) {
            $arrFields['grade'] = $arrParams['intGradeId'];
        }

        //判断学科
        if (isset($arrParams['intSubjectId'])) {
            $arrFields['subject'] = $arrParams['intSubjectId'];
        }

        //判断人数
        if (isset($arrParams['studentMaxCnt'])) {
            $arrFields['studentMaxCnt'] = intval($arrParams['studentMaxCnt']);
        }

        //判断内外部课
        if (isset($arrParams['isInner'])) {
            $arrFields['isInner'] = intval($arrParams['isInner']);
        }

        //判断学季
        if (isset($arrParams['learnSeason'])) {
            $learnSeason = explode('_', Const_Season::getLearnSeasonIdNameFullMap()[intval($arrParams['learnSeason'])]);
            $arrFields['season'] = strval($learnSeason[0]);
            $arrFields['seasonNum'] = intval($learnSeason[1]);
        }

        //判断年份
        if (isset($arrParams['year'])) {
            $arrFields['seasonYear'] = intval($arrParams['year']);
        }

        //是否是VIP课程
        if (isset($arrParams['isVipClass'])) {
            $arrFields['isVipClass'] = intval($arrParams['isVipClass']);
        }

        //课程的拉新属性, bitmap 存储
        if (isset($arrParams['pullNewDuty'])) {
            $arrFields['pullNewDuty'] = Service_Data_AssistantNewCourse::getPullNewDutyBit($arrParams['pullNewDuty']);
        }

        //判断章节
        $lessonList = $arrParams['lessonList'];
        if ($lessonList && is_array($lessonList)) {

            $extLesson = array();
            foreach ($lessonList as $v) {

                if (!$this->filterLesson($arrParams['courseType'], $v['courseLessonType'])) {
                    //continue;
                }

                $extLesson[$v['lessonId']] = array(
                    'lessonId'  => $v['lessonId'],
                    'startTime' => $v['startTime'],
                    'stopTime'  => $v['stopTime'],
                    'status'    => Service_Data_AssistantNewCourse::LESSON_STATUS_OK,
                    'type'      => $v['courseLessonType'],
                );
            }

            $extData['lessonList'] = $extLesson;
        }

        $arrFields['extData'] = $extData ? $extData : [];

        //判断新课程类型
        if (isset($arrParams['newCourseType'])) {
            $arrFields['newCourseType'] = intval($arrParams['newCourseType']);
        }
        return $arrFields;
    }

    //判断课程是否保留
    private function filterCourse($arrParams)
    {

        $courseType = intval($arrParams['courseType']);
        $pullNewDuty = $arrParams['pullNewDuty'] && is_array($arrParams['pullNewDuty']) ? $arrParams['pullNewDuty'] : [];

        //班课全留
        if (Zb_Const_Course::TYPE_PRIVATE_LONG == $courseType) {

            return true;
        }

        //专题课下短训班
        if (Zb_Const_Course::TYPE_PRIVATE == $courseType &&
            in_array(Service_Data_AssistantNewCourse::PULL_NEW_DUTY_983, $pullNewDuty)) {

            return true;
        }

        //专题课下微信群服务
        if (Zb_Const_Course::TYPE_PRIVATE == $courseType &&
            in_array(Service_Data_AssistantNewCourse::PULL_NEW_DUTY_1093, $pullNewDuty)) {

            return true;
        }

        //专题课下加油课保留
        if (Zb_Const_Course::TYPE_PRIVATE == $courseType &&
            in_array(Service_Data_AssistantNewCourse::PULL_NEW_DUTY_2286, $pullNewDuty)) {

            return true;
        }

        return false;
    }

    /**
     * 判断章节是否需要保留
     * 班课保留核心课(主题课章节),短训班暂时都保留
     * @param int $courseType 课程类型
     * @param int $lessonType 章节类型
     * @return bool
     */
    private function filterLesson($courseType, $lessonType)
    {

        $courseType = intval($courseType);
        $lessonType = intval($lessonType);

        //班课并且非主题课不保留
        if (Zb_Const_Course::TYPE_PRIVATE_LONG == $courseType && Zb_Const_Course::TYPE_COURSELESSONTYPE_CORE != $lessonType) {

            return false;
        }

        return true;
    }

}