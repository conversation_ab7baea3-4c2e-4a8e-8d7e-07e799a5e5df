<?php

class Service_Page_Deskcontract_Contractstudent_Performance {

    const LESSON_START_STATUS_NOT  = 0;// 未开始
    const LESSON_START_STATUS_SOON = 1;// 即将开课
    const LESSON_START_STATUS_IN   = 2;// 正在开课
    const LESSON_START_STATUS_END  = 3;// 已结束

    const PREVIEW_OPEN  = 1; // 预习开启
    const PREVIEW_HAS   = 1; // 有预习
    const HOMEWORK_OPEN = 1; // 巩固练习开启HOMEWORK_OPEN
    const HOMEWORK_HAS  = 1; // 有巩固练习

    /**
     * @es    idl_lpc_leads
     * @filed isAttend, isFinish, isPlayback字段
     *
     * 到课状态
     */
    const UNDONE_STATUS  = 1;
    const SUCCESS_STATUS = 2;
    const WAIT_STATUS    = 3;

    const COURSE_STATUS
        = [
            self::UNDONE_STATUS  => '未完成',
            self::SUCCESS_STATUS => '已完成',
            self::WAIT_STATUS    => '未开始'
        ];

    public static $practiceCorrectLevelMap = [
        1 => 'S',
        2 => 'A',
        3 => 'B',
    ];
    public static $deerHomeworkLevelMap    = [
        1 => 'S',
        2 => 'A',
        3 => 'B',
    ];

    const LESSON_EXERCISE_DETAIL   = '%d|%d|%d';
    const LESSON_INCLASS_TIME      = '%s %s-%s';
    const HOMEWORK_FULL_MARKS_CODE = 'S';

    private $tab;
    private $studentUid;
    private $personUid;
    private $courseId;
    private $contractId;
    private $contractType;
    private $leadsId;
    private $assistantUid;
    private $isLpcCourse = false;
    private $courseLessonInfos;
    private $lessonList  = [];
    private $lessonIds;
    private $department;
    //预习
    private $lessonPreviewInfo;
    //巩固练习
    private $homeworkIsOpenInfos;
    //请假数据
    private $assistantLessonStudentInfos;
    //已经结束章节的id
    private $endLessonIds;
    //das数据
    private $dasStudentLessonInfos;
    //学分数据
    private $scoreInfos;
    //lu
    private $luData;
    private $luCommonData;
    //lesson数据
    private $lessonData;
    //回放在线时长
    private $playbackOnlineTime;
    //试卷总数
    private $examTotalNum;
    //展示显示的lessonId
    private $showLessonIds = [];
    //小学语文获取章节巩固练习作文题配置信息
    private $lessonCompositionConf;
    //小学语文获取章节巩固练习作文题配置信息标志
    private $hasCompositionReport;
    //iLab信息
    private $previewInfoByIlab     = [];
    private $homeworkInfoByIlab    = [];
    private $inclassTestInfoByIlab = [];
    private $homeworkInfoByNomal   = [];
    private $checkIlabLesson       = [];
    //批改
    private $lessonNeedAuditMap = [];
    //试卷绑定情况
    private $hwBindExams = [];
    //月考报告
    private $arrMonthlyExamReportLesson = [];

    //是否有订正逻辑
    private $isAmendCourse;
    //辅导融合直播间互动题总数
    private $mixInteractionTotalNum = [];

    //Lpc独有
    //lpc lu数据
    private $lpcLUData = [];
    //lpc巩固练习
    private $lpcLessonStrengthPractice = [];
    //主讲信息
    private $lessonTeacherMap = [];
    //融合课中互动题总题数
    private $dimLpcLessonCommonWithLessonId = [];
    //年级id
    private $gradeId;
    //课堂报告
    private $lessonReport = [];
    //小鹿数据
    private $deerData = [];
    //遍历章节的index
    private $k;
    private $objCourseRecordSchema;


    //header
    private $header       = [];
    private $headerConfig = [];

    private $mapHeaderFunc = [
        AssistantDesk_Data_CoreDataHeader::HEADER_LESSONNAME                       => 'getLessonName',
        AssistantDesk_Data_CoreDataHeader::HEADER_STARTTIME                        => '',
        AssistantDesk_Data_CoreDataHeader::HEADER_PREVIEW                          => 'getPreview',
        AssistantDesk_Data_CoreDataHeader::HEADER_ATTEND                           => 'getAttendData',
        AssistantDesk_Data_CoreDataHeader::HEADER_PLAYBACK                         => 'getPlayback',
        AssistantDesk_Data_CoreDataHeader::HEADER_PLAYBACK_V1                      => 'getPlaybackOnlineTimeV1',
        AssistantDesk_Data_CoreDataHeader::HEADER_LBPATTENDDURATION                => 'getLbpAttendDuration',
        AssistantDesk_Data_CoreDataHeader::HEADER_LBPATTENDDURATIONOLD             => 'getLbpAttendDurationOld',
        AssistantDesk_Data_CoreDataHeader::HEADER_INCLASSTEST                      => 'getInclassTest',
        AssistantDesk_Data_CoreDataHeader::HEADER_ORALQUESTION                     => 'getOralQuestion',
        AssistantDesk_Data_CoreDataHeader::HEADER_HOMEWORK                         => 'getHomeworkData',
        AssistantDesk_Data_CoreDataHeader::HEADER_HOMEWORK_LIKE                    => 'getHomeworkLikeData',
        AssistantDesk_Data_CoreDataHeader::HEADER_EXERCISE                         => 'getExerciseColumn',
        AssistantDesk_Data_CoreDataHeader::HEADER_EXERCISEALL                      => 'getExerciseAllColumn',
        AssistantDesk_Data_CoreDataHeader::HEADER_LBPINTERACTEXAM                  => 'getLbpInteractExamColumn',
        AssistantDesk_Data_CoreDataHeader::HEADER_MIX_PLAYBACK_INTERACT            => 'getMixPlaybackInteract',
        AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS => 'getLittleKidFudaoData',
        AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL  => 'getLittleKidFudaoData',
        AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_INTERACT        => 'getLittleKidInteractData',
        AssistantDesk_Data_CoreDataHeader::HEADER_SYNCHRONOUSPRACTICE              => 'getSynchronousPractice',
        AssistantDesk_Data_CoreDataHeader::HEADER_HASCOMPOSITIONREPORT             => 'getHasCompositionReportData',
        AssistantDesk_Data_CoreDataHeader::HEADER_TALK                             => 'getTalk',
        AssistantDesk_Data_CoreDataHeader::HEADER_SCORE                            => 'getScoreData',
        AssistantDesk_Data_CoreDataHeader::HEADER_MONTHLYEXAMREPORT                => 'getMonthlyExamReportUrl',
        //Lpc
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_LESSONNAME                   => 'getLpcLessonName',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_TEACHERNAME                  => 'getLpcTeacherName',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_ATTENDSTATUS                 => 'getLpcAttendStatus',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_FINISHSTATUS                 => 'getLpcFinishStatus',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_PLAYSTATUS                   => 'getLpcPlayStatus',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_PREVIEW                      => 'getLpcPreViewData',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_TANGTANGEXAMSTAT             => 'getLpcTangTangExamStatData',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_STRENGTHPRACTICE             => 'getLpcStrengthPracticeData',
        AssistantDesk_Data_CoreDataHeader::HEADER_LPC_LESSONREPORTURL              => 'getLpcLessonReportData',
        AssistantDesk_Data_CoreDataHeader::HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL     => 'getDeerEloquenceHomeworkLevel',
        AssistantDesk_Data_CoreDataHeader::HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL  => 'getDeerProgrammingHomeworkLevel',
        AssistantDesk_Data_CoreDataHeader::HEADER_LESSON_REPORT                    => 'getDeerLessonReport',
        AssistantDesk_Data_CoreDataHeader::HEADER_LESSON_HOMEWORK                  => 'getLessonHomeWork',
        AssistantDesk_Data_CoreDataHeader::HEADER_ZHIBO_LESSON_REPORT              => 'getZhiboLessonReport',
    ];

    public function __construct() {
        $this->objCourseRecordSchema = new Dao_DetailConfig_CourseRecordSchema();
    }

    /**
     * @param $arrInput
     * @return array
     * @throws Laxin_Util_Exception
     */
    public function execute($arrInput) {
        AssistantDesk_Data_ParDeal::dealApi($arrInput);
        $studentUid = (int)$arrInput['studentUid'];
        $personUid  = (int)$arrInput['personUid'];
        $courseId   = (int)$arrInput['courseId'];
        $contractId = (int)$arrInput['contractId'];
        $contractType = (int)$arrInput['contractType'];
        $leadsId    = (int)$arrInput['leadsId'];
        $tab        = $arrInput['tab'];
        $isExport   = $arrInput['isExport'];        //是否导出

        if ($studentUid <= 0 || $courseId <= 0) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误');

        }
        if (empty($tab)) {
            $tab = AssistantDesk_Data_CourseRecordTabs::TAB_CORE_DATA;
        }

        if (empty($leadsId)) {
            $leads = Api_Contract_ContractAllocate::getLeadByStuAndContract($contractId, $studentUid);
            $leadsId = $leads['leadsId'];
            if (empty($leadsId)) {
                throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '获取leadsId失败');
            }
            Bd_Log::notice('PerformanceV1,get_leadsId:' . $leadsId);

        }

        $this->studentUid   = $studentUid;
        $this->personUid    = $personUid;
        $this->courseId     = $courseId;
        $this->contractId   = $contractId;
        $this->contractType = $contractType;
        $this->leadsId      = $leadsId;
        $this->assistantUid = (int)$arrInput['assistantUid'];
        $this->tab          = $tab;
        $this->initCourseLessonInfos();
        $this->initLessonList();


        //试卷数据单独处理
        if ($this->tab == Service_Page_DeskV1_Student_CourseRecord::TAB_EXAM) {
            $tableData = (new Service_Page_DeskV1_Student_GetExamTestList())->execute(['studentUid' => $studentUid,
                'courseId'   => $courseId]);
            $this->setExamDataHeader();
            return ['tableData'   => $tableData,
                'tableHeader' => $this->header];
        }

        //初始化数
        $this->initData();
        //设置标题
        $this->setAllHeader();


        //格式化数据
        $formatedData = $this->formatStudentLessonTableInfo();

        Bd_Log::notice('isExport:' . $isExport);

        //处理导出
        if ($isExport) {
            $lessonList = $this->doExport($formatedData);
            AssistantDesk_Tools::genCsv($lessonList);
            exit;
        }

        return $formatedData;
    }

    private function setHeader($key) {
        if (isset(AssistantDesk_Data_CoreDataHeader::$headerMap[$key])) {
            $this->header[] = AssistantDesk_Data_CoreDataHeader::$headerMap[$key];
        }
    }

    //试卷数据
    private function setExamDataHeader() {
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_TESTTYPE);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_EXAMCOUNT);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_PASSRULE);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_MAXANSWERTIME);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_ANSWERTIME);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_RIGHTCOUNT);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_EXAM_SCORE);
        $this->setHeader(AssistantDesk_Data_CoreDataHeader::HEADER_REPORTURL);

    }

    static $notExportFields = ['isSubmitLessonWork', 'isGenerateLessonReport'];
    private function doExport($lessonData) {
        $header = array_column($this->header, null, 'prop');
        foreach (self::$notExportFields as $field) {
            unset($header[$field]);
        }
        $titles = array_column($header, 'label');

        $lessonList = [];
        $lessonData = isset($lessonData['tableData']) ? $lessonData['tableData'] : [];

        $lessonData = Tools_Array::sortByMultiCols($lessonData, array('startTime' => SORT_ASC));

        Bd_Log::notice("lessonData" . json_encode($lessonData));
        foreach ($lessonData as $key => $val) {
            $row = [];
            foreach ($this->header as $one) {
                // 不支持导出的字段
                if (in_array($one['prop'], self::$notExportFields)) {
                    continue;
                }
                if (in_array($one['prop'], ['preview', 'inclassTest', 'homework', 'oralQuestion', 'similarHomework'])) {
                    $row[] = $val[$one['prop']][0];
                    continue;
                }
                if ($one['prop'] == 'startTime') {
                    $row[] = date("Y-m-d H:i:s", $val[$one['prop']]);
                    continue;
                }
                $row[] = $val[$one['prop']];
            }

            $lessonList[] = $row;
        }

        array_unshift($lessonList, $titles);
        Bd_Log::notice("lessonList" . json_encode($lessonList));

        return $lessonList;
    }

    private function doExportLittleKid($lessonData) {
        $titles     = [
            '章节',
            '上课时间',
            '到课时长',
            '作业成绩',
            '作业提交状态',
            '互动题对答总',
        ];
        $lessonList = [];
        $lessonData = isset($lessonData['tableData']) ? $lessonData['tableData'] : [];

        $lessonData = Tools_Array::sortByMultiCols($lessonData, array('startTime' => SORT_ASC));

        Bd_Log::notice("lessonData" . json_encode($lessonData));
        foreach ($lessonData as $key => $val) {
            $row = [];

            $row[] = $val['lessonName'];
            $row[] = $val['inclassTime'];
            $row[] = $val['attend'];
            $row[] = $val[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL];
            $row[] = $val[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS];
            $row[] = $val[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_INTERACT];

            $lessonList[] = $row;
        }

        array_unshift($lessonList, $titles);
        Bd_Log::notice("lessonList" . json_encode($lessonList));

        return $lessonList;
    }

    private function initData() {
        $this->initEndLessonIds();
        $this->initExamTotalNum();
        $this->initCommonLuData();
        $this->initDasStudentLessonInfos();

        if ($this->isLpcCourse) {

            $this->initGradeId();
            $this->initLpcLUData();
            $this->initLpcLessonStrengthPractice();
            $this->initLessonTeacherMap();
            $this->initDimLpcLessonCommon();
            $this->initLessonReport();
            $this->initDeerData();
        } else {

            $this->initPreviewIsOpenInfos();
            $this->initHomeworkIsOpenInfos();
            $this->initAssistantLessonStudentInfos();
            $this->initScoreInfos();
            $this->initLuData();
            $this->initLessonData();
            $this->initPlaybackOnlineTime();
            $this->initShowLessonIds();
            $this->initLessonCompositionConf();
            $this->initILabInfo();
            $this->initIsAmendCourse();
            $this->initLessonNeedAuditMap();
            $this->initHwBindExams();
            $this->initArrMonthlyExamReportLesson();
            $this->initMixInteractionTotalNum();
            $this->initLessonReport();
        }

        $headerConfig = $this->getShouldersAndTagsAndTabsByCourseId();
        if (empty($headerConfig['tabData'][$this->tab])) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '核心数据未配置');
        }
        $this->headerConfig = $headerConfig['tabData'][$this->tab];
        $this->headerConfig['schemaId'] = $headerConfig['schemaId'];

    }


    public function getShouldersAndTagsAndTabsByCourseId() {
        $courseInfo = Api_Dal::getCourseBaseByCourseIds($this->courseId);
        $contractList = Api_Contract_FwyyContract::getContractList([$this->contractId]);
        if (empty($contractList) || empty($contractList['list'])) {
            return [];
        }
        $contract = current($contractList['list']);
        if (empty($contract)) {
            return [];
        }
        $info    = [
            'courseId'       => $courseInfo['courseId'],
            'grade'          => $courseInfo['mainGradeId'],
            'subject'        => $courseInfo['mainSubjectId'],
            'newCourseType'  => $courseInfo['newCourseType'],
            'source'         => $courseInfo['source'],
            'coursePriceTag' => $contract['priceTag'],
        ];
        $configs = (new AssistantDesk_Data_CourseRecordConfig)->getShouldersAndTagsAndTabs([$info]);
        if (empty($configs)) {
            return [];
        }

        $headerConfig = $configs[$this->courseId];
        return $headerConfig;
    }

    // public function getMapCourseId2Schema($contractInfos) {
    //     if (empty($contractInfos)) {
    //         return [];
    //     }
    //     $course2SchemaId        = [];
    //     $courseId2Schema        = [];
    //     $courseIds              = array_column($contractInfos, 'courseId');
    //     $recordSchemaCourseList = $this->objCourseRecordSchema->getListByConds([
    //         'type'   => Service_Data_DetailConfig_CourseRecordSchema::TYPE_CONTRACT,
    //         'status' => Service_Data_DetailConfig_CourseRecordSchema::STATUS_IS_ONLINE,
    //     ], $this->objCourseRecordSchema->getFields());
    //     if (!empty($recordSchemaCourseList)) {
    //         foreach ($courseIds as $courseId) {
    //             foreach ($recordSchemaCourseList as $v) {
    //                 if (in_array($courseId, explode(',', $v['courseIds']))) {
    //                     $tmpSchema                   = @json_decode($v['schema'], true);
    //                     $tmpSchema['schemaId']       = $v['schemaId'];
    //                     $courseId2Schema[$courseId]  = $tmpSchema;
    //                     $course2SchemaId [$courseId] = $v['schemaId'];
    //                     break;
    //                 }
    //             }
    //         }

    //     }
    //     Bd_Log::notice('find_schema_by_course_ids:' . json_encode(array_keys($courseId2Schema)));
    //     $findedCourseIds    = array_keys($courseId2Schema);
    //     $notFindCourseInfos = [];
    //     foreach ($contractInfos as $v) {
    //         if (!in_array($v['courseId'], $findedCourseIds)) {
    //             $notFindCourseInfos[] = $v;
    //         }
    //     }
    //     Bd_Log::notice('not_find_schema_by_course_ids:' . json_encode(array_column($notFindCourseInfos, 'courseId')));
    //     if (empty($notFindCourseInfos)) {
    //         Bd_Log::notice('course2SchemaId:' . json_encode($course2SchemaId));
    //         return $courseId2Schema;
    //     }

    //     $sources            = array_unique(array_column($notFindCourseInfos, 'source'));
    //     $arrConds           = [];
    //     $arrConds[]         = 'schema_id > 0';
    //     $arrConds['status'] = Service_Data_DetailConfig_CourseRecordSchema::STATUS_IS_ONLINE;
    //     $arrConds['type']   = Service_Data_DetailConfig_CourseRecordSchema::TYPE_RULE;
    //     $arrConds[]         = 'business_line_id in (' . implode(',', $sources) . ')';

    //     $courseRecordSchemaList = $this->objCourseRecordSchema->getListByConds($arrConds, $this->objCourseRecordSchema->getFields());
    //     if (empty($courseRecordSchemaList)) {
    //         return [];
    //     }
    //     $mapSourceSchemaList = Tools_Array::groupByColumn($courseRecordSchemaList, 'businessLineId');
    //     foreach ($notFindCourseInfos as $v) {
    //         $grade          = $v['grade'];
    //         $subject        = $v['subject'];
    //         $source         = $v['source'];
    //         $newCourseType  = $v['newCourseType'];
    //         $coursePriceTag = $v['coursePriceTag'];

    //         if (!isset($mapSourceSchemaList[$source])) {
    //             continue;
    //         }
    //         foreach ($mapSourceSchemaList[$source] as $val) {
    //             if (!in_array($newCourseType, explode(',', $val['newCourseTypes']))) {
    //                 continue;
    //             }
    //             if (!in_array($grade, explode(',', $val['grades']))) {
    //                 continue;
    //             }
    //             if (!in_array($subject, explode(',', $val['subjects']))) {
    //                 continue;
    //             }
    //             //上线之前把所有课程性质都刷上
    //             if (!in_array($coursePriceTag, explode(',', $val['coursePriceTags']))) {
    //                 continue;
    //             }
    //             $tmpSchema                       = @json_decode($val['schema'], true);
    //             $tmpSchema['schemaId']           = $val['schemaId'];
    //             $courseId2Schema[$v['courseId']] = $tmpSchema;
    //             $course2SchemaId[$v['courseId']] = $val['schemaId'];

    //             break;
    //         }
    //     }
    //     Bd_Log::notice('find_by_rule:' . json_encode($courseId2Schema));
    //     Bd_Log::notice('course2SchemaId:' . json_encode($course2SchemaId));
    //     return $courseId2Schema;
    // }

    /**
     * 标题
     */
    private function setAllHeader() {
        $headers = $this->headerConfig['headers'];
        if (empty($headers)) {
            return;
        }
        foreach ($headers as $v) {
            $this->setHeaderByConfig($v);
        }
    }

    private function setHeaderByConfig($config) {
        $arkKeys = [];
        if (isset(AssistantDesk_Data_CoreDataHeader::$headerMap[$config['value']])) {
            $header = AssistantDesk_Data_CoreDataHeader::$headerMap[$config['value']];
            if (!empty($config['customName'])) {
                $header['label'] = $config['customName'];
            }
            if (!empty($config['width'])) {
                $header['width'] = $config['width'];
            }
            if (!empty($config['hover'])) {
                $header['hover'] = $config['hover'];
            }
            $this->header[] = $header;
        } else {
            $arkKeys[] = $config['value'];
        }
        if (!empty($arkKeys)) {
            $rules = AssistantDesk_Data_ArkConfigData::getRulesByApps(AssistantDesk_Ark_ArkConfig::ARK_APP_DETAIL_COLUMN);
            if (empty($rules)) {
                return;
            }
            $rules = Tools_Array::getNewKeyArray($rules, 'key');
            foreach ($arkKeys as $v) {
                if (!isset($rules[$v])) {
                    continue;
                }
                $this->header[] = [
                    'label'   => $rules[$v]['customName'],
                    'cname'   => 'CommonText',
                    'prop'    => $v,
                    'sort'    => 0,
                    'hover'   => '',
                    'width'   => 100,
                    'fixed'   => false,
                    'tooltip' => false,
                    'remark'  => '方舟',
                ];
            }
        }
    }


    private
    function initCourseLessonInfos() {
        $courseLessonInfos = Api_Dal::getCourseLessonInfoByCourseIds($this->courseId);
        if ($courseLessonInfos === false) {
            Bd_Log::warning('dal_getCourseLessonInfoByCourseIds_err');
            $courseLessonInfos = [];
        }
        $this->courseLessonInfos = $courseLessonInfos;
        $this->department        = isset($courseLessonInfos['mainGradeId']) ? Zb_Const_GradeSubject::$GRADEMAPXB[$courseLessonInfos['mainGradeId']] : 0;
        if (AssistantDesk_Data_Course::isLpcByCourse($this->courseId)) {
            $this->isLpcCourse = true;
        }
    }

    private
    function initLessonList() {
        if (empty($this->courseLessonInfos)) {
            return;
        }
        if (empty($this->courseLessonInfos)) {
            return;
        }
        $lessonList = $this->courseLessonInfos['lessonList'];
        if ($lessonList === false) {
            Bd_Log::warning('DAL章节信息获取失败');
            $lessonList = [];
        }
        $lessonList = Tools_Array::sortByMultiCols($lessonList, array(
            'startTime' => SORT_ASC,
        ));
        $lessonIds  = Tools_Array::getNewValueArray($lessonList, 'lessonId');
        $lessonIds  = array_filter($lessonIds);

        if (!$this->isLpcCourse) {
            foreach ($lessonList as $key => $val) {
                $lessonList[$key]['lessonName'] = AssistantDesk_Check::changeLessonName($val['lessonName'], $val['lessonType']);
            }
        }

        $this->lessonList = $lessonList;
        $this->lessonIds  = $lessonIds;
    }

    /**
     * 获取预习数据的 存在状态 开启状态
     * @param $courseId
     * @param $lessonList
     * @param $lessonIds
     * @param $department
     * @return array|string
     */
    private
    function initPreviewIsOpenInfos() {
        $courseId   = $this->courseId;
        $lessonList = $this->lessonList;
        $lessonIds  = $this->lessonIds;
        $department = $this->department;

        if (empty($courseId) || empty($lessonList) || empty($lessonIds) || empty($department)) {
            return;
        }


        $now               = time();
        $lessonPreviewInfo = '';
        switch ($department) {
            case Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL : //新增低幼学部
            case Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY :

                // 小学预习情况
                $bindList = [];
                foreach ($lessonIds as $lessonId) {
                    $bindKey    = 'lesson_' . $lessonId . ":" . Api_Exam::BIND_TYPE_PREVIEW;
                    $bindList[] = $bindKey;
                }
                $examRelationList = Api_Examcore::getRelation($bindList);
                if ($examRelationList === false) {
                    Bd_Log::warning('获取试卷绑定关系信息失败');
                    $examRelationList = [];
                }
                foreach ($lessonIds as $lessonId) {
                    $lessonInfo = $lessonList[$lessonId];

                    //通过是否绑定试卷判断是否有预习
                    $bindKey            = 'lesson_' . $lessonId . ":" . Api_Exam::BIND_TYPE_PREVIEW;
                    $examRelationDetail = $examRelationList[$bindKey];
                    $examIds            = is_array($examRelationDetail) ? array_unique(array_column($examRelationDetail, 'examId')) : [];

                    $row['hasPreview']            = intval($examIds[0]) ? 1 : 0;
                    $row['isOpenPreview']         = $row['hasPreview'] && $now > (strtotime(date("Y-m-d", $lessonInfo['startTime'])) - (7 * 24 * 3600)) ? 1 : 0;
                    $lessonPreviewInfo[$lessonId] = $row;
                }
                break;

            case Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR :
            case Zb_Const_GradeSubject::GRADE_STAGE_SENIOR :
                // 初高中预习情况
                $apiData = Api_Jx::getHighGradePreviewInfo($lessonIds);
                if ($apiData === false) {
                    Bd_Log::warning('初高中预习情况获取失败');
                    $apiData = [];
                }
                foreach ($lessonIds as $lessonId) {
                    $row['hasPreview']            = $apiData[$lessonId]['status'] ? self::PREVIEW_HAS : 0;
                    $row['isOpenPreview']         = $apiData[$lessonId]['isOpen'] ? $apiData[$lessonId]['isOpen'] : 0;
                    $lessonPreviewInfo[$lessonId] = $row;
                }
                break;

            default:
                break;
        }

        $this->lessonPreviewInfo = $lessonPreviewInfo;
        Bd_Log::notice('lessonPreviewInfo:' . json_encode($lessonPreviewInfo));
    }

    /**
     * 获取巩固练习开启状态
     * @param $courseId
     * @param $lessonList
     * @param $lessonIds
     * @return array
     */
    private
    function initHomeworkIsOpenInfos() {
        $courseId   = $this->courseId;
        $lessonList = $this->lessonList;
        $lessonIds  = $this->lessonIds;
        if (empty($lessonIds) || empty($lessonList) || empty($lessonIds)) {
            return;
        }

        $courseLessonList = [];
        foreach ($lessonList as $lessonInfo) {
            $courseLessonList[$courseId][$lessonInfo['lessonId']]['stopTime'] = $lessonInfo['stopTime'];
        }

        $apiData = Api_Exam::getHomeworkOpenTimeByCourseIds($courseLessonList);
        if ($apiData === false) {
            Bd_Log::warning('Api_Exam_getHomeworkOpenTimeByCourseIds_err:' . json_encode($courseLessonList));
            $apiData = [];
        }
        $homeworkIsOpenInfos = [];
        foreach ($lessonIds as $lessonId) {
            $row['isOpenHomework']          = isset($apiData[$lessonId]['isOpen']) ? $apiData[$lessonId]['isOpen'] : 0;
            $homeworkIsOpenInfos[$lessonId] = $row;
        }

        $this->homeworkIsOpenInfos = $homeworkIsOpenInfos;
    }

    private
    function initAssistantLessonStudentInfos() {
        if (empty($this->courseId) || empty($this->studentUid)) {
            return;
        }
        //获取学生该课程下所有的章节请假记录
        $arrConds                    = [
            'courseId'   => $this->courseId,
            'studentUid' => $this->studentUid,
        ];
        $assistantLessonStudentInfos = (new Service_Data_LessonStudent())->getListByConds($this->courseId, $arrConds);
        if (false === $assistantLessonStudentInfos) {
            Bd_Log::warning('获取学生该课程下所有的章节请假记录失败');
            $assistantLessonStudentInfos = [];
        }
        $this->assistantLessonStudentInfos = $assistantLessonStudentInfos ? AssistantDesk_Tools::getNewKeyArray($assistantLessonStudentInfos, 'lessonId') : [];

    }

    private
    function initEndLessonIds() {
        if (empty($this->lessonList)) {
            return;
        }
        //获取所有的章节信息并按照开始时间降序排列
        $nowTime      = time();
        $endLessonIds = array();
        foreach ($this->lessonList as $val) {
            if ($nowTime >= $val['stopTime']) {
                $endLessonIds[] = $val['lessonId'];
            }
        }
        $this->endLessonIds = $endLessonIds;
    }

    private
    function initDasStudentLessonInfos() {
        if (empty($this->studentUid) || empty($this->endLessonIds)) {
            return;
        }
        $studentLessonInfos = Api_Das::getStudentLesson(array($this->studentUid), $this->endLessonIds);

        if ($studentLessonInfos === false) {
            Bd_Log::warning('学生章节汇聚数据获取失败');
            $studentLessonInfos = [];
        }
        $this->dasStudentLessonInfos = $studentLessonInfos[$this->studentUid] ?: [];
    }

    private
    function initScoreInfos() {
        if (empty($this->studentUid) || empty($this->endLessonIds)) {
            return;
        }
        //获取学分
        $scoreInfos = Api_Jxdascore::getStudentLessonsScore($this->studentUid, $this->endLessonIds);
        if ($scoreInfos === false) {
            Bd_Log::warning('获取学分失败');
            $scoreInfos = [];
        }
        $this->scoreInfos = $scoreInfos[$this->studentUid] ?: [];
    }

    private
    function initLuData() {

        if (empty($this->courseId) || empty($this->studentUid)) {
            return;
        }

        // 预习数据 lu数据
        $fields            = [
            "tradeStatus",
            "lessonId",
            "homeworkLevel",
            "attendDuration",
            "chatNum",
            "exerciseParticipateNum",
            "exerciseRightNum",
            "homeworkPracticeCorrectNum",
            "homeworkPracticeParticipateNum",
            "hxPretestFinishnum",
            "isTangTangExamSubmit",
            "lessonOneScore",
            "lessonOneSubmitStatus",
            "lessonThreeScore",
            "lessonThreeSubmitStatus",
            "lessonTwoScore",
            "lessonTwoSubmitStatus",
            "micNum",
            "oralQuestionCorrectTime",
            "oralQuestionSubmit",
            "playbackParticipateNum",
            "playbackRightNum",
            "playbackTimeAfterUnlock",
            "playbackTimeAfterUnlock7d",
            "playbackTimeIn7d",
            "playbackTotalTime",
            "postclassAttendDuration",
            "powerChallengeScore",
            "powerChallengeSubmitStatus",
            "praiseNum",
            "preclassAttendDuration",
            "synchronousPracticeCorrectNum",
            "synchronousPracticeParticipateNum",
            "synthesisManoeuvreScore",
            "synthesisManoeuvreSubmitStatus",
            "synthesisShowScore",
            "synthesisShowSubmitStatus",
            "tangTangExamCorrectNum",
            "tangTangExamParticipateNum",
            "tangTangExamScore",
            "tryKnifeScore",
            "previewTotalNum",
            "isPreviewFinish",
            "previewCorrectNum",
            "previewParticipateNum",

            "lbpAttendDuration",
            "lbpInteractionRightNum",
            "lbpInteractionSubmitNum",
            "lbpInteractionSubmitNum",
            //新回放时长
            'inclassTeacherRoomTotalPlaybackTimeV1',
            'mix_live_interaction_right_num',
            'mix_live_interaction_submit_num',
            'mix_playback_interaction_right_num',
            'mix_playback_interaction_submit_num',

            'deer_programming_homework_level',
            'deer_programming_homework_status',
            //相似题字段
            'exam_answer.exam33.correct_level',
            'exam_answer.exam33.last_correct_status',
            'exam_answer.exam33.submit_num',
        ];
        $lessonActionDatas = \Assistant_Common_Service_DataService_Query_LessonStudentData::getListByCourseIdsStudentUids([$this->courseId], [$this->studentUid], $fields);

        if ($lessonActionDatas === false) {
            Bd_Log::warning('获取学生章节lu数据失败');
            $lessonActionDatas = [];
        }
        $lessonActionDatas = $lessonActionDatas && is_array($lessonActionDatas) ? $lessonActionDatas : [];
        $lessonActionDatas = Tools_Array::getNewKeyArray($lessonActionDatas, 'lessonId');
        foreach ($lessonActionDatas as $k => $v) {
            if ($v['tradeStatus'] != 1) {
                unset($lessonActionDatas[$k]);
            }
        }

        $this->luData = $lessonActionDatas;
    }

    function initCommonLuData() {

        if (empty($this->courseId) || empty($this->studentUid) || empty($this->lessonIds)) {
            return;
        }

        // 预习数据 lu数据
        $fields            = [
            "lesson_id",
            "inclass_right_cnt",
            "inclass_participate_cnt",
            "playback_participate_cnt",
            "playback_right_cnt",
            "inclass_teacher_room_total_playback_time_v1",
            "inclass_teacher_room_total_playback_content_time",
            "is_submit_lesson_work",
            "is_generate_course_report",
            "is_generate_lesson_report",
        ];
        $lessonActionDatas = \Api_DataProxy::getLuComonListByStudentLessons($this->studentUid, $this->lessonIds, $fields);
        if ($lessonActionDatas === false) {
            Bd_Log::warning('获取学生章节lu数据失败');
            $lessonActionDatas = [];
        }
        $lessonActionDatas = $lessonActionDatas && is_array($lessonActionDatas) ? $lessonActionDatas : [];
        $lessonActionDatas = Tools_Array::getNewKeyArray($lessonActionDatas, 'lesson_id');


        $this->luCommonData = $lessonActionDatas;
    }

    private
    function initLessonData() {
        if (empty($this->lessonIds)) {
            return;
        }
        $this->lessonData = AssistantDesk_Data_DataQuery::getLessonData($this->lessonIds, ['lessonId', 'lbpInteractionTotalNum']);

    }

    private
    function initPlaybackOnlineTime() {
        if (empty($this->studentUid) || empty($this->lessonIds)) {
            return;
        }
        //获取回放在线时长
        $this->playbackOnlineTime = Api_JxNotice::getUserLessonsDuration($this->studentUid, $this->lessonIds);

    }

    /**
     * 兼容初二物理ilab巩固练习总体数
     * @param $lessonIds
     * @return array
     * @throws Common_Exception
     */
    private
    function initExamTotalNum() {
        $lessonIds = $this->lessonIds;
        if (empty($lessonIds)) {
            return;
        }

        //获取总题数
        $examTypes = [
            Api_Exam::BIND_TYPE_TEST_IN_CLASS,//堂堂测
            Api_Exam::BIND_TYPE_PRACTICE_IN_CLASS,//互动题
            Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW,//同步练习
            Api_Exam::BIND_TYPE_ORAL_QUESTION,       // 口述题
            Api_Exam::BIND_TYPE_HOMEWORK,            //巩固练习
        ];

        $fields    = [
            'bindId',
            'relationType',
            'bindStatus',
            'totalNum',
            'isArtificialCorrect',

            'examId',
            'examTag',
            'examType',
            'bindType',
        ];
        $examInfos = Assistant_Common_Service_DataService_Query_ExamRelationData::getListByBindIdsBindTypeRelationTypesExamTags($lessonIds, 0, $examTypes, [0], $fields);

        if (false === $examInfos) {
            //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取章节下题目总数数据异常');
            Bd_Log::warning('获取章节下题目总数数据异常');
            $examInfos = [];
        }

        $arrRet = [];
        foreach ($examInfos as $row) {
            if (!isset($arrRet[$row['bindId']])) {
                $arrRet[$row['bindId']] = [];
            }
            if (!isset($arrRet[$row['bindId']][$row['relationType']])) {
                $arrRet[$row['bindId']][$row['relationType']] = [];
            }

            $arrRet[$row['bindId']][$row['relationType']] = $row;
        }

        $this->examTotalNum = $arrRet;
    }


    /**
     * 格式化章节上课表格数据
     * @return array|void
     */
    private
    function formatStudentLessonTableInfo() {

        $lessonList = array_values($this->lessonList);
        $lessonList = Tools_Array::sortByMultiCols($lessonList, ['startTime' => SORT_ASC]);

        $courseId         = $this->courseId;
        $this->lessonList = $lessonList;
        $courseInfo       = $this->courseLessonInfos;
        $studentUid       = $this->studentUid;
        if (empty($courseId) || empty($lessonList) || empty($courseInfo) || empty($studentUid)) {
            return;
        }
        $ret  = array();
        $list = array();

        foreach ($lessonList as $k => $val) {
            $this->k  = $k;
            $row      = [
                'lessonId'           => $val['lessonId'],
                'type'               => $val['lessonType'],
                'startTime'          => $val['startTime'],
                'stopTime'           => $val['stopTime'],
                'inclassTime'        => sprintf(self::LESSON_INCLASS_TIME, date('Y-m-d', $val['startTime']), date('H:i', $val['startTime']), date('H:i', $val['stopTime'])),
                'lessonReportUrl'    => [],
                'lessonReportStatus' => 0,
                'hasQuestion'        => 0,
                'hasPreview'         => 0,
            ];
            $lessonId = $val['lessonId'];

            //lpc前端不清楚哪个字段会使用，暂时都执行，后面再详细对
            $this->getLpcStrengthPracticeStatusData($row, $val);
            $this->getLpcInClassHandsUpNum($row, $val);
            $this->getLpcInClassVideoLinkNum($row, $val);
            $this->getLpcLessonType($row, $val);
            $this->getLpcTeacherName($row, $val);
            $this->getLpcLessonStartStatus($row, $val);
            $this->getLpcHasQuestion($row, $val);
            $this->getLpcHasPreview($row, $val);

            $headers = $this->headerConfig['headers'];
            if (!empty($headers)) {
                $arkHeaders = [];
                foreach ($headers as $v) {
                    if (isset($this->mapHeaderFunc[$v['value']])) {
                        $func = $this->mapHeaderFunc[$v['value']];
                        if (empty($func)) {
                            continue;
                        }
                        $this->$func($row, $val);
                    } else {
                        $arkHeaders[] = $v['value'];
                    }
                }
                $rules = AssistantDesk_Data_ArkConfigData::getRulesByApps(AssistantDesk_Ark_ArkConfig::ARK_APP_DETAIL_COLUMN);
                if (!empty($rules) && !empty($arkHeaders)) {
                    $rules      = Tools_Array::getNewKeyArray($rules, 'key');
                    $arkHeaders = array_intersect(array_keys($rules), $arkHeaders);
                    $needKeys   = $arkHeaders;
                    AssistantDesk_Data_CommonParams::setLeadsIdMapStudentUid([$this->leadsId => $this->studentUid]);
                    AssistantDesk_Data_CommonParams::setLessonId($val['lessonId']);
                    AssistantDesk_Data_CommonParams::setCourseId($this->courseId);
                    AssistantDesk_Data_CommonParams::setContractId($this->contractId);
                    AssistantDesk_Data_CommonParams::setContractType($this->contractType);
                    AssistantDesk_Data_CommonParams::setAssistantUid($this->assistantUid);
                    Service_Page_Deskcontract_Filter_StudentListFormatTool::beforeFormatAction($needKeys, $rules);
                    $studentData = Service_Page_Deskcontract_Filter_StudentListFormatTool::studentListByNeedKeys($needKeys, $rules);
                    $studentData = $studentData[$this->leadsId];
                    if (!empty($studentData)) {
                        foreach ($needKeys as $oneKey) {
                            $prop = $rules[$oneKey]['feConfig']['prop'] ?? $oneKey;
                            if (!isset($studentData[$prop])) {
                                continue;
                            }
                            if (isset($rules[$oneKey]['filterMap'][$studentData[$prop]])) {
                                $row[$oneKey] = $rules[$oneKey]['filterMap'][$studentData[$prop]];
                            } else {
                                $row[$oneKey] = $studentData[$prop];
                            }
                        }
                    }
                }

            }

            if ($val['t007Tag'] == 1) {
                // todo 007 影子章节 观看互动题默认都能点击
                $row['exerciseAll'] == '-' && $row['exerciseAll'] = "查看详情";
                $row['isShowExerciseAllDetail'] = 1;
            }

            $list[] = $row;
        }

        $ret['schemaId']    = $this->headerConfig['schemaId'];
        $ret['tableData']   = $list;
        $ret['tableHeader'] = $this->header;

        return $ret;
    }

    private
    function initShowLessonIds() {
        if (empty($this->lessonList)) {
            return;
        }
        foreach ($this->lessonList as $val) {
            if ($val['startTime'] > time() + 25 * 86400) {
                continue;
            }
            $this->showLessonIds[] = intval($val['lessonId']);
        }
    }

    private
    function initLessonCompositionConf() {

        if (empty($this->courseLessonInfos) || empty($this->showLessonIds)) {
            return;
        }
        $gradeId       = $this->courseLessonInfos['mainGradeId'];
        $subjectId     = $this->courseLessonInfos['mainSubjectId'];
        $showLessonIds = $this->showLessonIds;

        //小学语文获取章节巩固练习作文题配置信息
        if (!empty($showLessonIds)
            && ((Zb_Const_GradeSubject::$GRADEMAPXB[$gradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY && $subjectId == Zb_Const_GradeSubject::CHINESE)
                || (Zb_Const_GradeSubject::$GRADEMAPXB[$gradeId] == Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR && $subjectId == Zb_Const_GradeSubject::ENGLISH))
        ) {
            $this->lessonCompositionConf = Api_PcAssistant::lessonCompositionConf($showLessonIds);
            $this->hasCompositionReport  = true;
        }

    }

    private
    function initILabInfo() {
        if (empty($this->courseLessonInfos)) {
            return;
        }
        $gradeId   = $this->courseLessonInfos['mainGradeId'];
        $subjectId = $this->courseLessonInfos['mainSubjectId'];
        //处理ilab课程的预习、巩固练习、堂堂测
        $previewInfoByIlab     = [];
        $homeworkInfoByIlab    = [];
        $inclassTestInfoByIlab = [];
        $homeworkInfoByNomal   = [];
        $checkIlabLesson       = [];
        $acceptGrade           = [3, 4];
        $acceptSubject         = [Zb_Const_GradeSubject::PHYSICS];
        if (in_array($gradeId, $acceptGrade) && in_array($subjectId, $acceptSubject)) {
            $checkIlabLesson       = Api_ExamUI::getExamTypeByLessonIds($this->showLessonIds);
            $ilabRet               = $checkIlabLesson ? Api_ExamUI::getLevelByExamTypeUidLessonIds($this->studentUid, [Api_Exam::BIND_TYPE_HOMEWORK_ILAB, Api_Exam::BIND_TYPE_POSTTEST_MORE, Api_Exam::BIND_TYPE_TEST_IN_CLASS, Api_Exam::BIND_TYPE_HOMEWORK], $this->showLessonIds) : [];
            $homeworkInfoByIlab    = $ilabRet[Api_Exam::BIND_TYPE_HOMEWORK_ILAB] ? $ilabRet[Api_Exam::BIND_TYPE_HOMEWORK_ILAB] : [];
            $previewInfoByIlab     = $ilabRet[Api_Exam::BIND_TYPE_POSTTEST_MORE] ? $ilabRet[Api_Exam::BIND_TYPE_POSTTEST_MORE] : [];
            $inclassTestInfoByIlab = $ilabRet[Api_Exam::BIND_TYPE_TEST_IN_CLASS] ? $ilabRet[Api_Exam::BIND_TYPE_TEST_IN_CLASS] : [];
            $homeworkInfoByNomal   = $ilabRet[Api_Exam::BIND_TYPE_HOMEWORK] ? $ilabRet[Api_Exam::BIND_TYPE_HOMEWORK] : [];
        }
        $this->previewInfoByIlab     = $previewInfoByIlab;
        $this->homeworkInfoByIlab    = $homeworkInfoByIlab;
        $this->inclassTestInfoByIlab = $inclassTestInfoByIlab;
        $this->homeworkInfoByNomal   = $homeworkInfoByNomal;
        $this->checkIlabLesson       = $checkIlabLesson;
    }

    private
    function initLessonNeedAuditMap() {
        if (empty($this->courseLessonInfos) || empty($this->courseId) || empty($this->studentUid)) {
            return;
        }
        $lessonNeedAuditMap = [];
        if ($this->isAmendCourse) {
            $lessonNeedAuditList = Api_PcAssistant::getStudentHomeworkLessonNeedAudit($this->courseId, $this->studentUid);
            if ($lessonNeedAuditList === false || !is_array($lessonNeedAuditMap)) {
                //throw new Common_Exception(Common_ExceptionCodes::DESK_API_ERROR, '获取巩固练习待审核章节信息失败');
                Bd_Log::warning('获取巩固练习待审核章节信息失败');
                $lessonNeedAuditList = [];
            }
            $lessonNeedAuditMap = Tools_Array::getNewKeyArray($lessonNeedAuditList, 'lessonId');
        }
        $this->lessonNeedAuditMap = $lessonNeedAuditMap;
    }

    private
    function initHwBindExams() {
        if (empty($this->showLessonIds)) {
            return;
        }
        //获取章节巩固练习绑定情况
        $this->hwBindExams = AssistantDesk_ExamBind::lessonBindExams($this->showLessonIds, Api_Exam::BIND_TYPE_HOMEWORK);
    }

    private
    function initArrMonthlyExamReportLesson() {
        if (empty($this->courseId) || empty($this->showLessonIds)) {
            return;
        }
        // 月考报告
        $this->arrMonthlyExamReportLesson = Service_Data_MonthlyExamReport::getMonthlyExamReportLesson($this->courseId, $this->showLessonIds, $this->courseLessonInfos, $this->examTotalNum);

    }

    private
    function initMixInteractionTotalNum() {
        if (empty($this->showLessonIds)) {
            return;
        }
        $this->mixInteractionTotalNum = AssistantDesk_Data_DataQuery::getLessonData($this->showLessonIds, ['lessonId', 'mix_interaction_total_num']);

    }

//获取互动题数据，数据源es，lu
    private
    function getExerciseColumn(&$row, $currentLessonInfo) {
        $dasStudentLessonInfo = $this->dasStudentLessonInfos[$currentLessonInfo['lessonId']];
        $lessonLuCommonData   = $this->luCommonData[$currentLessonInfo['lessonId']];
        $examTotalInfo        = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $intInteractTotalNum  = $this->getIntInteractTotalNum($examTotalInfo);

        //处理互动题 及 互动题颜色标识
        $row['exercise'] = sprintf(
            self::LESSON_EXERCISE_DETAIL,
            $lessonLuCommonData['inclass_right_cnt'],
            $lessonLuCommonData['inclass_participate_cnt'],
            $intInteractTotalNum
        );
        if (!$dasStudentLessonInfo['isAttended']) {
            $row['exerciseCode'] = 0;//未参与
        } else if ($intInteractTotalNum && ($lessonLuCommonData['inclass_right_cnt'] == $intInteractTotalNum)) {
            $row['exerciseCode'] = 1;//全答全对
        } else {
            $row['exerciseCode'] = 2;//未全答全对
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'exercise', [
            "title"      => "互动题对答总",
            "dataSource" => "正确数：公共数仓lu.inclass_right_cnt, 参与数：公共数仓lu.inclass_participate_cnt",
            "解释"         => "逻辑较复杂：das中isAttended为0则为未参与，exerciseCode则为0，前端会展示为空，否则展示对答总"

        ]);
    }

    private
    function getPlaybackOnlineTime(&$row, $lessonId, $playType) {
        $time = 0;
        if ($this->playbackOnlineTime !== false
            && isset($this->playbackOnlineTime[$lessonId]['attendDuration']['playBack'])) {
            $time = $this->playbackOnlineTime[$lessonId]['attendDuration']['playBack'];

        }
        $row['playbackOnlineTime'] = AssistantDesk_Tools::formatDurationTime($time);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'playbackOnlineTime', [
            "title"      => "回放在线时长",
            "dataSource" => "取自课中接口：http://yapi.zuoyebang.cc/project/2697/interface/api/251753",

        ]);
        //lbp章节，直播章节回放在线时长展示成-
        if ($playType == Api_Dal::PLAY_TYPE_LUBO) {
            $row['playbackOnlineTime'] = '-';
        }
    }

//新回放时长
    private
    function getPlaybackOnlineTimeV1(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];

        $row['playbackV1'] = AssistantDesk_Tools::formatDurationTime(intval($lessonLuData['inclassTeacherRoomTotalPlaybackTimeV1']));;
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'playbackV1', [
            'title'  => '【新回放时长】',
            'source' => 'es:idl_assistant_lesson_student_action 字段, inclass_teacher_room_total_playback_time_v1'

        ]);
    }


//获取浣熊互动题数据，数据源es，lu
    private
    function getExerciseExamColumn(&$row, $currentLessonInfo) {
        $studentLessonInfo   = $this->luData[$currentLessonInfo['lessonId']];
        $examTotalInfo       = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $intInteractTotalNum = $this->getIntInteractTotalNum($examTotalInfo);
        // 浣熊互动题 对/答/总  Dao_Elasticsearch_IDLAssistantLessonStudentAction 数据源字段
        $row['exerciseExam'][0] = $row['exercise'];
        if (!$intInteractTotalNum) {
            $row['exerciseExam'][0] = "-";
            $row['exerciseExam'][1] = ''; //不存在
        } elseif ($intInteractTotalNum && ($studentLessonInfo['exerciseRightNum'] == $intInteractTotalNum)) {
            $row['exerciseExam'][1] = 'green'; //全答全对
        } elseif ($intInteractTotalNum && (0 < $studentLessonInfo['exerciseRightNum'])) {
            $row['exerciseExam'][1] = 'orange'; //未全对
        } elseif ($intInteractTotalNum && !$studentLessonInfo['exerciseParticipateNum']) {
            $row['exerciseExam'][1] = 'red'; //未答题
        }
        $row['exerciseExam'][2] = 0; // 不可点击

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'exerciseExam', [
            "title"      => "浣熊互动题对答总",
            "dataSource" => "正确数：lu.exam_answer.exam1.right_num, 参与数：lu.exam_answer.exam1.participate_num",
            "解释"         => "没有总数展示-"

        ]);
    }

//获取观看互动题数据，不是互动题，数据源es，lu
    private
    function getExerciseAllColumn(&$row, $currentLessonInfo) {
        $lessonLuCommonData  = $this->luCommonData[$currentLessonInfo['lessonId']];
        $examTotalInfo       = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $intInteractTotalNum = $this->getIntInteractTotalNum($examTotalInfo);
        if ($intInteractTotalNum === 0) {
            $row['exerciseAll'] = '-';
        } else {
            $row['exerciseAll'] = sprintf(
                self::LESSON_EXERCISE_DETAIL,
                $lessonLuCommonData['inclass_right_cnt'] + $lessonLuCommonData['playback_right_cnt'],
                $lessonLuCommonData['inclass_participate_cnt'] + $lessonLuCommonData['playback_participate_cnt'],
                $intInteractTotalNum
            );
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'exerciseAll', [
            "title"      => "观看互动题对答总",
            "dataSource" => "参与数：公共数仓lu:playback_participate_num + inclass_right_cnt,正确数： 公共数仓lu:playback_right_num + inclass_right_cnt",
            "解释"         => "总题数为0则为-，否则展示对答总"

        ]);

    }


//获取lbp互动题数据,数据源es，lu
    private
    function getLbpInteractExamColumn(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];

        $intInteractTotalNum = $this->mixInteractionTotalNum[$row['lessonId']]['mix_interaction_total_num'] ?? 0;
        if ($intInteractTotalNum == 0) {
            $row['lbpInteractExam'] = '-';
        } else {
            $row['lbpInteractExam'] = sprintf(
                self::LESSON_EXERCISE_DETAIL,
                $lessonLuData['mix_live_interaction_right_num'],
                $lessonLuData['mix_live_interaction_submit_num'],
                $intInteractTotalNum
            );
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'lbpInteractExam', [
            "title"      => "LBP互动题对答总",
            "dataSource" => "参与数：lu:mix_live_interaction_submit_num,正确数： lu:mix_live_interaction_right_num,总数：ads_zbk_lesson_common:mix_interaction_total_num",
            "解释"         => "总题数为0则为-，否则展示对答总"
        ]);

    }

// 融合直播间回放互动题对/答/总，数据源es，lu
    private
    function getMixPlaybackInteract(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];

        $row['mixPlaybackInteract'] = '-';
        $total                      = $this->mixInteractionTotalNum[$row['lessonId']]['mix_interaction_total_num'] ?? 0;

        if ($total > 0) {
            $row['mixPlaybackInteract'] = sprintf(
                self::LESSON_EXERCISE_DETAIL,
                $lessonLuData['mix_playback_interaction_right_num'],
                $lessonLuData['mix_playback_interaction_submit_num'],
                $total
            );
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(
            $row,
            'mixPlaybackInteract',
            [
                'title'      => '融合回放互动题',
                'dataSource' => '正确数：lu.mix_playback_interaction_right_num，参与数：lu.mix_playback_interaction_submit_num，总数：ads_zbk_lesson_common.mix_interaction_total_num',
                '解释'         => '总题数为0展示-，否则展示对答总'
            ]
        );
    }

    /**
     * 判断是否展示观看互动题详情
     * @param $row
     * @param $studentLessonInfo
     */
    private
    function getShowExerciseAllDetail(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];

        $row['isShowExerciseAllDetail'] = 0;
        if (($lessonLuData['playbackParticipateNum'] + $lessonLuData['exerciseParticipateNum']) > 0) {
            //有做题则展示
            $row['isShowExerciseAllDetail'] = 1;
        }
    }

    /**
     * 判断是LBP否展示互动题详情
     * @param $row
     * @param $studentLessonInfo
     */
    private
    function getShowLbpInteractExamDetail(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];

        $row['isShowLbpInteractExamDetail'] = 0;
        if (isset($lessonLuData['lbpInteractionSubmitNum']) && $lessonLuData['lbpInteractionSubmitNum'] > 0) {
            //有做题则展示
            $row['isShowLbpInteractExamDetail'] = 1;
        }
    }

    /**
     *到课
     */
    private
    function getAttendData(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];
        //合并课前课后课中直播到课时长
        //$attendDutation   = intval($lessonActionData['attendDuration'] + $lessonActionData['preclassAttendDuration'] + $lessonActionData['postclassAttendDuration']);
        //只展示课中到课时长，不包括课前出境到课时长 和 课后出境到课时长 20201111
        $attendDutation = intval($lessonLuData['attendDuration']);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'attend', [
            'title' => '【直播到课时长】',
            '数据源'   => "es:idl_assistant_lesson_student_action 字段：attendDuration",
            '解释'    => "只展示课中到课时长，不包括课前出境到课时长 和 课后出境到课时长",
            "参数"    => "lessonId:" . $currentLessonInfo['lessonId']

        ]);
        //合并课前课后课中直播到课时长
        $row['attend'] = $attendDutation % 60 > 0 ? floor($attendDutation / 60) . 'min' . ($attendDutation % 60) . 's' : floor($attendDutation / 60) . 'min';
        //lbp章节，直播章节到课时长展示成-
        if ($currentLessonInfo['playType'] == Api_Dal::PLAY_TYPE_LUBO) {
            $row['attend']  = '-';
            $attendDutation = 0;
        }
        //到课颜色标识
        if (!intval($row['attend'])) {
            if (intval($this->assistantLessonStudentInfos[$currentLessonInfo['lessonId']]['preAttend']) == Service_Data_LessonStudent::PRE_ATTEND_LEAVE) {
                $row['attendCode']  = 3;//请假
                $row['leaveSeason'] = $this->assistantLessonStudentInfos[$currentLessonInfo['lessonId']]['extData']['leaveSeason'] ?? "";//请假
            } else {
                $row['attendCode'] = 0; //未到
            }
        } else if (30 <= ($attendDutation / 60)) {//到课时长足够30min
            $row['attendCode'] = 1;//到课时长足够30min
        } else {
            $row['attendCode'] = 2;//到课时长不足30min
        }

    }

    /**
     *小鹿扩科作业状态，成绩
     */
    private
    function getLittleKidFudaoData(&$row, $currentLessonInfo) {

        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];
        AssistantDesk_Data_CommentAdd::addCommentArr($row, AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL, [
            'title' => '【小鹿辅导作业成绩】',
            '数据源'   => "es:idl_assistant_lesson_student_action 字段：exam_answer.exam_xlbc.deer_programming_homework_level",
            '解释'    => "",
            '参数'    => "lessonId:" . $currentLessonInfo['lessonId']
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS, [
            'title' => '【小鹿辅导作业状态】',
            '数据源'   => "es:idl_assistant_lesson_student_action 字段：exam_answer.exam_xlbc.deer_programming_homework_status",
            '解释'    => "",
            '参数'    => "lessonId:" . $currentLessonInfo['lessonId']
        ]);
        $levelMap                                                                        = [
            0 => '',
            1 => 'S',
            2 => 'A',
            3 => 'B',
        ];
        $statusMap                                                                       = [
            ""            => "未提交",
            "marked"      => "已完成",
            "un-correct"  => "待订正",
            "assigned"    => "待批改",
            "correct"     => "待重批",
            "un-assigned" => "待分配",
        ];
        $row[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL]  = $levelMap[intval($lessonLuData['deer_programming_homework_level'] ?? 0)] ?? '';
        $row[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS] = $statusMap[strval($lessonLuData['deer_programming_homework_status'] ?? '')] ?? '';
    }

    private
    function getLittleKidInteractData(&$row, $currentLessonInfo) {
        $lessonLuCommonData = $this->luCommonData[$currentLessonInfo['lessonId']];

        $examTotalInfo       = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $intInteractTotalNum = $this->getIntInteractTotalNum($examTotalInfo);
        // todo wiki
        AssistantDesk_Data_CommentAdd::addCommentArr($row, AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_INTERACT, [
            'title' => '【小鹿辅导互动题对答总】',
            '数据源'   => "es:dataware_idl_common_lesson_student 字段：inclass_right_cnt,inclass_participate_cnt 总数 码表totalNum字段",
            '解释'    => "",
        ]);

        $rightNum       = intval($lessonLuCommonData['inclass_right_cnt'] ?? 0);
        $participateNum = intval($lessonLuCommonData['inclass_participate_cnt'] ?? 0);
        if (!$intInteractTotalNum) {
            $str = '-';
        } else {
            $str = "$rightNum/$participateNum/$intInteractTotalNum";
        }
        $row[AssistantDesk_Data_CoreDataHeader::HEADER_LITTLE_KID_FUDAO_INTERACT] = $str;
    }

    private
    function getLessonName(&$row, $currentLessonInfo) {
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'lessonId', [
            'title'      => '【基础信息】',
            'lessonId'   => $currentLessonInfo['lessonId'],
            'courseId'   => $this->courseId,
            'studentUid' => $this->studentUid,
        ]);
        $row['lessonName'] = $currentLessonInfo['playType'] == Api_Dal::PLAY_TYPE_LUBO ? '【录】' . $currentLessonInfo['lessonName'] : $currentLessonInfo['lessonName'];
        $row['lessonName'] = $row['lessonName'] . '[' . $currentLessonInfo['lessonId'] . ']';

    }

    private
    function getLpcLessonName(&$row, $currentLessonInfo) {
        $row['lessonName'] = (string)$currentLessonInfo['lessonName'] ?: '-';
        $row['lessonName'] = $row['lessonName'] . '[' . $currentLessonInfo['lessonId'] . ']';

    }

    private
    function getHomeworkData(&$row, $currentLessonInfo) {
        $lessonLuData         = $this->luData[$currentLessonInfo['lessonId']];
        $dasStudentLessonInfo = $this->dasStudentLessonInfos[$currentLessonInfo['lessonId']];
        //章节是否绑定巩固练习
        $isBindHw = !empty($this->hwBindExams[$currentLessonInfo['lessonId']]);
        //兼容das作业未布置错误问题
        if ($dasStudentLessonInfo['homeworkStatus'] == 0 && $isBindHw) {
            $dasStudentLessonInfo['homeworkStatus'] = 1;
        }

        $examTotalInfo       = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $gradeId             = $this->courseLessonInfos['mainGradeId'];
        $subjectId           = $this->courseLessonInfos['mainSubjectId'];
        $lessonId            = $currentLessonInfo['lessonId'];
        $checkIlabLesson     = $this->checkIlabLesson;
        $homeworkInfoByIlab  = $this->homeworkInfoByIlab;
        $homeworkInfoByNomal = $this->homeworkInfoByNomal;
        //处理作业---根据作业级别判断出ABCDE对应级别、作业颜色标识（包含ilab相关逻辑）
        //0114新增订正逻辑
        if ($this->isAmendCourse) {
            $row['homework'] = ['-', 'gray', 1];
            if (!isset($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework']) || $this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'] !== self::HOMEWORK_OPEN) {
                // 未开放巩固练习
                $row['homework'][0] = '-';
            } elseif
            (!$isBindHw) {
                $row['homework'][0] = '未布置';
            } else
                if ($lessonLuData['homeworkLevel'] > 0) {
                    $row['homework'][0] = $lessonLuData['homeworkLevel'] > 0 ? Api_Das::$homeworkLevelMap[$lessonLuData['homeworkLevel']] : "暂无等级";
                } else if ($dasStudentLessonInfo['homeworkRecorrect'] == 2) {
                    $row['homework'][0] = '待批改';
                    //das订正巩固练习状态为待批改，批改系统巩固练习状态为待审核，则展示为待审核 (待重批作业已经有homeworkLevel评级，因此不展示待审核状态展示)
                    if (isset($this->lessonNeedAuditMap[$currentLessonInfo['lessonId']])) {
                        $row['homework'][0] = '待审核';
                    }
                } else {
                    $row['homework'][0] = '未提交';
                }
        } else {
            $row['homework'] = ['-', 'gray', 1];
            if (!isset($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework']) || $this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'] !== self::HOMEWORK_OPEN) {
                // 未开放巩固练习
                $row['homework'][0] = '-';
            } elseif (!$isBindHw) {
                $row['homework'][0] = '未布置';
            } else if ($dasStudentLessonInfo['homeworkStatus'] == 1) {
                $row['homework'][0] = '未提交';
            } else if ($dasStudentLessonInfo['homeworkStatus'] == 2) {
                $row['homework'][0] = '未批改';
            } else if ($dasStudentLessonInfo['homeworkStatus'] == 3) {
                $row['homework'][0] = $lessonLuData['homeworkLevel'] > 0 ? Api_Das::$homeworkLevelMap[$lessonLuData['homeworkLevel']] : "暂无等级";
            }
        }

        if ((self::HOMEWORK_FULL_MARKS_CODE == $row['homework'][0])) {
            $row['homework'][1] = 'green'; //满分
        }
        if (in_array($row['homework'][0], array_values(Api_Das::$homeworkLevelMap)) && (self::HOMEWORK_FULL_MARKS_CODE != $row['homework'][0])) {
            $row['homework'][1] = 'orange'; //不满分
        }

// 巩固练习兼容ILab相关逻辑
        if ($gradeId == 3 && $subjectId == 4 && $this->checkIlabLesson[$lessonId]['version'] == 2) {
            $row['homework'][0] = Api_ExamUI::$levelIlabMap[intval($homeworkInfoByIlab[$lessonId])] ? Api_ExamUI::$levelIlabMap[intval($homeworkInfoByIlab[$lessonId])] : "-";
            if ($homeworkInfoByIlab[$lessonId] == 1) {
                $row['homework'][1] = 'green'; //满分
            }
            if ($homeworkInfoByIlab[$lessonId] == 2) {
                $row['homework'][1] = 'orange'; //不满分
            }
        }
        if ($gradeId == 3 && $subjectId == 4 && $checkIlabLesson[$lessonId]['version'] == 1) {
            $row['homework'][0] = Api_ExamUI::$levelIlabMap[intval($homeworkInfoByNomal[$lessonId])] ? Api_ExamUI::$levelIlabMap[intval($homeworkInfoByNomal[$lessonId])] : "-";
            if ($homeworkInfoByNomal[$lessonId] == 1) {
                $row['homework'][1] = 'green'; //满分
            }
            if ($homeworkInfoByNomal[$lessonId] == 2) {
                $row['homework'][1] = 'orange'; //不满分
            }
        }

        if ($row['homework'][0] == '-') {
            $row['homework'][2] = 0;
        }

        $row['homeworkNums'] = $examTotalInfo[Api_Exam::BIND_TYPE_HOMEWORK]['totalNum'] ? sprintf(self::LESSON_EXERCISE_DETAIL, $lessonLuData['homeworkPracticeCorrectNum'], $lessonLuData['homeworkPracticeParticipateNum'], $examTotalInfo[Api_Exam::BIND_TYPE_HOMEWORK]['totalNum']) : '-';//巩固练习对答总，用于导出


        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'homework', [
            'title' => '【巩固练习】',
            '解释'    => '此字段逻辑特别复杂，wiki：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=312262586',
        ]);
    }

    /**
     * @param $row
     * @param $currentLessonInfo
     * @param $isBindHw
     * @param $lessonLuData
     * @param $dasStudentLessonInfo
     * @param $examTotalInfo
     */
    private
    function getHomeworkLikeData(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];
        $isBindHw     = !empty($this->hwBindExams[$currentLessonInfo['lessonId']]);

        $correctLevel  = $lessonLuData['exam_answer.exam33.correct_level'];
        $correctStatus = $lessonLuData['exam_answer.exam33.last_correct_status'];

        $row['similarHomework'] = ['-', 'gray', 1];
        if (isset($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'])
            && ($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'] == self::HOMEWORK_OPEN)
            && $isBindHw) {
            if (in_array($correctStatus, AssistantDesk_Config::EXAM_CORRECT_STATUS_WAIT_SHOW_MAP)) {
                $row['similarHomework'][0] = AssistantDesk_Config::$examCorrectStatusMap[$correctStatus] ?? '-';
            } else {
                $row['similarHomework'][0] = Api_Das::$homeworkLevelMap[$correctLevel] ?? '暂无等级';
                $row['similarHomework'][1] = 'orange'; //不满分
            }
            if ((self::HOMEWORK_FULL_MARKS_CODE == $row['similarHomework'][0])) {
                $row['similarHomework'][1] = 'green'; //满分
            }
        } else {
            $row['similarHomework'][2] = 0;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'similarHomework', [
            'title' => '【相似题】',
            '解释'    => '此字段逻辑特别复杂，wiki：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=375289584',
        ]);
    }

    private
    function initIsAmendCourse() {
        if (empty($this->courseLessonInfos)) {
            return;
        }
        $this->isAmendCourse = AssistantDesk_ServiceTools::isHomeworkAmendCourse($this->courseLessonInfos);

    }

    private
    function getHasCompositionReportData(&$row, $currentLessonInfo) {
        $lessonLuData = $this->luData[$currentLessonInfo['lessonId']];
        //小学语文 + 章节巩固练习配置作文题 + 巩固练习已有评级
        $row['hasCompositionReport'] = 0;
        if ($this->hasCompositionReport && !empty($this->lessonCompositionConf[$row['lessonId']]['tidList']) && $lessonLuData['homeworkLevel']) {
            $row['hasCompositionReport'] = 1;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'hasCompositionReport', [
            "title"      => "巩固练习作文",
            "dataSource" => "lu中有homeworkLevel，批改有巩固练习作文配置：/pcassistant/api/composition/getcompositiontid"
        ]);
    }

    private
    function getPlayback(&$row, $currentLessonInfo) {
        $lessonLuData       = $this->luData[$currentLessonInfo['lessonId']];
        $lessonLuCommonData = $this->luCommonData[$currentLessonInfo['lessonId']];
        //处理回放 及 回放颜色标识
        //展示全部回放时长
        //录播处理
        if ($currentLessonInfo['t007Tag'] == 1) {
            $playbackTotalTime = $lessonLuData['playbackTimeAfterUnlock'];
        } else {
            $playbackTotalTime = $lessonLuData['playbackTotalTime'];
        }
        $playbackSecond  = $playbackTotalTime % 60;
        $row['playback'] = floor($playbackTotalTime / 60) . "min";
        if ($playbackSecond) {
            $row['playback'] = $row['playback'] . $playbackSecond . 's';
        }

        //lbp章节，直播章节回放时长展示成-
        if ($currentLessonInfo['playType'] == Api_Dal::PLAY_TYPE_LUBO) {
            $row['playback'] = '-';
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'playback', [
            'title'  => '【回放时长】',
            '录播回访时长' => $lessonLuData['playbackTimeAfterUnlock'] ?? 0,
            '直播回放时长' => $lessonLuData['playbackTotalTime'] ?? 0,
            'source' => 'es:idl_assistant_lesson_student_action 字段playback_time_after_unlock, playback_time'

        ]);

        //新回放时长
        $row['playbackV1'] = AssistantDesk_Tools::formatDurationTime(intval($lessonLuCommonData['inclass_teacher_room_total_playback_time_v1']));;
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'playbackV1', [
            'title'  => '【新回放时长】',
            'source' => 'es:公共数仓lu字段, inclass_teacher_room_total_playback_time_v1'

        ]);
    }


    private
    function getIntInteractTotalNum($examTotalInfo) {
        // 互动题
        $intInteractTotalNum = 0;
        if (isset($examTotalInfo[Api_Exam::BIND_TYPE_PRACTICE_IN_CLASS])) {
            $intInteractTotalNum = $examTotalInfo[Api_Exam::BIND_TYPE_PRACTICE_IN_CLASS]['totalNum'] ?? 0;
        }
        return intval($intInteractTotalNum);

    }

    private
    function getLbpAttendDurationOld(&$row, $currentLessonInfo) {
        $lessonLuData      = $this->luData[$currentLessonInfo['lessonId']];
        $lbpAttendDutation = intval($lessonLuData['lbpAttendDuration']);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'lbpAttendDuration', [
            'title' => '【录播章节内容观看时长】',
            '数据源'   => "es:辅导lu字段：lbpAttendDuration",
            '解释'    => "",
            "参数"    => "lessonId:" . $row['lessonId']

        ]);
        $row['lbpAttendDurationOld'] = AssistantDesk_Tools::formatDurationTime($lbpAttendDutation);
    }

    private
    function getLbpAttendDuration(&$row, $currentLessonInfo) {
        $lessonLuCommonData = $this->luCommonData[$currentLessonInfo['lessonId']];

        $lbpAttendDutation = intval($lessonLuCommonData['inclass_teacher_room_total_playback_content_time']);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'lbpAttendDuration', [
            'title' => '【录播/回放内容观看时长】',
            '数据源'   => "es:公共数仓lu字段：inclass_teacher_room_total_playback_content_time",
            '解释'    => "",
            "参数"    => "lessonId:" . $row['lessonId']

        ]);
        $row['lbpAttendDuration'] = AssistantDesk_Tools::formatDurationTime($lbpAttendDutation);
    }

    private
    function getInclassTest(&$row, $currentLessonInfo) {
        $examTotalInfo = $this->examTotalNum[$currentLessonInfo['lessonId']];
        $lessonLuData  = $this->luData[$currentLessonInfo['lessonId']];
        $gradeId       = $this->courseLessonInfos['mainGradeId'];
        $subjectId     = $this->courseLessonInfos['mainSubjectId'];
        $lessonId      = $row['lessonId'];
        //处理堂堂测 及 颜色标识 (包含兼容ilab部分)
        $tangTangToalNum = ($examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['totalNum'] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['bindStatus']) ?
            intval($examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['totalNum']) : 0;

        $row['inclassTest'] = ['-', 'gray', 1];
        // 小英的堂堂测展示得分

        $row['inclassTest'][0] = sprintf(
            self::LESSON_EXERCISE_DETAIL,
            $lessonLuData['tangTangExamCorrectNum'],
            $lessonLuData['tangTangExamParticipateNum'],
            $tangTangToalNum
        );
        if ($tangTangToalNum && ($lessonLuData['tangTangExamCorrectNum'] == $tangTangToalNum)) {
            $row['inclassTest'][1] = 'green';//满分
        }
        if ($tangTangToalNum && ($lessonLuData['tangTangExamCorrectNum'] < $tangTangToalNum)) {
            $row['inclassTest'][1] = 'orange';//不满分
        }
        //ilab的堂堂测
        if ($gradeId == 3 && $subjectId == 4 && $this->checkIlabLesson[$lessonId]['iLabLesson']) {
            $row['inclassTest'][0] = Api_ExamUI::$levelIlabMap[intval($this->inclassTestInfoByIlab[$lessonId])] ? Api_ExamUI::$levelIlabMap[intval($this->inclassTestInfoByIlab[$lessonId])] : "-";
            if ($this->inclassTestInfoByIlab[$lessonId] == 1) {
                $row['inclassTest'][1] = 'green';//优秀
            }
            if ($this->inclassTestInfoByIlab[$lessonId] == 2) {
                $row['inclassTest'][1] = 'orange';//良好
            }
        }
        if ($row['inclassTest'][0] == '-') {
            $row['inclassTest'][2] = 0;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'inclassTest', [
            "title"      => "堂堂测",
            "dataSource" => "bdl_exam_relation确定总数，lu中tangTangExamCorrectNum和tangTangExamParticipateNum为正确数和参与数"
        ]);
    }

    private
    function getMonthlyExamReportUrl(&$row, $currentLessonInfo) {
        $lessonLuData                = $this->luData[$currentLessonInfo['lessonId']];
        $row['monthlyExamReportUrl'] = '';
        if (isset($this->arrMonthlyExamReportLesson[$row['lessonId']])) {
            $this->hasMonthlyExamReport = true;
            if ($lessonLuData['isTangTangExamSubmit']) {
                $row['monthlyExamReportUrl'] = Service_Data_MonthlyExamReport::getReportUrl(
                    $this->studentUid,
                    $this->courseId,
                    $row['lessonId']
                );
            }
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'monthlyExamReportUrl', [
            "title"      => "月考报告",
            "dataSource" => "月考报告是否配置：Assistant_Dao_AssistantMonthExamConf，lu中isTangTangExamSubmit为1再拼接生成月考报告url"

        ]);
    }

    private
    function getScoreData(&$row, $currentLessonInfo) {
        $scoreInfos = $this->scoreInfos;
        //处理学分
        $row['score'] = intval($scoreInfos[$currentLessonInfo['lessonId']]['score']);//学分
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'score', [
            "title"      => "学分",
            "dataSource" => "学分为教学接口返回：/jxdascore/score/getobjbizscorelist"

        ]);
    }

    private
    function getTalk(&$row, $currentLessonInfo) {
        $lessonLuData         = $this->luData[$currentLessonInfo['lessonId']];
        $dasStudentLessonInfo = $this->dasStudentLessonInfos[$currentLessonInfo['lessonId']];
        //处理课中聊天、课中聊天颜色
        $row['talk'] = $lessonLuData['chatNum'];
        if (!$dasStudentLessonInfo['isAttended']) {
            $row['talkCode'] = 0;//课中聊天颜色
        } else {
            $row['talkCode'] = 1;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'talk', [
            "title"      => "课中聊天",
            "dataSource" => "das中isAttended确定是否聊天，lu中chatNum确定聊天数"

        ]);

    }

    private
    function getPreview(&$row, $currentLessonInfo) {
        $lessonLuData      = $this->luData[$currentLessonInfo['lessonId']];
        $gradeId           = $this->courseLessonInfos['mainGradeId'];
        $subjectId         = $this->courseLessonInfos['mainSubjectId'];
        $lessonId          = $row['lessonId'];
        $row['preview']    = ['-', 'gray', 1];
        $row['preview'][0] = $lessonLuData['previewTotalNum'] ? sprintf(self::LESSON_EXERCISE_DETAIL, $lessonLuData['previewCorrectNum'], $lessonLuData['previewParticipateNum'], $lessonLuData['previewTotalNum']) : '-';
        // 兼容 iLab课程数据信息
        if ($gradeId == 3 && $subjectId == 4 && $this->checkIlabLesson[$lessonId]['iLabLesson']) {
            $row['preview'][0] = Api_ExamUI::$levelIlabMap[intval($this->previewInfoByIlab[$lessonId])] ? Api_ExamUI::$levelIlabMap[intval($this->previewInfoByIlab[$lessonId])] : "-";
            if ($this->previewInfoByIlab[$lessonId] == 1) {
                $row['preview'][1] = 'green';
            }
            if ($this->previewInfoByIlab[$lessonId] == 2) {
                $row['preview'][1] = 'orange';//良好
            }
        }
        // 开启状态 提交状态判断
        if ($row['preview'][0] == '-') {
            $row['preview'][0] = (isset($this->lessonPreviewInfo[$lessonId]['isOpenPreview']) && $this->lessonPreviewInfo[$lessonId]['isOpenPreview'] === self::PREVIEW_OPEN) ? ($this->lessonPreviewInfo[$lessonId]['isPreviewFinish'] ? '0/0/0' : '未提交') : '-';
            $row['preview'][2] = 0;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'preview', [
            "title"                 => "预习对答总",
            "previewTotalNum"       => $lessonLuData['previewTotalNum'] ?? 0,
            "previewCorrectNum"     => $lessonLuData['previewCorrectNum'] ?? 0,
            "previewParticipateNum" => $lessonLuData['previewParticipateNum'] ?? 0,
            "dataSource"            => "exam_answer.exam5.right_num或者exam_answer.exam13.right_num，exam_answer.exam5.participate_num或者exam_answer.exam13.participate_num"

        ]);
    }

    private
    function getSynchronousPractice(&$row, $currentLessonInfo) {
        $lessonLuData  = $this->luData[$currentLessonInfo['lessonId']];
        $examTotalInfo = $this->examTotalNum[$currentLessonInfo['lessonId']];


        //同步练习数据
        $row['synchronousPractice'] = ($examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['totalNum'] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['bindStatus']) ?
            sprintf(self::LESSON_EXERCISE_DETAIL,
                $lessonLuData['synchronousPracticeCorrectNum'],
                $lessonLuData['synchronousPracticeParticipateNum'],
                $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['totalNum']) :
            '-';

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'synchronousPractice', [
            "title"      => "同步练习数据",
            "dataSource" => "bdl_exam_relation确定总数，lu中synchronousPracticeCorrectNum和synchronousPracticeParticipateNum为正确数和参与数"

        ]);
    }

    private
    function getOralQuestion(&$row, $currentLessonInfo) {
        $lessonLuData  = $this->luData[$currentLessonInfo['lessonId']];
        $examTotalInfo = $this->examTotalNum[$currentLessonInfo['lessonId']];
        // 口述题
        $row['oralQuestion']    = ['-', 'gray', 0];
        $intStatus              = AssistantDesk_ServiceTools::getOralQuestionFinishStatus(
            $examTotalInfo[Api_Exam::BIND_TYPE_ORAL_QUESTION],
            $lessonLuData
        );
        $row['oralQuestion'][0] = AssistantDesk_TaskMap::$arrOralQuestionFinishStatus['map'][$intStatus];
        if ((AssistantDesk_TaskMap::ORALQU_STATUS_SUBMIT === $intStatus)
            || (AssistantDesk_TaskMap::ORALQU_STATUS_TB_CORRECTED === $intStatus)
        ) {
            $row['oralQuestion'][1] = 'green';
            $row['oralQuestion'][2] = 1; // 可点击
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'oralQuestion', [
            "title"      => "口述题",
            "dataSource" => "bdl_exam_relation查询口述题是否绑定，lu中oralQuestionSubmit为提交，isArtificialCorrect为1和oralQuestionCorrectTime小于等于0确定待批改"

        ]);
    }

    private
    function initLpcLUData() {
        if (empty($this->courseId) || empty($this->studentUid)) {
            return;
        }
        $field = ['lpc_uid', 'course_id', 'student_uid', 'lesson_id', 'attend_duration', 'leads_id', 'playback_time', 'exam1', 'exam5', 'exam7', 'exam13', 'exam10', 'in_class_video_link_num', 'in_class_hands_up_num', 'is_ai_attend', 'is_ai_finish', 'play_type', 'is_lbp_attend', 'lbp_attend_duration', 'attend', 'is_attend_finish', 'is_unlock_playback_attend', 'is_unlock_playback_finish', 'mix_live_interaction_submit_num', 'mix_live_interaction_right_num'];

        $res = Assistant_Common_Service_DataService_Query_LessonStudentData::getLpcListByCourseStudent($this->courseId, $this->studentUid, $field);
        if ($res === false) {
            Bd_Log::warning('Assistant_Common_Service_DataService_Query_LessonStudentData_getLpcListByCourseStudent_err');
            $res = [];
        }
        $res = array_column($res, null, 'lesson_id');

        $this->lpcLUData = $res;
        Bd_Log::notice("lpc_lu_data:" . json_encode($this->lpcLUData));
    }

    private
    function initGradeId() {
        if (empty($this->courseLessonInfos)) {
            return;
        }
        $this->gradeId = $this->courseLessonInfos['mainGradeId'];
    }

    private
    function initLpcLessonStrengthPractice() {
        if (empty($this->lessonIds)) {
            return;
        }

        //判断章节是否有巩固练习
        $this->lpcLessonStrengthPractice = AssistantDesk_ExamBind::lessonBindExams($this->lessonIds, Api_Exam::BIND_TYPE_HOMEWORK);


    }

    private
    function initLessonTeacherMap() {
        if (empty($this->lessonIds)) {
            return;
        }
        $this->lessonTeacherMap = (new Service_Data_GetTaskList())->getLessonTeacherMap($this->lessonIds);

    }

    private
    function initDimLpcLessonCommon() {
        if (empty($this->lessonIds)) {
            return;
        }
        $dimLpcLessonCommon                   = Assistant_Common_Service_DataService_Query_LessonData::getListByLessonIds($this->lessonIds, ['lesson_id', 'mix_interaction_total_num']);
        $this->dimLpcLessonCommonWithLessonId = $dimLpcLessonCommon ? array_column($dimLpcLessonCommon, null, 'lesson_id') : [];
    }

    /**
     *  获取班主任/销售课堂报告
     */
    private
    function initLessonReport() {
        if (empty($this->lessonIds) || empty($this->studentUid)) {

        }
        $report = Api_Jx::getHxLessonReport($this->lessonIds, $this->studentUid);
        if ($report === false) {
            Bd_Log::warning("Api_Jx_getHxLessonReport_err");
            $report = [];
        }
        $this->lessonReport = $report;
    }


    private
    function initDeerData() {
        if (empty($this->studentUid) || empty($this->lessonIds)) {
            return;
        }
        $obj    = new Dao_Deer_DeerDataLUDeerSpecialLPC();
        $conds  = ['studentUid' => $this->studentUid,
            'lesson_id in (' . implode(',', $this->lessonIds) . ')'];
        $fields = ['lessonId', 'studentUid', 'deerEloquenceHomeworkLevel', 'deerProgrammingHomeworkLevel'];

        $deerData = $obj->getListByConds($conds, $fields);
        Bd_Log::notice('deerData:' . json_encode($deerData));
        if (!empty($deerData)) {
            $deerData       = Tools_Array::getNewKeyArray($deerData, 'lessonId');
            $this->deerData = $deerData;
        }


    }


    /**
     * lpc到课状态
     * @param $item
     * @param $currentLessonInfo
     * @throws Laxin_Util_Exception
     */
    private
    function getLpcAttendStatus(&$item, $currentLessonInfo) {
        $lu              = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        $lessonStartTime = $currentLessonInfo['startTime'];
        $isAttend        = Duxuesc_Utils::getLessonIsAttend($currentLessonInfo['playType'], $lu);

        $item['attendStatus'] = $lessonStartTime > time() ? self::WAIT_STATUS : ($isAttend > 0 ? self::SUCCESS_STATUS : self::UNDONE_STATUS);
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'attendStatus', [
            "title"      => "lpc到课状态",
            "dataSource" => "章节开始时间大于当前时间且根据章节类型确定lu中is_lbp_attend，is_ai_attend，attend某一个为1",
        ]);

    }

    /**
     * Lpc完课状态
     * @param $item
     * @param $currentLessonInfo
     * @return int
     * @throws Laxin_Util_Exception
     */
    private
    function getLpcFinishStatus(&$item, $currentLessonInfo) {
        $lu              = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        $isFinish        = Duxuesc_Utils::getLessonIsFinished($currentLessonInfo['playType'], $lu);
        $lessonStartTime = $currentLessonInfo['startTime'];
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'finishStatus', [
            "title"      => "Lpc完课状态",
            "dataSource" => "章节开始时间大于当前时间且根据章节类型确定lu中is_lbp_attend_finish，is_ai_finish，is_attend_finish某一个为1",
        ]);

        return $item['finishStatus'] = $lessonStartTime > time() ? self::WAIT_STATUS : ($isFinish > 0 ? self::SUCCESS_STATUS : self::UNDONE_STATUS);

    }

    /**
     * Lpc回放数据
     * @param $item
     * @param $currentLessonInfo
     * @return int
     */
    private
    function getLpcPlayStatus(&$item, $currentLessonInfo) {
        $lessonStartTime = $currentLessonInfo['startTime'];
        $playbackTime    = $this->lpcLUData[$currentLessonInfo['lessonId']]['playback_time'] ?? 0;

        if ($lessonStartTime > time()) {
            return self::WAIT_STATUS;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'playStatus', [
            "title"      => "Lpc回放数据",
            "dataSource" => "章节开始时间大约现在且lu中playback_time大于300",
        ]);


        return $item['playStatus'] = $playbackTime >= 300 ? self::SUCCESS_STATUS : self::UNDONE_STATUS;

    }

    /**
     * Lpc预习数据
     * @param $item
     * @param $currentLessonInfo
     */
    private
    function getLpcPreViewData(&$item, $currentLessonInfo) {
        $lu  = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        $key = 'exam5';
        if (in_array($this->gradeId, [2, 3, 4, 20, 5, 6, 7, 30, 50])) {
            $key = 'exam13';
        }

        if (!isset($lu[$key])
            ||
            !isset($lu[$key]['right_num'])
            ||
            !isset($lu[$key]['participate_num'])
            ||
            !isset($lu[$key]['total_num'])
        ) {
            $preViewData = "-";
        } else {
            $preViewData =
                intval($lu[$key]['right_num']) . '|' .
                intval($lu[$key]['participate_num']) . '|' .
                intval($lu[$key]['total_num']);
        }

        $item['preView'] = $preViewData;

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'preView', [
            "title"      => "Lpc预习数据",
            "dataSource" => "lu中exam5或者exam13中，right_num，participate_num，total_num为对答总",
        ]);
    }

    /**
     * 获取Lpc堂堂测状态数据
     * @param $item
     * @param $currentLessonInfo
     */
    private
    function getLpcTangTangExamStatData(&$item, $currentLessonInfo) {
        $lu = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        if (time() < $currentLessonInfo['startTime']) {
            $tangtangExamStat = '-';
        } else if (!empty($lu['exam10']) && $lu['exam10']['is_submit'] == 1) {
            $tangtangExamStat = intval($lu['exam10']['right_num']) . '|' .
                intval($lu['exam10']['participate_num']) . '|' .
                intval($lu['exam10']['total_num']);
        } else {
            $tangtangExamStat = '-';
        }
        $item['tangtangExamStat'] = $tangtangExamStat;

        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'tangtangExamStat', [
            "title"      => "Lpc堂堂测状态数据",
            "dataSource" => "开始时间大于当前时间，lu中exam10中is_submit已提交，对答总：lu中exam10中right_num，participate_num，total_num",
        ]);

    }

    /**
     * 获取Lpc巩固练习数据
     * @param $item
     * @param $currentLessonInfo
     */
    private
    function getLpcStrengthPracticeData(&$item, $currentLessonInfo) {
        $lu = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];

        $item['strengthPractice'] = isset($this->lpcLessonStrengthPractice[$currentLessonInfo['lessonId']]) && ($this->lpcLessonStrengthPractice[$currentLessonInfo['lessonId']]) ? $this->getLessonStrengthPractice($lu) : '未布置';
        AssistantDesk_Data_CommentAdd::addCommentArr($row, 'strengthPractice', [
            "title"      => "Lpc巩固练习数据",
            "dataSource" => "绑定巩固练习且lu中exam7.is_submit，exam7.correct_status确定批改状态,exam7.correct_level确定评级",
        ]);
    }

    /**
     * 获取lpc巩固练习状态
     * @param $item
     * @param $currentLessonInfo
     */
    private
    function getLpcStrengthPracticeStatusData(&$item, $currentLessonInfo) {
        $lu = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];

        $item['strengthPracticeStatus'] = isset($this->lpcLessonStrengthPractice[$currentLessonInfo['lessonId']]) && ($this->lpcLessonStrengthPractice[$currentLessonInfo['lessonId']]) ? $this->getLessonStrengthPracticeStatus($lu) : AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_NOHAVA;
    }

    private
    function getLpcInClassHandsUpNum(&$item, $currentLessonInfo) {
        $lu                        = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        $item['inClassHandsUpNum'] = isset($lu['in_class_hands_up_num']) ? $lu['in_class_hands_up_num'] : 0;

    }

    private
    function getLpcInClassVideoLinkNum(&$item, $currentLessonInfo) {
        $lu                          = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        $item['inClassVideoLinkNum'] = isset($lu['in_class_video_link_num']) ? $lu['in_class_video_link_num'] : 0;

    }

    private
    function getLpcLessonType(&$item, $currentLessonInfo) {
        $item['lessonType'] = in_array($currentLessonInfo['playType'], [3, 5]) ? 2 : 1;
    }

    private
    function getLpcTeacherName(&$item, $currentLessonInfo) {
        $item['teacherName'] = !empty($this->lessonTeacherMap[$currentLessonInfo['lessonId']]) ? $this->lessonTeacherMap[$currentLessonInfo['lessonId']] : '-';
    }

    private
    function getLpcLessonStartStatus(&$item, $currentLessonInfo) {
        // 判断章节状态 1即将开课 2正在开课 3已结束 0未开课
        $item['lessonStartStatus'] = $this->getLessonStartStatus($currentLessonInfo['startTime'], $currentLessonInfo['stopTime'], $this->lessonList, $this->k);

    }

    /**
     * 获取章节上课状态
     *
     * @param        $startTime
     * @param        $stopTime
     * @param        $lessonList
     * @param int $index
     *
     * @return int
     */
    private
    function getLessonStartStatus($startTime, $stopTime, $lessonList, $index = 0) {
        if (empty($startTime) || empty($stopTime) || empty($lessonList) || !is_array($lessonList)) {
            return self::LESSON_START_STATUS_NOT;
        }
        $now = time();
        // 判断即将开课，case index为第二章节 则获取上一节课 结束时间判断是否结束
        if ($index > 0) {
            $prevLessonInfo = $lessonList[$index - 1];
            if (!empty($prevLessonInfo['stopTime']) && (int)$prevLessonInfo['stopTime'] < $now && $startTime < $now && $stopTime > $now) {
                return self::LESSON_START_STATUS_SOON;
            }
        } else {
            if ($now < $startTime) {
                return self::LESSON_START_STATUS_SOON;
            }
        }

        if ($startTime <= $now && $stopTime >= $now) {
            return self::LESSON_START_STATUS_IN;
        }

        if ($stopTime < $now) {
            return self::LESSON_START_STATUS_END;
        }

        return self::LESSON_START_STATUS_NOT;
    }

    private
    function getLpcHasQuestion(&$item, $currentLessonInfo) {
        $examType1 = Duxuesc_Const::BIND_TYPE_PRACTICE_IN_CLASS;
        $strExam1  = 'exam' . $examType1;
        $lu        = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];
        if (isset($lu[$strExam1])
            && isset($lu[$strExam1]['participate_num'])
            && $lu[$strExam1]['participate_num'] > 0
        ) {
            $item['hasQuestion'] = 1;
        }
    }

    private
    function getLpcHasPreview(&$item, $currentLessonInfo) {
        //小学-exam5  初中高中-exam13
        $examType2 = Duxuesc_Const::BIND_TYPE_PREVIEW;
        if (in_array($this->gradeId, [2, 3, 4, 20, 21, 5, 6, 7, 30])) {
            $examType2 = Duxuesc_Const::BIND_TYPE_POSTTEST_MORE;
        }
        $strExam2 = 'exam' . $examType2;
        $lu       = $this->lpcLUData[$currentLessonInfo['lessonId']] ?? [];

        if (isset($lu[$strExam2])
            && isset($lu[$strExam2]['participate_num'])
            && $lu[$strExam2]['participate_num'] > 0
        ) {
            $item['hasPreview'] = 1;
        }
    }

    private
    function getLpcLessonReportData(&$item, $currentLessonInfo) {
        //获取班主任/销售课堂报告
        $now      = time();
        $lessonId = intval($currentLessonInfo['lessonId']);
        if ($now < $currentLessonInfo['startTime']) {
            $item['lessonReportUrl']    = [];
            $item['lessonReportStatus'] = 0;
        } else {
            if (!isset($this->lessonReport[$lessonId]) || empty($this->lessonReport[$lessonId])) {
                $item['lessonReportUrl']    = [];
                $item['lessonReportStatus'] = 1;
            } else {
                $item['lessonReportUrl']    = $this->getShortUrl((array)$this->lessonReport[$lessonId]);
                $item['lessonReportStatus'] = 2;
            }
        }

    }

//小鹿口才作业等级
    private
    function getDeerEloquenceHomeworkLevel(&$item, $currentLessonInfo) {
        $deerEloquenceHomeworkLevel         = isset($this->deerData[$currentLessonInfo['lessonId']]['deerEloquenceHomeworkLevel']) ? $this->deerData[$currentLessonInfo['lessonId']]['deerEloquenceHomeworkLevel'] : 0;
        $item['deerEloquenceHomeworkLevel'] = self::$deerHomeworkLevelMap[$deerEloquenceHomeworkLevel] ?? '-';


    }

//小鹿编程作业等级
    private
    function getDeerProgrammingHomeworkLevel(&$item, $currentLessonInfo) {
        $deerProgrammingHomeworkLevel         = isset($this->deerData[$currentLessonInfo['lessonId']]['deerProgrammingHomeworkLevel']) ? $this->deerData[$currentLessonInfo['lessonId']]['deerProgrammingHomeworkLevel'] : 0;
        $item['deerProgrammingHomeworkLevel'] = self::$deerHomeworkLevelMap[$deerProgrammingHomeworkLevel] ?? '-';


    }

    /**
     * 小鹿章节报告
     */
    private function getDeerLessonReport(&$item, $currentLessonInfo) {
        $isSubmitLessonWork = $this->luCommonData[$currentLessonInfo['lessonId']]['is_submit_lesson_work'] ?? 0;
        $reportData = Api_Writereport::getReportUrl();
        $studentReportUrl = str_replace(['__lessonId__', '__studentUid__'], [$currentLessonInfo['lessonId'], $this->studentUid], $reportData['lessonReportUrl']);
        $item['isSubmitLessonWork'] = $isSubmitLessonWork;
        $item['deerLessonReportUrl'] = $isSubmitLessonWork ? Api_Su::getShortUrl($studentReportUrl) : "";
    }

    /**
     * 章节作品
     */
    private function getLessonHomeWork(&$item, $currentLessonInfo) {
        $isSubmitLessonWork = $this->luCommonData[$currentLessonInfo['lessonId']]['is_submit_lesson_work'] ?? 0;
        $item['is_submit_lesson_work'] = $isSubmitLessonWork;
    }

    /**
     * 直播课报告
     */
    private function getZhiboLessonReport(&$item, $currentLessonInfo) {
        $lessonReport = $this->lessonReport[$currentLessonInfo['lessonId']]['url'];
        $isGenerateLessonReport = $this->luCommonData[$currentLessonInfo['lessonId']]['is_generate_lesson_report'] ?? 0;
        $item['isGenerateLessonReport'] = $isGenerateLessonReport;
        $item['zhiboLessonReport'] = $isGenerateLessonReport ? Api_Su::getShortUrl($lessonReport) : "";
    }


    /**
     * 获取章节的巩固练习状态码
     *
     * @param $gradeId
     * @param $lessonData
     * @return int
     */
    private
    function getLessonStrengthPracticeStatus($lessonData) {
        $gradeId = $this->gradeId;

        $examStr  = 'exam' . Duxuesc_Const::BIND_TYPE_PRACTICE_STRENGTH;
        $examData = $lessonData[$examStr] ?: [];
        if (!isset($examData['is_submit']) || ($examData['is_submit'] != 1)) {
            return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_NOCOMMIT;//未提交
        }
        if (Zb_Const_GradeSubject::$GRADEMAPXB[$gradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY
            || Zb_Const_GradeSubject::$GRADEMAPXB[$gradeId] == Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL) {
            if (!isset($examData['correct_status'])) {
                return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITCORRECT;//待批改
            }
            switch ($examData['correct_status']) {
                case 2:
                    return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITCORRECT;
                case 4:
                    return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITRECORRECT;
                case 5:
                    return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITRECOMMIT;
                case 6:
                    return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_RECORRECTED;
            }
            return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_NOCOMMIT;
            //小学
        }
        return AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_COMMIT; //已提交
    }

    private
    function getLessonStrengthPractice($lessonData) {
        $status = $this->getLessonStrengthPracticeStatus($lessonData);
        switch ($status) {
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_NOCOMMIT:
                return '未提交';
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_COMMIT:
                return $this->formatCommitedWord($lessonData);
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITCORRECT:
                return '待批改';
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITRECORRECT:
                return '待重批' . $this->formatCorrectLevel($lessonData);
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_WAITRECOMMIT:
                return '待重提' . $this->formatCorrectLevel($lessonData);
            case AssistantDesk_Data_Duxuesc_DataCenter::PRACTICE_STATUS_RECORRECTED:
                return '已订正' . $this->formatCorrectLevel($lessonData);
        }
        return '未提交';
    }

    private
    function formatCommitedWord($lessonData) {
        $examStr = 'exam' . Duxuesc_Const::BIND_TYPE_PRACTICE_STRENGTH;
        if (!isset($lessonData[$examStr]['right_num']) || !isset($lessonData[$examStr]['participate_num']) || !isset($lessonData[$examStr]['total_num'])) {
            return '-';
        }
        return $lessonData[$examStr]['right_num'] . '/' . $lessonData[$examStr]['participate_num'] . '/' . $lessonData[$examStr]['total_num'];
    }

    private
    function formatCorrectLevel($lessonData) {
        $examStr = 'exam' . Duxuesc_Const::BIND_TYPE_PRACTICE_STRENGTH;
        if (isset($lessonData[$examStr]['correct_level']) && isset(self::$practiceCorrectLevelMap[$lessonData[$examStr]['correct_level']])) {
            return '(' . self::$practiceCorrectLevelMap[$lessonData[$examStr]['correct_level']] . ')';
        }
        return '';
    }

//获取课堂报告的短链接
    private
    function getShortUrl(array $arrLessonReportUrl) {
        if (empty($arrLessonReportUrl)) {
            return [];
        }

        $R = [];
        foreach ($arrLessonReportUrl as $key => $longUrl) {
            $R[$key] = Api_Su::getShortUrl($longUrl) ?: $longUrl;
        }

        return $R;
    }
}