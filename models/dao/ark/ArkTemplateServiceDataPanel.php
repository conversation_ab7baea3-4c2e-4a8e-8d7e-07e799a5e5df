<?php

class Dao_Ark_ArkTemplateServiceDataPanel extends Dao_Base_ZybFudao
{
    public function __construct()
    {
        $this->_table = "tblArkTemplateServiceDataPanel";

        $this->arrFieldsMap = array(
            'id'         => 'id',
            'tplId'      => 'tpl_id',
            'serviceId'  => 'service_id',
            'arkKey'  => 'ark_key',
            'name' => 'name',
            'userType'   => 'user_type',
            'transferType'   => 'transfer_type',
            'homepage' => 'homepage',
            'hover'    => 'hover',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'status'     => 'status',
        );
        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'tplId'     => Hk_Service_Db::TYPE_INT,
            'serviceId'  => Hk_Service_Db::TYPE_INT,
            'arkKey'  => Hk_Service_Db::TYPE_STR,
            'name' => Hk_Service_Db::TYPE_STR,
            'userType'   => Hk_Service_Db::TYPE_JSON,
            'transferType'   => Hk_Service_Db::TYPE_INT,
            'homepage' => Hk_Service_Db::TYPE_INT,
            'hover'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
        );
    }
}