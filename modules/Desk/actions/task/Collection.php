<?php
/**
 * Created by PhpStorm.
 * User: liuxian<PERSON>@zuoyebang.com
 * Date: 2019/08/28
 * Time: 16:08
 */
class Action_Collection extends AssistantDesk_ActionCommon {

    public function invoke(){

        $this->checkLogin();

        $arrInput = array(
            'assistantUid'  => $this->_userInfo['assistantUid'] ? intval($this->_userInfo['assistantUid']) : 0,
            'personUid'     => isset($this->_userInfo['personUid']) ? intval($this->_userInfo['personUid']) : 0,
            'courseId'      => $this->_requestParam['courseId'] ? intval($this->_requestParam['courseId']) : 0,
            'lessonId'      => $this->_requestParam['lessonId'] ? intval($this->_requestParam['lessonId']) : 0,
            'taskId'        => $this->_requestParam['taskId'] ? intval($this->_requestParam['taskId']) : 0,
            'serviceId'     => $this->_requestParam['serviceId'] ? intval($this->_requestParam['serviceId']) : 0,
            'timestamp'     => $this->_requestParam['timestamp'] ? intval($this->_requestParam['timestamp']) : 0,
        );

        Hk_Util_Log::start('ps_desk_task_collection');
        $objPS = new Service_Page_Desk_Coursedata_Collection();
        $arrOutput = $objPS->execute($arrInput);
        Hk_Util_Log::stop('ps_desk_task_collection');

        // diff 请求参数记录
        // 请求只用包含必须的
        AssistantDesk_Data_ArkQueryParamData::insertArkQueryParams($arrInput,'Collection','');

        return $arrOutput;
    }
}