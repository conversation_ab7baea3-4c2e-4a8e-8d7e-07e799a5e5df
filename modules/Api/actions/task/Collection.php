<?php

class Action_Collection extends AssistantDesk_ActionCommon {

    protected $needCheckSelectedAssistant = false;

    public function invoke(){


        $arrInput = array(
            'assistantUid'  => $this->_requestParam['assistantUid'] ? intval($this->_requestParam['assistantUid']) : 0,
            'personUid'     => $this->_requestParam['personUid'] ? intval($this->_requestParam['personUid']) : 0,
            'courseId'      => $this->_requestParam['courseId'] ? intval($this->_requestParam['courseId']) : 0,
            'lessonId'      => $this->_requestParam['lessonId'] ? intval($this->_requestParam['lessonId']) : 0,
            'taskId'        => $this->_requestParam['taskId'] ? intval($this->_requestParam['taskId']) : 0,
            'serviceId'     => $this->_requestParam['serviceId'] ? intval($this->_requestParam['serviceId']) : 0,
            'timestamp'     => $this->_requestParam['timestamp'] ? intval($this->_requestParam['timestamp']) : 0,
            'isApi'=> 1,
        );

        Hk_Util_Log::start('ps_desk_task_collection');
        $objPS = new Service_Page_Desk_Coursedata_Collection();
        $arrOutput = $objPS->execute($arrInput);
        Hk_Util_Log::stop('ps_desk_task_collection');

        return $arrOutput;
    }
}