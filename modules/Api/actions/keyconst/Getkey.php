<?php

class Action_Getkey extends AssistantDesk_ActionApi {
    protected $needGetUserInfo = false;
    protected $needCheckSelectedAssistant = false;
    protected $needCheckRole = false;
    protected $needCheckLogin = false;

    const KEY_MAP = [
        Service_Data_Config_KeyConst::SWITCH_SUBJECT_KEY => "subject",
    ];

    public function invoke()
    {
        $key         = $this->_requestParam['key'] ? trim($this->_requestParam['key']) : Service_Data_Config_KeyConst::SWITCH_SUBJECT_KEY;
        $subjectList = (new Service_Page_Desk_Config_KeyStrategyService)->matchStrategyList($key);
        if (empty($subjectList)) {
            Bd_Log::warning("subjectList_get_empty,学科列表获取为空");
        }
        $keyName = self::KEY_MAP[$key] ?? $key;
        return [$keyName => $subjectList];
    }
}