<?php

class Action_CourseListAndCardByYear extends AssistantDesk_ActionCommon {

    protected $needCheckSelectedAssistant = true;

    public function invoke() {

        $this->checkLogin();

        $this->_requestParam['personUid']    = isset($this->_userInfo['personUid']) ? intval($this->_userInfo['personUid']) : 0;
        $this->_requestParam['assistantUid'] = isset($this->_userInfo['assistantUid']) ? intval($this->_userInfo['assistantUid']) : 0;


        $objPS     = new Service_Page_DeskV1_Task_CourseListAndCardByYear();
        $arrOutput = $objPS->execute($this->_requestParam);

        // diff 请求参数记录
        // 请求只用包含必须的
        $arrInput = array(
            'assistantUid' => $this->_userInfo['assistantUid'] ? intval($this->_userInfo['assistantUid']) : 0,
            'year'     => $this->_requestParam['year'] ? intval($this->_requestParam['year']) : 0,
        );

        AssistantDesk_Data_ArkQueryParamData::insertArkQueryParams($arrInput,'CourseListCard','');

        return $arrOutput;
    }
}
