<?php

class Action_LessonInfo extends AssistantDesk_ActionCommon {

    protected $needCheckSelectedAssistant = true;

    public function invoke() {

        $this->checkLogin();

        $this->_requestParam['personUid'] = isset($this->_userInfo['personUid']) ? intval($this->_userInfo['personUid']) : 0;
        $this->_requestParam['assistantUid'] = isset($this->_userInfo['assistantUid']) ? intval($this->_userInfo['assistantUid']) : 0;


        $objPS     = new Service_Page_DeskV1_Task_LessonInfo();
        $arrOutput = $objPS->execute($this->_requestParam);

        $arrInput = array(
            'courseId'    => $this->_requestParam['courseId'] ?? 0,
            'assistantUid' => isset($this->_userInfo['assistantUid']) ? intval($this->_userInfo['assistantUid']) : 0,
            'personUid' => isset($this->_userInfo['personUid']) ? intval($this->_userInfo['personUid']) : 0
        );
        // 记录请求参数
        AssistantDesk_Data_ArkQueryParamData::insertArkQueryParams($arrInput,'LessonInfo','');

        return $arrOutput;
    }
}


