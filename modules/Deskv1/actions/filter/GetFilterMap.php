<?php

class Action_GetFilterMap extends AssistantDesk_ActionCommon
{

    protected function invoke() {
        $this->checkLogin();

        $arrInput = [
            'assistantUid' => $this->_userInfo['assistantUid'] ? intval($this->_userInfo['assistantUid']) : 0,
            'personUid'    => $this->_userInfo['personUid'] ? intval($this->_userInfo['personUid']) : 0,
            'courseId'     => $this->_requestParam['courseId'] ? intval($this->_requestParam['courseId']) : 0,
            'lessonId'     => $this->_requestParam['lessonId'] ? intval($this->_requestParam['lessonId']) : 0,
            'taskId'       => $this->_requestParam['taskId'] ? intval($this->_requestParam['taskId']) : 0,
            'serviceId'    => $this->_requestParam['serviceId'] ? intval($this->_requestParam['serviceId']) : 0,
            'serviceType'  => $this->_requestParam['serviceType'] ? intval($this->_requestParam['serviceType']) : 0,
            'isMergeCourse' => $this->_requestParam['isMergeCourse'] ? intval($this->_requestParam['isMergeCourse']) : 0,
        ];

        $objPS     = new Service_Page_DeskV1_Filter_GetFilterMap();
        $arrOutput = $objPS->execute($arrInput);

        // diff 请求参数记录
        // 请求只用包含必须的
        AssistantDesk_Data_ArkQueryParamData::insertArkQueryParams($arrInput,'GetFilterMap','');

        return $arrOutput;
    }
}