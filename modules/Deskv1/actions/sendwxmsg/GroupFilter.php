<?php

class Action_GroupFilter extends AssistantDesk_ActionCommon
{

    protected function invoke() {
        $this->checkLogin();

        $arrInput = [
            'assistantUid' => $this->_userInfo['assistantUid'] ?? 0,
            'personUid'    => $this->_userInfo['personUid'] ?? 0,
            'courseId'     => isset($this->_requestParam['courseId']) ? intval($this->_requestParam['courseId']) : 0,
            'lessonId'     => $this->_requestParam['lessonId'] ? intval($this->_requestParam['lessonId']) : 0,
            'sendType'     => $this->_requestParam['sendType'] ? intval($this->_requestParam['sendType']) : 0,
            'taskId'       => isset($this->_requestParam['taskId']) ? intval($this->_requestParam['taskId']) : 0,
            'sceneFilter'  => $this->_requestParam['sceneFilter'] ?? '', //场景过滤
            'pn'           => $this->_requestParam['pn'] ?? 0,
            'rn'           => $this->_requestParam['rn'] ?? 10000,
            'groupItem'    => $this->_requestParam['groupItem'] ?? '', //场景过滤
            'sceneContext' => $this->_requestParam['sceneContext']?? ''
        ];

        $objPS     = new Service_Page_DeskV1_SendMsg_GroupFilter();
        return $objPS->execute($arrInput);
    }
}