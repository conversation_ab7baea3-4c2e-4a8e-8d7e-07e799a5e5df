<?php
/**
 * Created by PhpStorm.
 * User: liuxain<PERSON>@zuoyebang.com
 * Date: 2020/10/28
 * Time: 15:49
 * achilles 相关
 */
class Api_Achilles {

    const STATUS_REFUSE         = 100;  //驳回
    const STATUS_STAGE_DONE     = 2;    //任务阶段
    const STATUS_FINAL_DONE     = 4;    //最终完成

    const RAL_NAME = 'achilles-v3-server';

    //0_0_268188_268556_2000110390_7
    const MATERIALS_PARAM_FORMAT = '%s_%s_%s_%s_%s_%s_%s';

    const LESSONID_STAGE_TEACHERID = '%d_%d_%d';

    /**
     * @param $courseId
     * @param $lessonId
     * @param $studentUid
     * @param $examType
     * @param int $cpuId
     * @param int $outlineId
     * @return false|mixed
     */
    public static function getCorrectionMaterial($courseId, $lessonId, $studentUid, $examType, $cpuId = 0, $outlineId= 0){

        if(!$courseId || !$lessonId || !$studentUid || !$examType){
            return false;
        }

        //根据绑定关系获取到当前存在的所有绑定examId
        $bindKey  = 'lesson_'.$lessonId.":".$examType;
        $bindList = [$bindKey];
        $examRelationList = Api_Examcore::getRelation($bindList);
        $examRelationList = $examRelationList[$bindKey];
        $examIds = is_array($examRelationList) ? array_unique(array_column($examRelationList, 'examId')) : [];
        $examId  = intval($examIds[0]);

        $materialsParam = sprintf(self::MATERIALS_PARAM_FORMAT,
            $cpuId, $outlineId, $courseId, $lessonId, $studentUid, $examType, $examId);

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/pcassistant/api/correction/getcorrectionmaterial",
        ];

        $arrParams = [
            'materialsParam' => [$materialsParam],
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);
        if(false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo']){

            Bd_Log::warning("Error[getchatinfo ral response error] params:".json_encode($arrParams)."], response:[" . json_encode($ret) . ']');
            return false;
        }

        return $ret['data']['correctionMaterial'][$materialsParam];
    }

    public static function getCocosPkgPreviewUrl() {


        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/hera/api/getcocospkgpreviewurl",
        ];

        $arrParams = [
            'status' => ['4'],
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);
        if (false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo']) {

            Bd_Log::warning("Error[getchatinfo ral response error] params:" . json_encode($arrParams) . "], response:[" . json_encode($ret) . ']');
            return false;
        }
        Bd_Log::notice('getCocosPkgPreviewUrl:' . json_encode([$arrHeader, $arrParams, $ret]));

        return $ret['data']['cocosPkgPreviewUrl'][4]['pkgList'] ?? [];
    }

    /**
     * 获取cocos SnapId
     * @param $lessonId
     * @param $teacherUid
     * @return false|mixed
     */
    public static function getCocosSnapId($lessonId, $teacherUid) {

        if (!$lessonId || !$teacherUid) {
            return false;
        }

        $lessonParam = sprintf(self::LESSONID_STAGE_TEACHERID,
            $lessonId, 1, $teacherUid);

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/ocapi/inclass/getallresourcelist",
        ];

        $arrParams = [
            'param' => [$lessonParam],
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);
        if (false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo']) {

            Bd_Log::warning("Error[getallresourcelist ral response error] params:" . json_encode($arrParams) . "], response:[" . json_encode($ret) . ']');
            return false;
        }
        Bd_Log::notice('getCocosSnapId：' . json_encode([$arrHeader, $arrParams, $ret]));

        return $ret['data']['cwAllResourceList'][$lessonParam]['resourceTi']['cocosTiToSnap'] ?? [];
    }


    /**
     *
     * 根据章节批量获取直播间id
     * @param $lessonIds
     * @return false|mixed
     */
    public static function aclsGetRoomsByLessonIds($lessonIds) {

        if (empty($lessonIds)) {
            return false;
        }

        //转换成string类型
        $strLessonIds = [];
        foreach ($lessonIds as $v) {
            $strLessonIds[] = strval($v);
        }
        $lessonIds = $strLessonIds;

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids",
        ];
        $chunked   = array_chunk($lessonIds, 20);
        $reqArray  = [];
        foreach ($chunked as $i => $v) {
            $arrParams           = [
                'lessonIds' => $v,
            ];
            $reqArray["req{$i}"] = [self::RAL_NAME, "post", $arrParams, mt_rand(), $arrHeader];
        }

        $ret = Api_Ral::ralMulti($reqArray);
        Bd_Log::notice('aclsGetRoomsByLessonIds:' . json_encode([$arrHeader, $reqArray, $ret]));

        $roomData = [];
        foreach ($ret as $reqKey => $item) {
            if ($item) {
                //$item = @json_decode($item, true);
                if ($item['errNo'] > 0 || !is_array($item['data']) || !$item['data'] || !$item['data']['liveRoomInfo']) {
                    Bd_Log::notice('Uri:/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids Response:' . json_encode($item));
                    continue;
                }
                $roomData = $roomData + $item['data']['liveRoomInfo'];
            }
        }

        $retData = [];
        foreach ($roomData as $lessonId => $v) {
            foreach ($v as $val) {
                foreach ($val as $oneRoom) {
                    if ($oneRoom['liveStage'] == 1) {
                        $retData[$lessonId] = $oneRoom['liveRoomId'];
                    }
                }
            }
        }

        Bd_Log::notice('aclsGetRoomsByLessonIds_liveRoomData:' . json_encode($retData));

        return $retData;
    }

    /**
     *
     * 根据章节批量获取所有课前课中课后直播间id
     * @param $lessonIds
     * @return false|mixed
     */
    public static function aclsGetAllRoomsByLessonIdsTeacherUid($lessonIds, $teacherUid, $liveStage) {

        if (empty($lessonIds)) {
            return false;
        }

        //转换成string类型
        $strLessonIds = [];
        foreach ($lessonIds as $v) {
            $strLessonIds[] = strval($v);
        }
        $lessonIds = $strLessonIds;

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids",
        ];
        $chunked   = array_chunk($lessonIds, 20);
        $reqArray  = [];
        foreach ($chunked as $i => $v) {
            $arrParams           = [
                'lessonIds' => $v,
            ];
            $reqArray["req{$i}"] = [self::RAL_NAME, "post", $arrParams, mt_rand(), $arrHeader];
        }

        $ret = Api_Ral::ralMulti($reqArray);
        Bd_Log::notice('aclsGetRoomsByLessonIds:' . json_encode([$arrHeader, $reqArray, $ret]));

        $roomData = [];
        foreach ($ret as $reqKey => $item) {
            if ($item) {
                //$item = @json_decode($item, true);
                if ($item['errNo'] > 0 || !is_array($item['data']) || !$item['data'] || !$item['data']['liveRoomInfo']) {
                    Bd_Log::notice('Uri:/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids Response:' . json_encode($item));
                    continue;
                }
                $roomData = $roomData + $item['data']['liveRoomInfo'];
            }
        }

        $retData = [];
        foreach ($roomData as $lessonId => $v) {
            foreach ($v as $val) {
                foreach ($val as $oneRoom) {
                    if ($oneRoom['teacherUid'] == $teacherUid && $oneRoom['liveStage'] == $liveStage) {
                        $retData[$lessonId] = $oneRoom['liveRoomId'];
                    }
                }
            }
        }


        Bd_Log::notice('aclsGetRoomsByLessonIds_liveRoomData:' . json_encode($retData));

        return $retData;
    }



    /**
     *
     * 根据章节批量获取直播间信息（课前、课中、课后）
     * @param $lessonIds
     * @return false|mixed [lessonId => [[liveRoomId => x, ...], ...]]
     */
    public static function aclsGetRoomsDataByLessonIds($lessonIds) {

        if (empty($lessonIds)) {
            return false;
        }

        //转换成string类型
        $strLessonIds = [];
        foreach ($lessonIds as $v) {
            $strLessonIds[] = strval($v);
        }
        $lessonIds = $strLessonIds;

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids",
        ];
        $chunked   = array_chunk($lessonIds, 20);
        $reqArray  = [];
        foreach ($chunked as $i => $v) {
            $arrParams           = [
                'lessonIds' => $v,
            ];
            $reqArray["req{$i}"] = [self::RAL_NAME, "post", $arrParams, mt_rand(), $arrHeader];
        }

        $ret = Api_Ral::ralMulti($reqArray);
        Bd_Log::notice('aclsGetRoomsByLessonIds:' . json_encode([$arrHeader, $reqArray, $ret]));

        $roomData = [];
        foreach ($ret as $reqKey => $item) {
            if ($item) {
                //$item = @json_decode($item, true);
                if ($item['errNo'] > 0 || !is_array($item['data']) || !$item['data'] || !$item['data']['liveRoomInfo']) {
                    Bd_Log::notice('Uri:/achilles/v3/origin/jxdalivestation/api/aclsgetroomsbylessonids Response:' . json_encode($item));
                    continue;
                }
                $roomData = $roomData + $item['data']['liveRoomInfo'];
            }
        }

        $retData = [];
        foreach ($roomData as $lessonId => $v) {
            foreach ($v as $val) {
                foreach ($val as $oneRoom) {
                    $retData[$lessonId][] = $oneRoom;
                }
            }
        }

        Bd_Log::notice('aclsGetRoomsByLessonIds_liveRoomData:' . json_encode($retData));

        return $retData;
    }

    /**
     * 通过阿喀琉斯获取模板信息，可单个或多个
     *
     * @param $tmpIds
     *
     * @return false|mixed
     */
    public static function getTmpDetailByTmpId($tmpIds){

        if(empty($tmpIds)){
            return false;
        }
        $isSingle = is_array($tmpIds) ? false : true;

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/tartarus/v1/getTmpDetailAchilles",
        ];

        $arrParams = [
            'tmpKey' => $isSingle ? [$tmpIds] : $tmpIds,
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);
        if(false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo']){

            Bd_Log::warning("Error[getTmpDetailByTmpId ral response error] params:".json_encode($arrParams)."], response:[" . json_encode($ret) . ']');
            return false;
        }

        return $isSingle ? $ret['data']['tmpDetail'][$tmpIds] : $ret['data']['tmpDetail'];
    }

    /**
     * 通过阿喀琉斯获取模板信息，可单个或多个
     * @param $courseIds
     * @return false|mixed
     */
    public static function getWhetherAssignClassByCourseIds($courseIds) {
        if (empty($courseIds)) {
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/achilles/v3/origin/plum/api/whetherassignclass",
        ];

        $arrParams = [
            'courseIds' => $courseIds,
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);
        if (false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo']) {
            Bd_Log::warning("Error[getTmpDetailByTmpId ral response error] params:".json_encode($arrParams)."], response:[" . json_encode($ret) . ']');
            return false;
        }

        return $ret;
    }


    /**
     * 判断是否纳米灰度
     */
    public static function batchMatch($lessonId) {
        $arrParams = [
            'lessonIds' => is_array($lessonId) ? $lessonId : [strval($lessonId)],
        ];
        $arrHeader  = [
            'pathinfo'  => '/achilles/v3/origin/classgray/api/batchmatch',
        ];

        $ret = Api_Ral::ral($arrParams, $arrHeader, self::RAL_NAME);

        Bd_Log::notice("Api_Achilles_BatchMatch,params:" . json_encode($arrParams));
        if (false === $ret || !isset($ret['errNo']) || 0 != $ret['errNo'] || empty($ret['data'])) {
            Bd_Log::warning("Error[Api_Achilles_BatchMatch_Error] params:".json_encode($arrParams)."], response:[" . json_encode($ret) . ']');
            return false;
        }
        
        return $ret['data']['classGrayBatchMatch'] ?? [];
    }

    public static function tikuProxyGetByTid($tidList) {
        if(empty($tidList) || !is_array($tidList)){
            Bd_Log::warning("Error:[tikuProxyGetByTid][param error], Detail:[tidList:$tidList");
            return false;
        }
        if (count($tidList) > 15) {
            return self::tikuProxyMultiGetByTid($tidList);
        }
        $tidList = array_map('strval', $tidList);

        $arrHeader = [
            'pathinfo'      => '/achilles/v3/origin/tikuproxy/api/v1/getbytid',
            'Content-Type'  => 'application/json',
        ];

        $params = [
            'tid' => $tidList,
        ];
        $ret = Api_Ral::ral($params, $arrHeader, self::RAL_NAME, 'POST');

        if(false === $ret || 0 != $ret['errNo']){
            Bd_Log::warning("Error:[tikuProxyGetByTid][network error], Detail:[param:".@json_encode($params).",ret:".@json_encode($ret));
            return false;
        }
        return $ret['data']['tiInfos'];
    }

    public static function tikuProxyMultiGetByTid($tidList) {
        if(empty($tidList) || !is_array($tidList)){
            Bd_Log::warning("Error:[tikuProxyGetByTid][param error], Detail:[tidList:$tidList");
            return false;
        }
        $tidList = array_map('strval', $tidList);
        $arrHeader = [
            'pathinfo'      => '/achilles/v3/origin/tikuproxy/api/v1/getbytid',
            'Content-Type'  => 'application/json',
        ];
        $sliceList = array_chunk($tidList, 15);

        $reqArr = [];
        foreach ($sliceList as $key => $slice){
            $params = [
                'tid' => $slice,
            ];

            $reqArr[$key] = [self::RAL_NAME, "post", $params, mt_rand(), $arrHeader];
        }

        $retList = Api_Ral::ralMulti($reqArr);
        $data    = [];
        foreach ($retList as $key => $item){
            if(empty($item)){
                Bd_Log::warning("Error:[multi false], Detail:".json_encode([$reqArr[$key], $item]));
                continue;
            }
            if(intval($item['errNo'])){
                Bd_Log::warning("Error:[multi errNo], Detail:".json_encode([$reqArr[$key], $item]));
                continue;
            }

            $data = $item['data']['tiInfos'] ? $data + $item['data']['tiInfos'] : $data;
        }
        return $data;
    }

}
