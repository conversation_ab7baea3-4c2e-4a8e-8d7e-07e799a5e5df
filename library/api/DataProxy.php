<?php
class Api_DataProxy {
    const RAL_NAME = 'dataproxy';
    const RAL_NAME_JSON = 'dataproxy-v2';

    const EDU_PROBE_PDF_STATUS_NOT_CREATED = 0;
    const EDU_PROBE_PDF_STATUS_CREATED = 1;
    const EDU_PROBE_PDF_STATUS_OUTDATED = 2;
    const EDU_PROBE_PDF_STATUS_CREATE_FAIL = 3;
    const EDU_PROBE_PDF_STATUS_CREATING = 100;

    const REVISE_STATUS_NEVER = 0;
    const REVISE_STATUS_DOING = 1;
    const REVISE_STATUS_DONE = 2;
    const REVISE_STATUS_NO_NEED = 3;

    public static function getVolunteerCardListByStudentUids($studentUids, $fields = ['student_uid']) {
        if(empty($studentUids) || !is_array($studentUids) || empty($fields) || !is_array($fields)){
            return false;
        }

        $arrHeader  = array(
            'pathinfo' => '/dataproxy/student/get-volunteercard-list',
            'cookie' => $_COOKIE,
        );

        $arrParams  = array(
            'studentUids' => implode(',', $studentUids),
            'fields' => implode(',', $fields),
        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getLuComonListByStudentLessons($studentUid, $lessonIds, $fields = ['student_uid']) {
        if (empty($lessonIds) || !is_array($lessonIds) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $arrHeader = array(
            'pathinfo' => '/dataproxy/lu/get-common-list-by-student-lessons',
            'cookie' => $_COOKIE,
        );

        $arrParams = array(
            'studentUid' => $studentUid,
            'lessonIds'  => implode(',', $lessonIds),
            'fields'     => implode(',', $fields),
        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getContinueSemesterListByStudentUidsAndOtherConds($studentUids, $otherConds = [], $fields = ['student_uid']) {
        if(empty($studentUids) || !is_array($studentUids) || empty($fields) || !is_array($fields)){
            return false;
        }

        $arrHeader  = array(
            'pathinfo' => '/dataproxy/studentYearSeasonPeriod/getListByStudentIdsYearSeasonLearnSeason',
            'cookie' => $_COOKIE,
        );

        $arrParams  = array(
            'studentUids' => implode(',', $studentUids),
            'fields' => implode(',', $fields),
        );

        if (!empty($otherConds['learn_year'])) {
            $arrParams['learn_year'] = $otherConds['learn_year'];
        }

        if (!empty($otherConds['season'])) {
            $arrParams['season'] = $otherConds['season'];
        }

        if (!empty($otherConds['learn_season'])) {
            $arrParams['learn_season'] = $otherConds['learn_season'];
        }

        if (!empty($otherConds['learn_seasons'])) {
            $arrParams['learn_seasons'] = implode(',', $otherConds['learn_seasons']);
        }

        if (!empty($otherConds['gradeIds'])) {
            $arrParams['gradeIds'] = implode(',', $otherConds['gradeIds']);
        }


        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        $data = $data === false ? false : $data['data']['list'];
        if (false === $data) {
            return $data;
        }
        foreach ($data as $k => $li) {
            if (isset($li['reserve_course'])) {
                $data[$k]['reserve_course'] = $li['reserve_course'] ? @json_decode($li['reserve_course'],true) : [];
            }
            if (isset($li['reserve_subject'])) {
                $data[$k]['reserve_subject'] = $li['reserve_subject'] ? @json_decode($li['reserve_subject'],true) : [];
            }
            if (isset($li['valid_subjects'])) {
                $data[$k]['valid_subjects'] = $li['valid_subjects'] ? @json_decode($li['valid_subjects'],true) : [];
//                if (!$statisticSubjects) {
//                    //如果不传，默认为学科的subjectIds，否则使用传入的subjectIds，比如学能的subjectIds
//                    $statisticSubjects = [
//                        Zb_Const_GradeSubject::CHINESE,
//                        Zb_Const_GradeSubject::MATHEMATICS,
//                        Zb_Const_GradeSubject::ENGLISH,
//                        Zb_Const_GradeSubject::PHYSICS,
//                        Zb_Const_GradeSubject::CHEMISTRY,
//                        Zb_Const_GradeSubject::BIOLOGY,
//                        Zb_Const_GradeSubject::POLITICS,
//                        Zb_Const_GradeSubject::HISTORY,
//                        Zb_Const_GradeSubject::GEOGRAPHY,
//                    ];
//                }
//                //过滤学科或学能有效学科ids
//                foreach ($data[$k]['valid_subjects'] as $idx => $subjectId) {
//                    if (!in_array($subjectId, $statisticSubjects)) {
//                        unset($data[$k]['valid_subjects'][$idx]);
//                    }
//                }
            }
        }
        return $data;
    }

    public static function getListByCourseIdUnitIdsStudentUids($courseId, $unitIds, $studentUids, $fields = ['courseId']) {
        if (empty($courseId) || empty($unitIds) || !is_array($unitIds) || !is_array($studentUids) || empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $arrHeader = array(
            'pathinfo' => '/dataproxy/cuUnit/getListByCourseIdStudentUidsUnitIds',
            'cookie' => $_COOKIE,
        );

        $arrParams = array(
            'courseId'    => $courseId,
            'studentUids' => implode(',', $studentUids),
            'unitIds'     => implode(',', $unitIds),
            'fields'      => implode(',', $fields),
        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        if (false === $data) {
            Bd_Log::warning("getListByCourseIdUnitIdsStudentUids error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    /**
     * @param $learnYear
     * @param $department
     * @param $learnSeasons
     * @param $studentUids
     * @param string[] $fields
     * @param bool $filterNotPre 是否过滤未预约数据，默认为true
     * @return false|mixed
     */
    public static function getListByStudentIdsYearDepartmentPeriods($learnYear, $department, $learnSeasons, $studentUids, $fields = ['student_uid'], $filterNotPre = true) {
        if (empty($learnYear) || empty($department) || !is_array($learnSeasons) || !is_array($studentUids) || empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $arrHeader = array(
            'pathinfo' => '/dataproxy/studentYearDepartmentPeriod/getListByStudentIdsYearDepartmentPeriods',
            'cookie' => $_COOKIE,
        );

        $arrParams = array(
            'studentUids'  => implode(',', $studentUids),
            'learnSeasons' => implode(',', $learnSeasons),
            'fields'       => implode(',', $fields),
            'learnYear'    => $learnYear,
            'department'   => $department,

        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        if (false === $data) {
            Bd_Log::warning("getListByStudentIdsYearDepartmentPeriods error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        if ($filterNotPre && is_array($data)) {
            $tmpData = [];
            foreach ($data as $v) {
                if (in_array('earliest_reserve_time', $fields) && empty($v['earliest_reserve_time'])) {
                    continue;
                }
                if (in_array('reserve_grade_subject', $fields) && empty($v['reserve_grade_subject'])) {
                    continue;
                }
                $tmpData[] = $v;
            }
            $data = $tmpData;

        }
        return $data;
    }

    public static function getCommonLuByLessonStudents($lessonId, $studentUids, $fields = ['student_uid']) {
        if (empty($lessonId) || !is_array($studentUids) || empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $arrHeader = array(
            'pathinfo' => '/dataproxy/lu/get-common-list-by-lesson-students',
            'cookie' => $_COOKIE,
        );

        $arrParams = array(
            'lessonId'    => $lessonId,
            'studentUids' => implode(',', $studentUids),
            'fields'      => implode(',', $fields),
        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        if (false === $data) {
            Bd_Log::warning("getCommonListByLessonStudents error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getCommonLuByLessonIdsStudents($lessonIds, $studentUids, $fields = ['student_uid']) {
        if (empty($lessonIds) || !is_array($studentUids) || empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $arrHeader = array(
            'pathinfo' => '/dataproxy/lu/get-common-list-by-lessons-students',
        );

        $arrParams = array(
            'lessonIds'   => implode(',', $lessonIds),
            'studentUids' => implode(',', $studentUids),
            'fields'      => implode(',', $fields),
        );

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);

        if (false === $data) {
            Bd_Log::warning("getCommonListByLessonStudents error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getExternalCuByCourseStudents($courseId, $studentUids, $fields = ['student_uid']) {
        if (empty($courseId) || !is_array($studentUids) || empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/cu/get-external-list-by-course-students',
            'cookie' => $_COOKIE,
        ];
        $params = array(
            'courseId'    => $courseId,
            'studentUids' => implode(',', $studentUids),
            'fields'      => implode(',', $fields),
        );
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getExternalCuByCourseStudents error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getCommonListByStudentCourseIds($courseIds, $studentUid, $fields = ['student_uid']) {
        if (empty($courseIds) || !is_array($courseIds) || empty($studentUid) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/cu/getCommonListByStudentCourseIds',
        ];
        $params = array(
            'courseIds'  => implode(',', $courseIds),
            'studentUid' => $studentUid,
            'fields'     => implode(',', $fields),
        );
        $data   = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getCommonListByStudentCourseIds error, return false");
        }

        $data = $data === false ? false : $data['data']['list'];
        return $data;
    }

    public static function getListByStudentCourseIds($courseId, $studentUids, $fields = ['student_uid']) {
        if ($courseId == 0) {
            return false;
        }
        if (empty($studentUids) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/cu/getListByCourseIdsStudentUids',
        ];
        $params = array(
            'studentUids'  => implode(',', $studentUids),
            'courseIds' => $courseId,
            'fields'     => implode(',', $fields),
        );
        $data   = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getListByStudentCourseIds error, return false");
            return false;
        }
        $groupData = Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $groupData;
    }

    public static function getLuListByCourseIdLessonIdsAssistantUid($courseId, $lessonIds, $assistantUid, $fields = ['student_uid']) {
        if ($courseId == 0) {
            return false;
        }
        if (empty($lessonIds) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/lu/getListByCourseIdLessonIdsAssistantUid',
        ];
        $params = array(
            'lessonIds'  => implode(',', $lessonIds),
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'fields'     => implode(',', $fields),
        );
        $data   = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getLuListByCourseIdLessonIdsAssistantUid error, return false");
            return false;
        }
        $groupData = Tools_Array::getNewKeyArray($data['data']['list'], 'studentUid');
        return $groupData;
    }

    /**
     * 学生App维度数据
     *
     * @param array $studentUids
     * @param int $appType App枚举值：1-小鹿素养App；2-小鹿写字App
     * @param array $fields
     * @return false|array
     */
    public static function getEsStudentAppData($studentUids, $appType, $fields) {
        if (empty($studentUids) || !is_array($studentUids) || empty($appType) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/student/get-student-app-info',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUids' => implode(',', $studentUids),
            'appType' => $appType,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getEsStudentAppData error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 获取cuWeekly维度数据
     * @param $studentUids
     * @param $courseId
     * @param int|null $weekly
     * @param $fields
     * @return array|bool map[studentUid][weekly]data
     */
    public static function getExternalWeeklyStudentMap($studentUids, $courseId, $weekly, $fields) {
        if (empty($studentUids) || !is_array($studentUids)
            || empty($courseId) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['student_uid', 'weekly']));
        $header = [
            'pathinfo' => '/dataproxy/cuWeekly/getExternalWeeklyStudentList',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUids' => implode(',', $studentUids),
            'courseId' => $courseId,
            'fields' => implode(',', $fields),
        ];
        if (!empty($weekly)) {
            $params['weekly'] = $weekly;
        }
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getDeerWeeklyCUData error, return false");
            return false;
        }

        if (empty($data['data']['list'])) {
            return [];
        }

        $list = [];
        foreach ($data['data']['list'] as $row) {
            $list[$row['student_uid']][$row['weekly']] = $row;
        }
        return $list;
    }

    /**
     * 获取调查问卷提交数据
     * @param $studentUids
     * @param $sourceId
     * @param $sourceType
     * @param $dimensionKey
     * @param $dimensionType
     * @param $evaluateId
     * @param $fields
     * @return array|false
     */
    public static function getEvaluateCommitStudentMap($studentUids, $sourceId, $sourceType, $dimensionKey, $dimensionType, $evaluateId, $fields)
    {
        if (empty($studentUids) || !is_array($studentUids) || empty($sourceId) || empty($sourceType)
            || empty($dimensionKey) || empty($dimensionType) || empty($evaluateId)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/cu/customer-satisfaction-survey',
            'cookie'   => $_COOKIE,
        ];

        if (empty($fields)) {
            $fields = ["student_uid", "create_time", "submit_id"];
        }

        $params = [
            'studentUids'   => implode(',', $studentUids),
            'sourceId'      => $sourceId,
            'sourceType'    => $sourceType,
            'dimensionKey'  => $dimensionKey,
            'dimensionType' => $dimensionType,
            'evaluateId'    => $evaluateId,
            'fields'        => implode(',', $fields),
        ];
        $data   = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning(sprintf("getEvaluateCommitStudentMap error, params: %s", json_encode($params)));
            return false;
        }

        if (empty($data['data']['list'])) {
            return [];
        }

        $list = [];
        foreach ($data['data']['list'] as $row) {
            $list[$row['student_uid']] = [
                "commitStatus" => 1,
                "submitId"     => $row["submit_id"] ?? 0,
                "studentUid"   => $row["student_uid"] ?? 0,
                "commitTime"   => $row["create_time"] ?? 0,
            ];
        }
        return $list;
    }

    /**
     * 获取学习报告CU维度数据
     * https://yapi.zuoyebang.cc/project/7087/interface/api/795560
     * @param $studentUids
     * @param $courseId
     * @param int|null $weekly
     * @param $fields
     * @return array|bool map[studentUid][weekly]data
     */
    public static function getEsExternalULearningReport($studentUids, $fields, $type = 'edu_probe') {
        if (!is_array($studentUids)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['student_uid', 'zbk_update_time']));
        $arrHeader = [
            'pathinfo' => '/dataproxy/cu/learning-report-list',
            'cookie' => $_COOKIE,
        ];
        $arrParams = [
            'studentUids' => implode(',', $studentUids),
            'type'        => $type,
            'fields'      => implode(',', $fields),
        ];

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getEsStudentAppData error, return false");
            return false;
        } else {
            $groupData = Tools_Array::groupByColumn($data['data']['list'], 'student_uid');
//            print_r($groupData);
            $res = [];
            foreach ($groupData as $studentUid => $group) {
                usort($group, function ($a, $b) {
                    return intval($b['zbk_update_time']) - intval($a['zbk_update_time']);
                });
                $res[$studentUid] = reset($group);
            }
            return $res;
        }
    }

    /**
     * 获取cau维度的学生数据
     * @param $assistantId
     * @param $courseId
     * @param $studentUids
     * @param $fields
     * @param int $saveTime Ymd的日期格式，eg：20240320
     * @return array|bool
     */
    public static function getEsAssistantCourseStudentList($assistantId, $courseId, $studentUids, $fields,
                                                           $saveTime = 0, $isNewIndex = 0) {
        if (empty($assistantId) || empty($courseId) || empty($studentUids) || empty($fields)) {
            return false;
        }
        if (empty($saveTime)) {
            $saveTime = intval(date("Ymd", time()));
        }

        $fields = array_unique(array_merge($fields, ['student_uid']));
        $arrHeader = [
            'pathinfo' => '/dataproxy/cau/getAssistantCourseStudentList',
            'cookie' => $_COOKIE,
        ];
        $arrParams = [
            'assistantUid' => $assistantId,
            'courseId'     => $courseId,
            'saveTime'     => $saveTime,
            'studentUids'  => implode(',', $studentUids),
            'fields'       => implode(',', $fields),
            'isNew'        => $isNewIndex,
        ];

        $data = Api_Ral::Ral($arrParams, $arrHeader, self::RAL_NAME);
        AssistantDesk_Util_Log::debug("Api_DataProxy.getEsAssistantCourseStudentList:params:".json_encode(func_get_args()).";response:".json_encode($data));
        if (false === $data) {
            Bd_Log::warning("getEsAssistantCourseStudentList error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 通过dataproxy转发kafka消息
     * @param $messageList
     * @return bool|mixed
     */
    public static function syncMetricsCommon($messageList) {
        if (empty($messageList)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/metrics/syncMetricsCommon',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'messageList' => $messageList,
        ];

        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("syncMetricsCommon error, return false");
        }

        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function aggCourseWeekSportGroup($courseId,$fields) {
        if (empty($courseId) || empty($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/sport/get-agg-course-group',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'courseId' => $courseId,
            'fields' => implode(',', $fields),
        ];

        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("syncMetricsCommon error, return false");
            return false;
        }
        return  $data['data']['list'] ?? [];
    }

    public static function getSportWeekReportList($courseId,$taskId,$studentUids,$fields) {
        if (empty($courseId) || empty($fields) || empty($taskId)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/sport/get-course-week-student-list',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'courseId' => $courseId,
            'week' => $taskId,
            'studentUids' => implode(",",$studentUids),
            'fields' => implode(',', $fields),
        ];

        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("syncMetricsCommon error, return false");
            return false;
        }
        return  $data['data']['list'] ?? [];
    }

    public static function getStudentSportReportList($courseId,$studentUids,$fields) {
        if (empty($courseId) || empty($fields)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/sport/get-course-student-list',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'courseId' => $courseId,
            'studentUids' => $studentUids,
            'fields' => implode(',', $fields),
        ];

        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("syncMetricsCommon error, return false");
            return false;
        }
        return  $data['data']['list'] ?? [];
    }


    public static function getDeerDeviceCheckInfo($studentUids, $fields, $appId = "") {
        if (empty($studentUids)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/student/deer-device-check-info',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUids'  => implode(',', $studentUids),
            'appId' => $appId ?? "xiaolusuyang",
            'fields'       => implode(',', $fields),
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $res) {
            Bd_Log::warning("getDeerDeviceCheckInfo error, return false");
            return [];
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getDeerDeviceCheckInfo errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return [];
        }
        return $res['data']['list'] ?: [];
    }


    public static function getAggQiweiCourseList($assistantUid, $courseId, $fields) {
        $header = [
            'pathinfo' => '/dataproxy/qiwei-course/get-lpc-course-assistant-agg',
            'cookie' => $_COOKIE,

        ];
        $params = [
            'courseId'  => $courseId,
            'assistantUid'  => $assistantUid,
            'fields'       => implode(',', $fields),
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $res) {
            Bd_Log::warning("getAggQiweiCourseList error, return false");
            return false;
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getAggQiweiCourseList errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return false;
        }
        return $res['data']['list'] ?: [];
    }

    public static function getAssistantQiweiLessonList($assistantUid, $lessonId,$saveTime,$newUserType,$transferType, $fields) {
        $header = [
            'pathinfo' => '/dataproxy/qiwei-course/get-assistant-lesson-assistant-agg',
            'cookie' => $_COOKIE,

        ];
        $params = [
            'lessonId'  => $lessonId,
            'assistantUid'  => $assistantUid,
            'saveTime'  => $saveTime,
            'newUserType'  => $newUserType,
            'transferType'  => $transferType,
            'fields'       => implode(',', $fields),
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $res) {
            Bd_Log::warning("getAggQiweiCourseList error, return false");
            return false;
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getAggQiweiCourseList errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return false;
        }
        return $res['data']['list'] ?: [];
    }

    /**
     * 根据学生ID获取订单数据
     * @param $studentUids
     * @param $fields
     * @return array
     */
    public static function getIdlTradeOrderAssistantByStudentUids($studentUids, $fields)
    {
        if (!is_array($studentUids) && empty($studentUids)) {
            return [];
        }

        $arrHeader = [
            'pathinfo' => '/dataproxy/cuOrder/getListByStudentUids',
            'cookie'   => $_COOKIE,
        ];
        $arrParams = [
            'fields'    => implode(',', $fields),
            'startTime' => time() - 365 * 86400 * 3 // 课程最长服务周期是一年半, 可以向前取开课时间是一年内的数据, 需要取两年半以内的数据,
        ];

        $studentUidChunk = array_chunk($studentUids, 200);
        $reqArray = [];
        foreach ($studentUidChunk as $i => $ids)
        {
            $arrParams['studentUids'] = implode(",", $ids);
            $reqArray[$i] = [self::RAL_NAME, 'get', $arrParams, mt_rand(), $arrHeader];
        }

        $retList =  Api_Ral::ralMulti($reqArray);
        $data = [];
        foreach ($retList as $key => $item) {
            $item = @json_decode($item, true);
            if (empty($item) || !isset($item['errNo']) || $item["errNo"] > 0) {
                Bd_Log::warning("Error:[multi false], Detail:" . json_encode([$reqArray[$key], $item]));
                continue;
            }
            $data = array_merge($data, $item['data']['list'] ?? []);
        }
        return $data;
    }

    public static function getBcReportStatusByLessonStudentUids($lessonIds, $studentUids, $fields) {
        if (empty($lessonIds) || empty($studentUids)) {
            return [];
        }
        $header = [
            'pathinfo' => '/dataproxy/lu/get-bc-report-data-by-lesson-students',
            'cookie' => $_COOKIE,

        ];
        $reqArr = [];
        foreach ($lessonIds as $idx => $lessonId) {
            $params = [
                'lessonId'  => $lessonId,
                'studentUids'  => implode(',', $studentUids),
                'fields'       => implode(',', $fields),
            ];
            $reqArr[$idx] = [self::RAL_NAME, "post", $params, mt_rand(), $header];
        }
        $data = [];
        $retList = Api_Ral::ralMulti($reqArr);
        foreach ($retList as $key => $item){
            $item = @json_decode($item, true);
            if(empty($item)){
                Bd_Log::warning("Error:[multi false], Detail:".json_encode([$reqArr[$key], $item]));
                continue;
            }
            if(intval($item['errNo'])){
                Bd_Log::warning("Error:[multi errNo], Detail:".json_encode([$reqArr[$key], $item]));
                continue;
            }
            //注意下，key冲突的话，array_merge是会后者覆盖前面的key，+不会，此处无所谓
            $data = $item['data']['list'] ? array_merge($data, $item['data']['list']) : $data;
        }
        return $data;
    }
    
    public static function getSopCourseLessonLeadsData($courseId, $lessonId, $studentUids, $fields) {
        if (empty($studentUids)){
            return [];
        }
        $path = "/dataproxy/sopclu/get-course-lesson-common-data-new";
        $params = [
            "studentUids" => implode(",", $studentUids),
            "courseId" => $courseId,
            "lessonId" => $lessonId,
            "fields" => implode(",", $fields),
        ];
        $arrHeader = [
            'pathinfo' => $path,
            'cookie' => $_COOKIE,
        ];
        $res       = Api_Ral::ral($params, $arrHeader, self::RAL_NAME);
        if ($res === false) {
            return false;
        }
        if (!isset($res['errNo']) || $res['errNo']) {
            return false;
        }

        return $res['data']['list'] ?: [];
    }

    /**
     * 城市信息数据
     *
     * @param array $cityNames
     * @param array $fields
     * @return false|array
     */
    public static function getEsCityData($cityNames, $fields = []) {
        if (empty($cityNames) || !is_array($cityNames)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['city_level_name','city_name','city_level']));
        $header = [
            'pathinfo' => '/dataproxy/area/get_city_info',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'cityNames' => implode(',', $cityNames),
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getEsCityData error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'city_name');
        return $data;
    }

    /**
     * 根据学生ID+skuId获取完成的订单信息
     */
    public static function getOrderStudentSkuOrderInfo($studentUids, $skuId, $fields){
        if (!is_array($studentUids) || empty($studentUids)) {
            return [];
        }
        $arrHeader = [
            'pathinfo' => '/dataproxy/order/get-order-student-sku-order-info',
            'cookie'   => $_COOKIE,
        ];
        $studentUidChunk = array_chunk($studentUids, 200);
        $reqArray        = [];
        foreach ($studentUidChunk as $i => $ids) {
            $arrParams = [
                'fields'      => implode(',', $fields),
                'sku_id'      => $skuId,
                'studentUids' => implode(",", $ids) // 课程最长服务周期是一年半, 可以向前取开课时间是一年内的数据, 需要取两年半以内的数据,
            ];
            $reqArray[$i]     = [self::RAL_NAME, 'get', $arrParams, mt_rand(), $arrHeader];
        }
        $ret = Api_Ral::ralMulti($reqArray);
        $data    = [];
        foreach ($ret as $key => $item) {
            $item = @json_decode($item, true);
            if (empty($item) || !isset($item['errNo']) || $item["errNo"] > 0) {
                Bd_Log::warning("Error:[multi false], Detail:" . json_encode([$reqArray[$key], $item]));
                continue;
            }
            $data = array_merge($data, $item['data']['list'] ?? []);
        }
        return $data;
    }

    /**
     * 获取学情报告 - 学生维度
     * @param $studentUids
     * @param $fields
     * @return array|bool
     */
    public static function getEsULearningReport($studentUids, $fields) {
        return Api_EduProbe::getLearningReportByStudent($studentUids);
        // 数据组还没接，先强制切换到eduprobe todo;

        if (!is_array($studentUids) || empty($studentUids)) {
            return false;
        }
        $header = [
            'pathinfo' => '/dataproxy/u/get-learningreport-by-student',
            'cookie'   => $_COOKIE,
        ];
        $fields = array_unique(array_merge($fields, ['student_uid']));
        $params = [
            'studentUids' => implode(',', $studentUids),
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getLearningReportByStudent error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 获取学情报告 - 学生课程维度
     * @param $studentUids
     * @param $courseId
     * @param $fields
     * @return array|bool
     */
    public static function getEsCULearningReport($studentUids, $courseId, $fields) {
        if (!is_array($studentUids) || empty($studentUids) || empty($courseId)) {
            return false;
        }
        $header = [
            'pathinfo' => '/dataproxy/cu/get-learningreport-by-student-course',
            'cookie'   => $_COOKIE,
        ];
        $fields = array_unique(array_merge($fields, ['student_uid', 'course_id']));
        $params = [
            'studentUids' => implode(',', $studentUids),
            'courseId' => $courseId,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getLearningReportByStudentCourse error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 获取课程任务维度的学生错题任务
     *
     * @param array $studentUids
     * @param int $courseId
     * @param array $fields
     * @return false|array
     */
    public static function getEsExerciseNoteTaskData($courseId,$assistantUid,$taskId,$studentUids, $fields) {
        if (empty($studentUids) || !is_array($studentUids) || empty($courseId) || empty($assistantUid) || empty($taskId) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/exercisenote/get-course-task-stu-data',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUids' => implode(',', $studentUids),
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'taskId' => $taskId,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getEsExerciseNoteTaskData error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 获取课程任务维度的学生错题任务
     *
     * @param array $studentUids
     * @param int $courseId
     * @param array $fields
     * @return false|array
     */
    public static function getEsExerciseNoteCourseTaskData($courseId,$assistantUid,$taskIds, $fields) {
        if (empty($taskIds) || !is_array($taskIds) || empty($courseId) || empty($assistantUid) || empty($fields) || !is_array($fields)) {
            return false;
        }

        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/exercisenote/get-course-task-all-data',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'taskIds' => implode(',', $taskIds),
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getEsExerciseNoteTaskData error, return false");
        }

        $data = $data === false ? false : Tools_Array::getNewKeyArray($data['data']['list'], 'student_uid');
        return $data;
    }

    /**
     * 获取课程任务维度的学生错题任务
     *
     * @param int $studentUid
     * @param int $courseId
     * @param int $assistantUid
     * @param array $fields
     * @return false|array
     */
    public static function getStuExerciseNoteTaskData(int $courseId, int $assistantUid, int $studentUid, array $fields = []) {
        if (empty($studentUid) || empty($courseId) || empty($assistantUid) || empty($fields)) {
            return false;
        }


        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/exercisenote/get-course-stu-task-data',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUid' => $studentUid,
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getStuExerciseNoteTaskData error, return false");
        }
        return $data['data']['list'] ?? false;
    }

    /**
     * 获取课程任务维度的学生错题任务
     *
     * @param int $studentUid
     * @param int $courseId
     * @param int $assistantUid
     * @param array $fields
     * @return false|array
     */
    public static function getCourseStuTaskTiData(int $courseId, int $assistantUid, int $studentUid, array $fields = []) {
        if (empty($studentUid) || empty($courseId) || empty($assistantUid) || empty($fields)) {
            return false;
        }


        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/exercisenote/get-course-stu-ti-data',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'studentUid' => $studentUid,
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getCourseStuTaskTiData error, return false");
        }
        return $data['data']['list'] ?? false;
    }

    /**
     * 获取课程任务维度的错题信息
     *
     * @param int $studentUid
     * @param int $courseId
     * @param int $assistantUid
     * @param array $fields
     * @return false|array
     */
    public static function getCourseTaskTiData(int $courseId, int $assistantUid, int $taskId, array $fields = []) {
        if (empty($taskId) || empty($courseId) || empty($assistantUid) || empty($fields)) {
            return false;
        }


        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/exercisenote/get-course-task-ti-data',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'taskId' => $taskId,
            'courseId' => $courseId,
            'assistantUid' => $assistantUid,
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $data) {
            Bd_Log::warning("getCourseTaskTiData error, return false");
        }
        return $data['data']['list'] ?? false;
    }

    public static function getLessonExamTidStudentUidList(int $lessonId, int $examId, int $tid,int $tidSort,array $studentUidList,array $fields = []) {
        if (empty($lessonId) || empty($examId) || empty($tid) || empty($fields)) {
            return false;
        }


        $fields = array_unique(array_merge($fields, ['student_uid']));
        $header = [
            'pathinfo' => '/dataproxy/let/get-lesson-exam-tid-student',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'lessonId' => $lessonId,
            'examId' => $examId,
            'tid' => $tid,
            'tidSort' => $tidSort,
            'studentUids' => implode(',', $studentUidList),
            'fields' => implode(',', $fields),
        ];
        $data = Api_Ral::Ral($params, $header, self::RAL_NAME);
        if (false === $data) {
            Bd_Log::warning("getLessonExamTidStudentUidList error, return false");
        }
        return $data['data']['list'] ?? false;
    }

    public static function getStudentExprDetailInfo($businessId,$fields) {
        if (empty($businessId)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/student/get-student-expr-detail',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'businessId' => $businessId,
            'fields'       => implode(',', $fields),
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $res) {
            Bd_Log::warning("getStudentExprDetailInfo error, return false");
            return [];
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getStudentExprDetailInfo errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return [];
        }
        return $res['data']['list'] ?: [];
    }

    public static function getStudentExprCntInfo($studentUids, $courseId,$fields) {
        if (empty($studentUids) || empty($courseId)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/student/get-student-expr-cnt',
            'cookie' => $_COOKIE,
        ];
        $params = [
            'courseId' => $courseId,
            'studentUids'  => implode(',', $studentUids),
            'fields'       => implode(',', $fields),
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME, Api_Ral::METHOD_GET);
        if (false === $res) {
            Bd_Log::warning("getStudentExprCntInfo error, return false");
            return [];
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getStudentExprCntInfo errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return [];
        }
        return $res['data']['list'] ?: [];
    }
    
    public static function getPersonInGroupIds(int $staffUid, array $groupIds) {
        if (empty($staffUid) || empty($groupIds)) {
            return false;
        }

        $header = [
            'pathinfo' => '/dataproxy/userprofile/getPersonList',
        ];
        $params = [
            'staffUid' => [$staffUid],
            'groupId'  => $groupIds,
            'needGroupChildren' => 1
        ];

        $res = Api_Ral::Ral($params, $header, self::RAL_NAME_JSON, Api_Ral::METHOD_POST);
        if (false === $res) {
            Bd_Log::warning("getPersonInGroupIds error, return false");
            return false;
        }

        if (!isset($res['errNo']) || $res['errNo']) {
            Bd_Log::warning("getPersonInGroupIds errNo:{$res['errNo']} errMsg:{$res['errMsg']}");
            return false;
        }

        Bd_Log::notice("getPersonInGroupIds success, staffUid: ". json_encode($staffUid)." groupIds: ".json_encode($groupIds)."  res:".json_encode($res));

        if (isset($res['data']['total']) && $res['data']['total'] == 1) {
            return true;
        }

        return false;
    }

}