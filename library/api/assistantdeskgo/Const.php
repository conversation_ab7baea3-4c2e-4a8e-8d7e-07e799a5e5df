<?php

class Api_Assistantdeskgo_Const {

    const RAL_SERVICE_NAME = 'assistantdeskgo';

    const PATH_API_SYNC_METRICS_COMMON = '/assistantdeskgo/api/metrics/syncmetricscommon';
    const PATH_API_GRAY_HIT = '/assistantdeskgo/api/gray/hit';

    const PATH_API_AI_RECORD = '/assistantdeskgo/ai/studentcallrecord';

    const PATH_GET_PINYIN = '/assistantdeskgo/api/tool/getpinyin';

    const GRAY_KEY_NEW_ARK = 'new_ark_gray';

    const PATH_API_STUDY_FEEDBACK_GET_POINT = '/assistantdeskgo/api/studyfeedback/getpointbytid';

    const PATH_API_STUDY_FEEDBACK_GET_EXPLAIN = '/assistantdeskgo/api/studyfeedback/getexplain';

    const PATH_API_GROUP_FILTER_GROUP_BIND = '/assistantdeskgo/api/scenetouch/groupfilter/groupbind';
    const PATH_API_SCENE_CONTEXT_OPTIONS = '/assistantdeskgo/api/scenetouch/scenecontext/options';
}