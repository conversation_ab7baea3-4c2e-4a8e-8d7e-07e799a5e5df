<?php

class Api_Assistantdeskgo_Api {
    /**
     * 通过assistantdeskgo转发kafka消息
     * @param $messageList
     * @return bool|mixed
     */
    public static function syncMetricsCommon($messageList) {
        if (empty($messageList)) {
            return false;
        }

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_SYNC_METRICS_COMMON,
            'Content-Type' => 'application/json'
        ];
        $params = [
            'messageList' => $messageList,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("syncMetricsCommon error, return false");
        }

        $data = $data === false ? false : $data['data'];
        return $data;
    }

    /**
     * 判断是否命中灰度
     */
    public static function grayHit($personUid, $key) {
        if (empty($personUid) || empty($key)) {
            return false;
        }

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_GRAY_HIT,
        ];
        $params = [
            'personUid' => $personUid,
            'key' => $key,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("grayHit error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function studyFeedbackGetPointByTid($tidList) {
        if (empty($tidList)) {
            return false;
        }

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_STUDY_FEEDBACK_GET_POINT,
        ];
        $params = [
            'tidList' => $tidList,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("studyFeedbackGetPointByTid error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function studyFeedbackGetExplainByPointId($pointIdList) {
        if (empty($pointIdList)) {
            return false;
        }

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_STUDY_FEEDBACK_GET_EXPLAIN,
        ];
        $params = [
            'pointIdList' => $pointIdList,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("studyFeedbackGetExplainByPointId error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function groupFilterGroupBind($params) {
        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_GROUP_FILTER_GROUP_BIND,
        ];
        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("studyFeedbackGetExplainByPointId error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function getSceneContextOptions($assistantUid, $courseId, $lessonId, $sceneType, $key) {
        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_SCENE_CONTEXT_OPTIONS,
        ];
        $params = [
            'assistantUid' => $assistantUid,
            'courseId' => $courseId,
            'lessonId' => $lessonId ?? 0,
            'sceneType' => $sceneType,
            'key' => $key,
        ];
        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("getSceneContextOptions error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function getAIRecord($studentUid, $courseId,$type,$page,$pageSize) {

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_API_AI_RECORD,
            'cookie'       => $_COOKIE,
            'Content-Type' => 'application/json'
        ];
        $params = [
            'courseId' => $courseId,
            'studentUid' => $studentUid,
            'pageTab' => $type,
            'page' => $page,
            'pageSize' => $pageSize,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        Bd_Log::notice("getAIRecord".json_encode($data));
        if (false === $data) {
            Bd_Log::warning("getAIRecord error, return false");
        }
        $data = $data === false ? false : $data['data'];
        return $data;
    }

    public static function getPinYin($names) {

        $header = [
            'pathinfo' => Api_Assistantdeskgo_Const::PATH_GET_PINYIN,
            'Content-Type' => 'application/json'
        ];
        $params = [
            'chineseName' => $names,
        ];

        $data = Api_Ral::Ral($params, $header, Api_Assistantdeskgo_Const::RAL_SERVICE_NAME);
        if (false === $data) {
            Bd_Log::warning("getPinYin error, return false");
        }
        $data = $data === false ? false : $data['data']['pingYin'];

        Bd_Log::notice("getPinYin".json_encode($data));
        return $data;
    }
}