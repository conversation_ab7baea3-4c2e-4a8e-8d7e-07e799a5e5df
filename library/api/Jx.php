<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2019/8/12
 * Time: 10:06 AM
 * 教学报告
 */
class Api_Jx{

    const TYPE_STAGE  = 'stage';
    const TYPE_LESSON = 'lesson';
    const API_MAX_NUM = 50;

    const RAL_NAME = 'jx';

    const API_MAX_STUDENT_NUM = 200;

    /**
     * 获取阶段报告list
     * @param $courseId
     * @param $studentUids
     * @return bool
     */
    public static function getCourseReportUrlList($courseId, $studentUids){

        $courseId = intval($courseId);

        $studentUids = is_array($studentUids) ? $studentUids : [$studentUids];

        if(0 >= $courseId || empty($studentUids)){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/getcoursereporturllist",
        ];

        $ret = Api_Ral::ral(['uids' => $studentUids, 'courseId' => $courseId], $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * @param $courseId
     * @return array|mixed
     * @wiki https://yapi.zuoyebang.cc/project/201/interface/api/170273
     */
    public static function getReportRules($courseId) {
        if (!$courseId) {
            Bd_Log::warning("[Param Error] " . __METHOD__ . $courseId);
            return [];
        }
        $arrHeader = [
            'pathinfo' => "/jxreport/api/getreportrulelist",
        ];

        $ret = Api_Ral::ral(['courseId' => $courseId], $arrHeader, Api_Config::MODULE_JXREPORT);

        Bd_Log::notice(__METHOD__ . json_encode([$ret, $arrHeader, $courseId]));

        return $ret['data'] ?? [];
    }

    /**
     * 编辑阶段报告评语
     * @param $courseId
     * @param $studentUid
     * @return bool
     */
    public static function setCourseReportContent($courseId, $studentUid, $num, $content){

        if(0 >= $courseId || 0 >= $studentUid || 0 >= $num){
            Bd_Log::warning("setCourseReportContent param error");
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/setcoursereportcontent",
        ];

        $ret = Api_Ral::ral(['uid' => $studentUid, 'courseId' => $courseId, 'num' => $num, 'content' => $content], $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * Notes:获取学生互动题明细（班主任出镜）--从inclass迁过来，出入参数不变
     *
     * @param     $lessonId
     * @param     $studentUid
     * @param  int $isPlayback 是否包含回放答题数据
     *
     * @return bool
     * 原接口yapi：yapi: http://yapi.zuoyebang.cc/project/624/interface/api/73915 @赵伟
     * 新接口和原接口出入参一样：刘蔚然维护
     */
    public static function getExerciseInfo($lessonId, $studentUid, int $isPlayback = 0) {

        $lessonId   = intval($lessonId);
        $studentUid = intval($studentUid);


        if (!$lessonId || !$studentUid) {
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxdainteract/api/getstuinteractinfo",
        ];

        $params = array(
            'studentUid' => $studentUid,
            'lessonId'   => $lessonId,
            'isPlayback' => $isPlayback,
        );

        $ret = Api_Ral::ral($params, $arrHeader, Api_Config::MODULE_JXDAINTERACT);

        if (false === $ret || 0 != $ret['errNo']) {
            Bd_Log::warning("Error:[New getExerciseInfo][network error], Detail:[studentUid:$studentUid lessonId:$lessonId,ret=" . @json_encode($ret));
            return false;
        }
        return $ret['data'];
    }

    /**
     * 获取阶段报告知识点
     * @param $studentUid
     * @param $courseId
     * @param $num
     * @return bool
     */
    public static function getCourseReportPoint($studentUid, $courseId, $num){

        $courseId = intval($courseId);

        $studentUid = intval($studentUid);

        $num = intval($num);

        if(0 >= $courseId || 0 >= $studentUid || 0 >= $num){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/getcoursereportpoint",
        ];

        $ret = Api_Ral::ral(['studentUid' => $studentUid, 'courseId' => $courseId, 'num' => $num], $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * 获取课堂报告知识点
     * @param $studentUid
     * @param $lessonId
     * @return bool
     */
    public static function getLessonReportPoint($studentUid, $lessonId){

        $lessonId = intval($lessonId);

        $studentUid = intval($studentUid);

        if(0 >= $lessonId || 0 >= $studentUid){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/getlessonreportpoint",
        ];

        $ret = Api_Ral::ral(['studentUid' => $studentUid, 'lessonId' => $lessonId], $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * 获取学情分析数据--单章节
     * http://yapi.zuoyebang.cc/project/201/interface/api/55456
     * @param $courseId
     * @param $studentUid
     * @param $lessonList
     * @param int $lastLessonId
     * @return bool
     */
    public static function getSingleLessonCommunication($courseId, $studentUid, $lessonList, $lastLessonId = 0){

        $studentUid = intval($studentUid);
        $courseId   = intval($courseId);

        if(0 >= $studentUid || empty($lessonList) || 0 >= $courseId){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/getsinglelessoncommunication",
        ];

        $input = [
            'courseId'   => $courseId,
            'studentUid' => $studentUid,
            'lessonList' => $lessonList,
        ];

        if($lastLessonId){
            $input['lastLessonId'] = $lastLessonId;
        }

        $ret = Api_Ral::ral($input, $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * 获取学情分析数据--多章节
     * http://yapi.zuoyebang.cc/project/201/interface/api/55456
     * @param $courseId
     * @param $studentUid
     * @param $lessonList
     * @param int $lastLessonId
     * @return bool
     */
    public static function getMultiLessonCommunication($courseId, $studentUid, $lessonList, $lastLessonId = 0){

        $studentUid = intval($studentUid);
        $courseId   = intval($courseId);

        if(0 >= $studentUid || empty($lessonList) || 0 >= $courseId){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/jxreport/api/getmultilessoncommunication",
        ];

        $input = [
            'courseId'   => $courseId,
            'studentUid' => $studentUid,
            'lessonList' => $lessonList,
        ];

        if($lastLessonId){
            $input['lastLessonId'] = $lastLessonId;
        }

        $ret = Api_Ral::ral($input, $arrHeader, Api_Config::MODULE_JXREPORT);

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * 获取学生课中是否可以聊天
     * @尤贵
     * @yapi:
     * @param $lessonId
     * @param $studentUids
     * @param $liveRoomId
     * @return bool | array
     */
    public static function getStudentTalkStatus($lessonId, $studentUids, $liveRoomId){

        $liveRoomId = intval($liveRoomId);
        $lessonId   = intval($lessonId);

        if(0 >= $lessonId || empty($studentUids) || !is_array($studentUids)){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/chat/ajax/getstudenttalkstatusbatch",
        ];


        $reqArray = [];
        $studentUidsCount = count($studentUids);
        $loopNum  = ceil($studentUidsCount / self::API_MAX_NUM);
        for ($i = 0 ; $i < $loopNum; $i ++){

            $loopStart          = $i * self::API_MAX_NUM;
            $sliceStudentUids   = array_slice($studentUids, $loopStart, self::API_MAX_NUM);

            $params = [
                'lessonId'       => $lessonId,
                'studentUidList' => @json_encode($sliceStudentUids),
                'liveRoomId'     => $liveRoomId,
            ];

            $reqArray["req{$i}"] = [Api_Config::MODULE_CHATNEW, "post", $params, mt_rand(), $arrHeader];

        }

        $ret    = Api_Ral::ralMulti($reqArray);
        $data   = [];
        foreach ($ret as $reqKey => $item){
            if(false != $item){
                $item = @json_decode($item, true);
                if(0 != $item['errNo'] || empty($item['data']) || !is_array($item['data'])){
                    continue;
                }

                $reqData = $item['data'];
                foreach ($reqData as $studentUid => $detail){
                    $data[$studentUid] = $detail;
                }
            }
        }

        return $data;
    }


    /**
     * @param $lessonId
     * @param $classId
     * @param $liveRoomId
     * @return array|bool
     * 提供人 尤贵
     * 文档：http://yapi.zuoyebang.cc/project/372/interface/api/75329
     */
    public static function getClassTalkStatus($lessonId, $classId, $liveRoomId){

        $lessonId   = intval($lessonId);
        $classId    = intval($classId);
        $liveRoomId = intval($liveRoomId);

        if(0 >= $lessonId || 0 >= $classId || 0 >= $liveRoomId){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/chat/ajax/getclasstalkstatus",
        ];

        $arrParam = [
            'lessonId'      => $lessonId,
            'classId'       => $classId,
            'liveRoomId'    => $liveRoomId,
        ];

        $ret = Api_Ral::ral($arrParam, $arrHeader, Api_Config::MODULE_CHATNEW);
        if (false === $ret || 0 != $ret['errNo']) {
            Bd_Log::Warning("[getclasstalkstatus] Detail[lessonId:{$lessonId} classId:{$classId} liveRoomId:{$liveRoomId}]");
            return false;
        }
        return $ret['data'];
    }



    /**获取阶段测的推荐详情--用于打开阶段测试页用来展示阶段测结果信息 @王歌阳
     * 文档：http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=94237163
     * @param $examId
     * @param $studentUids
     * @return array|bool
     * 返回示例
     * 返回示例
        {
            "errNo":0,
            "errStr":"success",
            "data":{
                "111111":{//学生uid
                    "currentClassTypeId":1//当前班型
                    "currentClassTypeName":提高班//当前班型名称
                    "isAnswerStageTest":0//是否作答阶段测
                    "examScore":85,//学生阶段测成绩
                    "recommendClassTypeId":2//推荐的班型
                    "recommendClassTypeName":尖端班//推荐的班型名称
                    "showStudent":1//是否展示给学生，即是否是最终建议
                },
            }
        }
     */
    public static function getStageRecInfoByExamId($examId, $studentUids) {
        //接口下线
        return false;
        $examId = intval($examId);
        if (!$examId || empty($studentUids) || !is_array($studentUids)){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/examui/stageraiseclass/getstagerecommendcourse",
        ];

        $reqArray = [];
        $studentUidsCount   = count($studentUids);
        $loopNum            = ceil($studentUidsCount / self::API_MAX_STUDENT_NUM);
        for ($i = 0 ; $i < $loopNum; $i ++){

            $loopStart          = $i * self::API_MAX_STUDENT_NUM;
            $sliceStudentUids   = array_slice($studentUids, $loopStart, self::API_MAX_STUDENT_NUM);

            $sliceStudentUids   = implode(',', $sliceStudentUids);
            if (empty($sliceStudentUids)){
                continue;
            }
            $params = [
                'examId'            => $examId,
                'studentUidList'    => $sliceStudentUids,
            ];

            $reqArray["req{$i}"] = [Api_Config::MODULE_EXAMUINEW, "post", $params, mt_rand(), $arrHeader];
        }

        $ret    = Api_Ral::ralMulti($reqArray);
        $data   = [];
        if (!is_array($ret)){
            Bd_Log::Warning("[getStageRecInfoByExamId] Detail[examId:{$examId} $studentUids".@json_encode($studentUids),"]");
            return $data;
        }
        foreach ($ret as $reqKey => $item){
            if (false == $item){
                continue;
            }
            $item = @json_decode($item, true);
            if(0 != $item['errNo'] || empty($item['data']) || !is_array($item['data'])){
                continue;
            }

            $reqData = $item['data'];
            foreach ($reqData as $studentUid => $detail){
                $data[$studentUid] = $detail;
            }
        }
        return $data;
    }



    /**获取阶段测的推荐详情和推荐课程--用于列表页展示结果和课程推荐页展示课程信息(单学生)  @王歌阳
     * 文档：http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=94237163
     * @param $courseId
     * @param $studentUids
     * @return array|bool
     * 返回示例
     * 返回示例
        {
            "errNo":0,
            "errStr":"success",
            "data":{
                "123123":{//学生uid
                    "currentClassTypeId":1//当前班型
                    "currentClassTypeName":提高班//当前班型名称
                    "isAnswerStageTest":0//是否作答阶段测
                    "examScore":85,//学生阶段测成绩
                    "recommendClassTypeId":2//推荐的班型   【51，50，352 为班型ID，只存在这三个值，和王哥阳确认过】
                    "recommendClassTypeName":2//推荐的班型名称
                    "recommendCourseId":[123,456],//推荐的courseId数组
                },
            }
        }
     */
    public static function getStageRecInfoAndRecCourseByCourseId($courseId, $studentUids){

        $courseId = intval($courseId);
        if (!$courseId || empty($studentUids) || !is_array($studentUids)){
            return false;
        }
        return [];
    }

    public static function getHxLessonReport($lessonIds, $studentUid){

        $lessonIds = is_array($lessonIds) ? $lessonIds : [$lessonIds];
        if(!$studentUid || !$lessonIds){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "jxreport/api/getlessonreporturllist",
        ];

        $arrParam = [
            'lessonIdArr'      => json_encode($lessonIds),
            'studentUid'       => $studentUid,
        ];

        $ret = Api_Ral::ral($arrParam, $arrHeader, Api_Config::MODULE_JXREPORT);
        if (false === $ret || 0 != $ret['errNo']) {
            Bd_Log::Warning("[getLessonReportUrlList] Detail[lessonId:" . json_encode($lessonIds) . " studentUid:{$studentUid}]");
            return false;
        }
        return $ret['data'];
    }

    /**
     * 获取生成报告的规则
     * http://yapi.zuoyebang.cc/project/201/interface/api/170273
     * @param $courseId
     * @return false|mixed
     */
    public static function getReportRuleList($courseId) {

        $arrHeader = [
            'pathinfo' => "jxreport/api/getreportrulelist",
        ];

        $arrParam = [
            'courseId' => $courseId,
        ];

        $ret = Api_Ral::ral($arrParam, $arrHeader, Api_Config::MODULE_JXREPORT);
        if (false === $ret || 0 != $ret['errNo']) {
            Bd_Log::Warning("[getlessonreporturllist] Detail[courseId:" . $courseId . "]");
            return false;
        }
        return $ret['data'];
    }

    /**
     * 学习报告数据
     *
     * 目前只有互动题对/答/总数据
     *
     * http://yapi.zuoyebang.cc/project/201/interface/api/165021
     *
     * @param array $arrStudentUid
     * @param int $intLessonId
     * @return bool|array
     */
    public static function getLessonReportExercise($arrStudentUid, $intLessonId) {
        if (empty($arrStudentUid) || !is_array($arrStudentUid) || (0 >= $intLessonId)) {
            return false;
        }

        $arrHeader = [
            'pathinfo' => '/jxreport/api/getlessonreports',
        ];
        $arrParam = [
            'lessonId' => $intLessonId,
        ];
        $arrRequest = [];
        $arrStudentUid = array_chunk($arrStudentUid, 200);
        foreach ($arrStudentUid as $i => $arrStuId) {
            $arrParam['uidList'] = $arrStuId;

            $arrRequest["req{$i}"] = [Api_Config::MODULE_JXREPORT, Api_Ral::METHOD_POST, $arrParam, mt_rand(), $arrHeader];
        }

        $arrRet = [];
        $mixedRet = Api_Ral::ralMulti($arrRequest);
        foreach ($mixedRet as $k => $v) {
            if ($v) {
                $v = json_decode($v, true);
                if (is_array($v) && (0 === $v['errNo'])
                    && isset($v['data'][$intLessonId])
                    && is_array($v['data'][$intLessonId])
                ) {
                    foreach ($v['data'][$intLessonId] as $stuId => $item) {
                        $arrRet[$stuId] = $item;
                    }
                }
            }
        }

        return $arrRet;
    }

    /**
     * Notes: 教学前端模块 获取日积月累任务列表
     * yapi地址 http://yapi.zuoyebang.cc/project/2391/interface/api/128347
     * 提供人 <EMAIL>
     * @param $skuIds
     * @param int $interfaceId
     * @return bool | array
     */
    public static function getTaskList($courseId){

        /**testCode**/
        //$courseId = 411844;


        $result = [];

        $courseId = intval($courseId);
        if(!$courseId){
            Bd_Log::notice('Params 不正确:$courseId');
            return $result;
        }

        $arrHeader = ['pathinfo' => '/frontcourse/public/api/accumulate'];
        $arrParams = [
            'courseId' => $courseId,
        ];
        $arrParams['sign'] = Hk_Util_StrCrypt::encodeStr(http_build_query($arrParams));

        $response = Api_Ral::ral($arrParams, $arrHeader, Api_Config::MODULE_FRONTCOURSENEW);

        Bd_Log::notice(" [arrHeader:" . json_encode($arrHeader) . "], [arrParams:" . json_encode($arrParams) . "], [response:" . json_encode($response) . "]");

        if($response === false || !is_array($response) || $response['errNo'] > 0){
            return $response;
        }

        $result = $response['data'];

        return $result;
    }


    /**
     * Notes: 教学前端模块 获取每日一练任务列表
     * yapi地址 http://yapi.zuoyebang.cc/project/2391/interface/api/128347
     * 提供人 <EMAIL>
     * @param $skuIds
     * @param int $interfaceId
     * @return bool | array
     */
    public static function getDailyList($courseId){

        /**testCode**/
        //$courseId = 410795;

        $result = [];

        $courseId = intval($courseId);
        if(!$courseId){
            Bd_Log::notice('Params 不正确:$courseId');
            return $result;
        }

        $arrHeader = ['pathinfo' => '/frontcourse/public/api/dailytest'];
        $arrParams = [
            'courseId' => $courseId,
        ];
        $arrParams['sign'] = Hk_Util_StrCrypt::encodeStr(http_build_query($arrParams));

        $response = Api_Ral::ral($arrParams, $arrHeader, Api_Config::MODULE_FRONTCOURSENEW);

        Bd_Log::notice(" [arrHeader:" . json_encode($arrHeader) . "], [arrParams:" . json_encode($arrParams) . "], [response:" . json_encode($response) . "]");

        if($response === false || !is_array($response) || $response['errNo'] > 0){
            return $response;
        }

        $result = $response['data'];

        return $result;
    }

    /**
     * Notes: 设置外化缓存
     * 提供人 <EMAIL>
     * @param $studentUid
     * @param $list
     * @return bool | array
     */
    public static function setAssistantList($studentUid, $list)
    {
        if(empty($studentUid) || empty($list) || !is_array($list)){
            return false;
        }

        $arrHeader = ['pathinfo' => 'frontcourse/public/Student/setassistantlist'];
        $arrParams = [
            'studentUid' => $studentUid,
            'json' => json_encode($list),
        ];

        $response = Api_Ral::ral($arrParams, $arrHeader, Api_Config::MODULE_FRONTCOURSENEW);

        Bd_Log::notice(" [arrHeader:" . json_encode($arrHeader) . "], [arrParams:" . json_encode($arrParams) . "], [response:" . json_encode($response) . "]");

        if($response === false || !is_array($response) || $response['errNo'] > 0){
            return false;
        }

        return true;
    }

    /**
     * Notes: 获取初高预习结果【新接口，20210128之后请使用这个】
     * @param $lessonIds
     * @return bool | array
     */
    public static function getHighGradePreviewInfo($lessonIds)
    {
        if(empty($lessonIds)){
            return false;
        }

        $lessonIds      = is_array($lessonIds) ? array_values($lessonIds) : [$lessonIds];

        $arrHeader = [
            'pathinfo' => "/frontcourse/product/exam/lessonpreviewinfo",
        ];

        $ret = Api_Ral::ral(['lessonIds' => $lessonIds], $arrHeader, Api_Config::MODULE_FRONTCOURSENEW);

        Bd_Log::notice(" [arrHeader:" . json_encode($arrHeader) . "], [arrParams:" . json_encode(['lessonIds' => $lessonIds]) . "], [Response:" . json_encode($ret) . "]");

        if(false === $ret || 0 != $ret['errNo']){
            return false;
        }

        return $ret['data'];
    }

    /**
     * 批量获取学生的课堂报告链接
     *
     * @param $lessonId
     * @param $studentUids
     * @return mixed
     */
    public static function getLessonReportUrlLpc($lessonId, $studentUids)
    {
        if (!$lessonId || !$studentUids || !is_array($studentUids)) {
            return false;
        }

        $header = [
            'pathinfo' => '/jxreport/api/getlessonreporturllpc',
        ];

        $stuIds         = array_chunk($studentUids, 200);
        $multiArrParams = [];
        foreach ($stuIds as $uids) {
            $multiArrParams[] = [
                'lessonId' => $lessonId,
                'uidList'  => json_encode($uids),
            ];
        }
        $multiRalRes = Laxin_Common_Tools::multiSendRal(Api_Config::MODULE_JXREPORT, $header, 'POST', $multiArrParams);

        $data = [];
        foreach ($multiRalRes as $singleRes) {
            if (false === $singleRes || $singleRes['errNo'] !== 0) {
                Bd_Log::warning("Error[" . __METHOD__ . "] Detail[ral,params: " . json_encode($multiArrParams) . ". detail:" . json_encode($multiRalRes) . "]");
                continue;
            }

            if (empty($singleRes['data'])) {
                Bd_Log::warning("Error[" . __METHOD__ . "] Detail[ral data empty,params: " . json_encode($multiArrParams) . ". detail:" . json_encode($multiRalRes) . "]");
                continue;
            }

            if (isset($singleRes['data'][$lessonId])) {
                foreach ($singleRes['data'][$lessonId] as $studentUid => $urls) {
                    $data[$studentUid] = $urls;
                }
            }
        }
        return $data;
    }
}
