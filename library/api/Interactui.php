<?php
/**
* @description : 互动数据
* <AUTHOR>
* Date: 2020/5/6
*/
class Api_Interactui {

    const RAL_NAME = 'interactui';
    const RAL_NAME_INTERACTME = 'interactui-me';
    const API_MAX_NUM = 20;

    /**
     * 浣熊跟课-互动题、上台、学豆
     * http://yapi.zuoyebang.cc/project/600/interface/api/88411 xumingdong
     * @param $courseId
     * @param $lessonId
     * @param $studentUids
     * @return array | boolean
     */
    public static function hxInteractData($courseId, $lessonId, $studentUids){
        if(0 >= $courseId || 0 >= $lessonId || !is_array($studentUids) || empty($studentUids)){
            return false;
        }

        $arrHeader = [
            'pathinfo' => "/interactui/v1/lessonmonit",
        ];

        $arrParams = [
            'courseId'      => $courseId,
            'lessonId'      => $lessonId,
        ];

        $studentArr = array_chunk($studentUids, self::API_MAX_NUM);
        $reqArray = [];
        foreach($studentArr as $i => $studentList){
            $arrParams['studentList'] = $studentList;
            $reqArray["req{$i}"] = array(self::RAL_NAME, 'post', $arrParams, mt_rand(), $arrHeader);
        }

        $reqArray = Api_Ral::ralMulti($reqArray);

        $retData = [];
        foreach ($reqArray as $reqKey => $item) {

            if (false != $item) {

                if (0 != $item['errNo'] || empty($item['data'])) {
                    continue;
                }

                $data = $item['data'];
                foreach($data as $values){
                    $retData[] = $values;
                }
            }
        }
        return $retData;
    }

    /**
     * 批量获取课中辅导老师触达学生的次数
     *
     * http://yapi.zuoyebang.cc/project/600/interface/api/163109
     *
     * @param int $intCourseId
     * @param array $arrStudentUid
     * @param int $intLessonId 传lessonId取指定章节下的数据，不传则取课程维度下的数据
     * @return false|array
     */
    public static function getTouchCnt($intCourseId, $arrStudentUid, $intLessonId = 0, $personUid = 0) {
        $intCourseId = intval($intCourseId);
        $intLessonId = intval($intLessonId);
        if ((0 >= $intCourseId) || empty($arrStudentUid) || !is_array($arrStudentUid)
            || (0 > $intLessonId)
        ) {
            return false;
        }

        $ralName = self::RAL_NAME;
        $arrHeader = [
            'pathinfo' => '/interactui/v2/gettouchcnt',
            'content-type' => 'application/json',
        ];
        if (Api_Assistantdeskgo_Api::grayHit($personUid, "inact_total_num_trans_gray")) {
            $ralName = self::RAL_NAME_INTERACTME;
        }
        $arrParams = [
            'courseId' => $intCourseId,
        ];
        if ($intLessonId) {
            $arrParams['lessonId'] = $intLessonId;
        }

        $arrRet = [];
        $arrRequest = [];
        $arrStudentUid = array_map('intval', $arrStudentUid);
        $arrStudentUid = array_chunk($arrStudentUid, 50);
        foreach ($arrStudentUid as $i => $studentUid) {
            $arrParams['studentUidList'] = $studentUid;

            $arrRequest["req{$i}"] = [$ralName, Api_Ral::METHOD_POST, $arrParams, mt_rand(), $arrHeader];
        }

        $mixedRet = Api_Ral::ralMulti($arrRequest);
        foreach ($mixedRet as $k => $v) {
            if ($v) {
                if ((0 === $v['errNo'])
                    && !empty($v['data']['touchList']) && is_array($v['data']['touchList'])
                ) {
                    foreach ($v['data']['touchList'] as $item) {
                        if (isset($item['studentUid'])) {
                            $arrRet[$item['studentUid']] = $item;
                        }
                    }
                }
            }
        }

        return $arrRet;
    }


    /**
     * 获取课中互动题答题情况（对答总 + 答题时间线）  并发请求
     * @param $lessonId
     * @param $studentUids
     * @param $liveRoomId
     * @return array|bool
     * http://yapi.zuoyebang.cc/project/2319/interface/api/99908 @shaohuan
     */
    public static function interactAnswerDetails($lessonId, $studentUids, $liveRoomId) {
        if (!$lessonId || !is_array($studentUids) || empty($studentUids)) {
            return false;
        }
        $arrHeader               = ['pathinfo' => '/interactui/v2/studentinteract'];
        $arrParams['lessonId']   = $lessonId;
        $arrParams['liveRoomId'] = $liveRoomId;
        $batchParamName          = 'studentUid';
        $studentUidsChunk        = array_chunk($studentUids, self::MAX_ID_COUNT);
        $batchDataArr            = self::ralMultiCommon($arrParams, $studentUidsChunk, $batchParamName, $arrHeader);
        Bd_Log::notice('Api_Interactui::interactAnswerDetails' . json_encode($arrHeader + $arrParams + $batchDataArr));
        //结果合并
        $result = ['sendCount' => 0, 'studentList' => []];
        foreach ($batchDataArr as $batchData) {
            if (isset($batchData['sendCount'])) {
                $result['sendCount'] = $batchData['sendCount'];
            }
            if (isset($batchData['studentList'])) {
                $result['studentList'] = array_merge($result['studentList'], $batchData['studentList']);
            }
        }
        return $result;
    }

    /**
     * ral并发公共调用部分
     * @param $params
     * @param $chunkArr
     * @param $paramBatchName
     * @param $arrHeader
     * @return array
     */
    private static function ralMultiCommon($params, $chunkArr, $paramBatchName, $arrHeader) {
        $reqArray = [];
        foreach ($chunkArr as $i => $chunks) {
            $params[$paramBatchName] = $chunks;
            $reqArray["req{$i}"]     = [self::RAL_NAME, "post", $params, mt_rand(), $arrHeader];
        }
        $ret  = Api_Ral::ralMulti($reqArray);
        $data = [];
        foreach ($ret as $reqKey => $item) {
            if (false != $item) {
                $item = json_decode($item, true);
                if (0 != $item['errNo'] || empty($item['data'])) {
                    continue;
                }
                $data[] = $item['data'];
            }
        }
        return $data;
    }
}
