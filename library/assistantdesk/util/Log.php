<?php
/**
 * Created by PhpStorm.
 * User: zhao<PERSON>yang
 * Desc: rd日志记录
 */

class AssistantDesk_Util_Log {

    private static $maxLen = 3500; // max len 4k， 留一点buff

    /**
     * RD调试使用，方便排查问题，不种cookie，线上不会打印日志
     * @param $data
     */
    public static function debug($data) {
        if (isset($_COOKIE['RD_DEBUG_MODE'])) {
            if (is_array($data)) {
                $data = json_encode($data);
            }
        }
        if (is_array($data)) {
            $data = json_encode($data);
        }
        // cookie调试时，打印日志
        if (isset($_COOKIE['RD_DEBUG_MODE']) && intval($_COOKIE['RD_DEBUG_MODE']) == 1) {
            //rd调试使用
            self::formatOutput($data);
        }
        // 参数调试时，返回debug数据
        if (isset($_GET['RD_DEBUG_MODE']) && intval($_GET['RD_DEBUG_MODE']) == 2) {
            echo "<pre>" . print_r([
                    'data' => $data,
                    'logId' => Bd_Log::genLogID(),
                    'time' => time(),
                ], true) . "</pre>";
            echo "\r\n";
        }
    }

    /**
     * 输出日志，避免被截断，分多行打
     * @param $data
     */
    private static function formatOutput($data) {
        if (strlen($data) <= self::$maxLen) {
            Bd_Log::notice("RD_DEBUG_MODE==={$data}===");
            return;
        }

        $chunk = str_split($data, self::$maxLen);
        $sign = md5($data);
        foreach ($chunk as $i => $str) {
            Bd_Log::notice("RD_DEBUG_MODE[{$sign}.{$i}]==={$str}===");
        }
    }
}
