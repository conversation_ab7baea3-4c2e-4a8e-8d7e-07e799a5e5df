<?php
/**
 * 企业标签名称格式化类
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/20
 * Time: 11:43
 */

/**
 * class AssistantDesk_Data_StudentListFormat
 */
class AssistantDesk_Data_StudentListFormat
{

    // 当前类太大了，按领域拆到不同trait中
    use AssistantDesk_Data_Domain_TradeTrait;
    use AssistantDesk_Data_Domain_Contract;
    use AssistantDesk_Data_Domain_DeerTrait;
    use AssistantDesk_Data_Domain_OrderStudent;
    use AssistantDesk_Data_Domain_QiweiCourseStudent;
    use AssistantDesk_Data_Domain_LpcTrait;
    use AssistantDesk_Data_Domain_LbpTrait;
    use AssistantDesk_Data_Domain_StudentTrait;
    use AssistantDesk_Data_Domain_SportWeekReport;
    use AssistantDesk_Data_Domain_RJYL;

    /**
     * 为了避免后续维护无法区分不同领域trait文件，后续统一维护到下述维度；按最小维度划分
     * C：课程维度
     * A：资产维度，可以是辅导，也可以是lpc
     * L：章节维度
     * U：用户维度，一般指学生
     * Leads：例子维度
     * Other：其他维度的，一般少用又不能按上述key划分的维度
     */
    use AssistantDesk_Data_Domain_CA;
    use AssistantDesk_Data_Domain_CAL;
    use AssistantDesk_Data_Domain_CAU;
    use AssistantDesk_Data_Domain_CU;
    use AssistantDesk_Data_Domain_LU;
    use AssistantDesk_Data_Domain_U;
    use AssistantDesk_Data_Domain_Leads;
    use AssistantDesk_Data_Domain_Other;
    use AssistantDesk_Data_Domain_ArkGo;
    use AssistantDesk_Data_Domain_SopCLU;
    use AssistantDesk_Data_Domain_ExerciseNoteTask;

    private static $student;
    private static $studentUid;
    private static $leads = [];
    private static $leadsId;
    private static $lessonIdInfoMap = [];

    private static $dalData;

    private static $L2rSpaceSeasonData = [];

    private static $currentFieldRuleInfo = [];

    public static function setFieldRule($fieldRule) {
        self::$currentFieldRuleInfo = $fieldRule;
    }
    public static function getFieldRule() {
        return self::$currentFieldRuleInfo;
    }
    const WATCH_STATUS_LESSON_YES     = 1; // 章节已观看
    const WATCH_STATUS_LESSON_NO      = 2; // 章节未观看
    const WATCH_STATUS_LESSON_PENDING = 3; // 章节未开始

    const HIDDEN_EXT = 'Hidden';
    const PLAYBACK_WATCH_STATUS_7D_YES        = 1;
    const PLAYBACK_WATCH_STATUS_7D_NO         = 2;
    const PLAYBACK_FINISH_WATCH_STATUS_7D_YES = 1;
    const PLAYBACK_FINISH_WATCH_STATUS_7D_NO  = 2;

    const EVALUATE_SOURCE_TYPE_COURSE = 1; // 课程
    const EVALUATE_DIMENSION_TYPE_FD = 1; // 辅导老师维度
    /**
     * @param $studentUid
     */
    public static function initStudent($studentUid){
        self::$student = [];
        self::$studentUid = $studentUid;
    }
    public static function getCurrentStudentUid() {
        return self::$studentUid;
    }
    public static function getCurrentLeadsId() {
        return self::$leadsId;
    }
    public static function initLeads($leadsId, $studentUid){
        self::$student = [];
        self::$leads = [];
        self::$leadsId = $leadsId;
        self::$studentUid = $studentUid;
    }

    public static function getHiddenPhoneStudent($canCopy = false)
    {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        foreach (self::$student as $key => $value) {
            if ($canCopy && $key == 'phone'){
                continue;
            }
            if (self::strEndsWith($key, "Url")) {
                //特殊处理,头像中的数字以及加密结果不应该隐藏
                continue;
            }
            if (in_array($key, ['eduProbePdf', 'eduProbePdfName', 'isBindFn', 'fnDemandSurveyLink', 'fnStudyReportLink', 'encodeCourseId', 'encodeStudentId', 'fnProbeReportLink'])) {
                continue;
            }
            self::$student[$key] = AssistantDesk_Tools::getHiddenPhone($value);
        }
        return self::$student;
    }

    public static function strEndsWith($haystack, $needle): bool {
        return '' === $needle || ('' !== $haystack && 0 === substr_compare($haystack, $needle, -\strlen($needle)));
    }

    public static function getHiddenPhoneStudentLeads($canCopy = false): array {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        foreach (self::$leads as $key => $value) {
            if ($canCopy && $key == 'phone') {
                continue;
            }
            if (self::strEndsWith($key, "Url")) {
                //特殊处理,头像中的数字以及加密结果不应该隐藏
                continue;
            }
            if (in_array($key, ['eduProbePdf', 'eduProbePdfName', 'isBindFn', 'fnDemandSurveyLink', 'fnStudyReportLink', 'encodeCourseId', 'encodeStudentId', 'fnProbeReportLink'])) {
                continue;
            }
            self::$leads[$key] = AssistantDesk_Tools::getHiddenPhone($value);
        }
        return array_merge(self::getHiddenPhoneStudent($canCopy), self::$leads);
    }

    public static function getStudent() {
        return self::$student;
    }

    public static function getStudentName() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentInfos         = Common_Singleton::getInstanceData(AssistantDesk_Student::class, "getStudentInfo", [AssistantDesk_Data_CommonParams::$studentUids]);
        self::$student['studentName'] = $studentInfos[self::$studentUid]['studentName'] ? $studentInfos[self::$studentUid]['studentName'] : '';      //学生uid
        self::$student['phone']       = $studentInfos[self::$studentUid]['registerPhone'] ? $studentInfos[self::$studentUid]['registerPhone'] : "";  //注册手机号
    }

    /**
     * 学生信息
     * @throws Common_Exception
     * @throws ReflectionException
     */
    public static function getStudentInfo(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
                "leap_user_type",
                "isTransferStudent",
                "isTransferStudentTypeB",
                "isTransferStudentDelayed",
                "isWechatBind",
            ])
            && \AssistantDesk_Data_DataSource::beforeAddOrderFields([
                "isL2r",
                "isBoundDiscountRetain",
                "isBoundDiscount",
                "isRetain",
                "isBoundDiscount",
            ])
        ) {
            return [];
        }

        $courseInfo           = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $newCourseInfo        = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getNewCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esCuData             = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");
        $esContinueData       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");
        $levelInfo            = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLevelData");
        $studentInfos         = Common_Singleton::getInstanceData(AssistantDesk_Student::class, "getStudentInfo", [AssistantDesk_Data_CommonParams::$studentUids]);
        $changeCourseStudents = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTransferData");
        $bindRelationStudents = Common_Singleton::getInstanceData(Api_KunpengEnterprise::class, "getWxidByUid",[AssistantDesk_Data_CommonParams::$assistantUid,AssistantDesk_Data_CommonParams::$studentUids,Assistant_Const_WeChat::WORK_APP_ID]);
        //学生uid
        self::$student['studentUid'] = self::$studentUid;
        //学生姓名
        self::$student['studentName'] = $studentInfos[self::$studentUid]['studentName'] ? $studentInfos[self::$studentUid]['studentName'] : '';      //学生uid
        //昵称
        self::$student['nickname'] = $studentInfos[self::$studentUid]['uname'] ? $studentInfos[self::$studentUid]['uname'] : '';              //昵称
        //电话
        self::$student['phone'] = $studentInfos[self::$studentUid]['registerPhone'] ? $studentInfos[self::$studentUid]['registerPhone'] : "";  //注册手机号
        //是否是新例子
        self::$student["isNewLead"] = !self::getWechatBBDCondition();
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'studentUid', [
            'title'        => '【学生信息:学生id,学生姓名，昵称，电话（注册手机号）】',
            'studentUid'   => self::$studentUid,
            'studentName'  => self::$student['studentName'],
            'nickname'     => self::$student['nickname'],
            'phone'        => self::$student['phone'],
            'assistantUid' => AssistantDesk_Data_CommonParams::$assistantUid,
            'source'       => 'dau',
            "isNewLead"    => "判定学生是否是新例子，是否添加微信和帮帮盾有外呼记录"

        ]);
        //用户类型
        self::$student['userType'] = $esCuData[self::$studentUid]['leap_user_type'] ? $esCuData[self::$studentUid]['leap_user_type'] : '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userType', [
            'title'  => '【学生信息：用户类型】',
            'source' => 'cu.leap_user_type',
        ]);
        //二级续报
        self::$student['isLevelTwo'] = intval($esContinueData[self::$studentUid]['isL2r']);               //二级续报
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLevelTwo', [
            'title'  => '【学生信息：二级续报】',
            'source' => 'es订单:is_l2r',
        ]);
        //转班生
        self::$student['isTransferCourse'] = in_array(self::$studentUid, $changeCourseStudents) ? 1 : 0;        //转班生
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferCourse', [
            'title'  => '【学生信息：转班生】',
            'source' => '数据库表tblAssistantChangeCourse是否有这个学生数据',
        ]);
        //插班生
        self::$student['isTransferStudent'] = intval($esCuData[self::$studentUid]['isTransferStudent']);        //插班生
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudent', [
            'title'  => '【学生信息：插班生】',
            'source' => 'cu.is_transfer_student',
        ]);
        //是否是B类插班生
        self::$student['isTransferStudentB'] = 0;
        if (Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]){
            //小学才展示B类插班字段
            self::$student['isTransferStudentB'] = intval($esCuData[self::$studentUid]['isTransferStudentTypeB']);       //B插班生
        }

        //B类插班生和插班生同时存在时，只展示B类插班生
        if (self::$student['isTransferStudentB'] &&self::$student['isTransferStudent']){
            self::$student['isTransferStudent'] = 0;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudentB', [
            'title'  => '【学生信息：B类插班生】',
            'source' => 'cu.is_transfer_student_typeB,B类插班生和插班生同时存在时，只展示B类插班生',
        ]);

        //是否反确认
        if ($bindRelationStudents) {
            $bindRelationStudentMap = [];
            foreach ($bindRelationStudents as $bindRelationStudent) {
                $bindRelationStudentMap[$bindRelationStudent['studentUid']] = 1;
            }
            if ($bindRelationStudentMap[self::$studentUid]){
                self::$student['wechatRelationBind'] = 1;
            }
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wechatRelationBind', [
            'title'  => '【学生信息：是否反确认】',
            'source' => '鲲鹏反确认接口',
        ]);

//        //延期插班生
//        self::$student['isTransferStudentDelayed'] = 0;
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudentDelayed', [
//            'title'  => '【学生信息：延期插班生】',
//            'source' => 'cu.is_transfer_student_delayed,延期插班生逻辑初高已经去掉',
//        ]);

        //根据不同课程类型取不同的微信绑定状态
        self::$student['wechatServiceType'] = intval($esCuData[self::$studentUid]['isWechatBind']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wechatServiceType', [
            'title'  => '【学生信息：微信绑定状态】',
            'source' => 'cu.is_wechat_bind',
        ]);
        //暑寒单报留存
        self::$student['singleApplyRetain'] = 0;//暑寒单报留存
        //联报状态根据学季不同取不同字段值
        self::$student['isBound'] = 0;
        self::$student['isBoundHover'] = 0;
        if ($newCourseInfo['season'] == '春' || $newCourseInfo['season'] == '秋') {
            self::$student['isBound'] = intval($esContinueData[self::$studentUid]['isBoundDiscountRetain']);
            self::$student['isBoundHover'] = 1;
        }
        if ($newCourseInfo['season'] == '暑' || $newCourseInfo['season'] == '寒') {
            self::$student['isBound'] = intval($esContinueData[self::$studentUid]['isBoundDiscount']);
            self::$student['isBoundHover'] = 2;
            //暑寒单报留存
            if (isset($esContinueData[self::$studentUid]['isRetain']) && $esContinueData[self::$studentUid]['isRetain'] === 1 && isset($esContinueData[self::$studentUid]['isBoundDiscount']) && $esContinueData[self::$studentUid]['isBoundDiscount'] === 0) {
                self::$student['singleApplyRetain'] = 1;
            }
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isBound', [
            'title'  => '【学生信息：联报】',
            'source' => '春秋：es订单：is_bound_discount_retain，暑寒：es订单：cu.is_bound_discount',
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'singleApplyRetain', [
            'title'  => '【学生信息：暑寒单报留存】',
            'source' => '暑寒学季 && es订单.is_retain==1 && es订单.is_bound_discount == 0',
        ]);

        //会员里程等级 仅小学、低幼展示 20221009下掉
//        self::$student['levelVip'] = 0;
//        self::$student['levelTitle'] = '';
        $department = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']];
        //低价用户
        if (in_array($department, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY, Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL])) {
            self::$student['userRoleLowPrice'] = '非低';
            $userRoles                         = Common_Singleton::getInstanceData(Api_UserRole::class, 'getUserRoleIdsByBatch', [AssistantDesk_Data_CommonParams::$studentUids]);
            $userRole                          = $userRoles[self::$studentUid] ?? [];
            if (in_array(Api_UserRole::ROLE_ID_3780, $userRole)) {
                self::$student['userRoleLowPrice'] = '低';
            }
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userRoleLowPrice', [
                'title'  => '【学生信息：学能低价用户】',
                'source' => '接口：/userrole/coreapi/batchgetuserroles',
            ]);
        }
        //是否保价（仅初高展示）
        self::$student['pricePro'] = 0;

        if (in_array($department, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY, Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL])){
//            self::$student['levelTitle'] = $levelInfo[self::$studentUid]['levelTitle'];
//            self::$student['levelVip']   = $levelInfo[self::$studentUid]['level'];
        }else{
            self::$student['pricePro']   = $levelInfo[self::$studentUid]['pricePro'];
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'pricePro', [
            'title'  => '【学生信息：是否保价】',
            'source' => '售卖接口：/viprights/tob/user/getbatchuservipinfo',
        ]);
        self::getGroupName();
    }

    // 新版学生信息字段，分离了标签类字段。
    public static function getStudentInfoV3(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
                "leap_user_type",
                "isTransferStudent",
                "isTransferStudentTypeB",
                "isTransferStudentDelayed",
                "isWechatBind",
            ])
            && \AssistantDesk_Data_DataSource::beforeAddOrderFields([
                "isL2r",
                "isBoundDiscountRetain",
                "isBoundDiscount",
                "isRetain",
                "isBoundDiscount",
            ])
        ) {
            return [];
        }


        $studentInfos         = Common_Singleton::getInstanceData(AssistantDesk_Student::class, "getStudentInfo", [AssistantDesk_Data_CommonParams::$studentUids]);
        $bindRelationStudents = Common_Singleton::getInstanceData(Api_KunpengEnterprise::class, "getWxidByUid",[AssistantDesk_Data_CommonParams::$assistantUid,AssistantDesk_Data_CommonParams::$studentUids,Assistant_Const_WeChat::WORK_APP_ID]);
        $leadsInfos           = Common_Singleton::getInstanceData(AssistantDesk_Student::class, "getLeadsInfosByUidCidleadsIds", [AssistantDesk_Data_CommonParams::$personUid,AssistantDesk_Data_CommonParams::$courseId,AssistantDesk_Data_CommonParams::$leadsIdMapStudentUid]);
        $isLpcCourse         = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "isLpcCourse" , [AssistantDesk_Data_CommonParams::$courseId]);

        // 是否外呼标识
        $isCall = !empty($leadsInfos[self::$leadsId]['isCall']) ? true : false;
        if ($isLpcCourse){
            self::$student['isCall'] = $isCall;
        }else{
            self::$student['isCall'] = true;// 辅导常亮
        }

        //学生uid
        self::$student['studentUid'] = self::$studentUid;
        //学生姓名
        self::$student['studentName'] = $studentInfos[self::$studentUid]['studentName'] ? $studentInfos[self::$studentUid]['studentName'] : '';
        // 学生姓名拼音
        self::$student['studentNamePinYin'] = $studentInfos[self::$studentUid]['studentNamePinYin'] ? $studentInfos[self::$studentUid]['studentNamePinYin'] : '';

        //昵称
        self::$student['nickname'] = $studentInfos[self::$studentUid]['uname'] ? $studentInfos[self::$studentUid]['uname'] : '';              //昵称
        //电话
        self::$student['phone'] = $studentInfos[self::$studentUid]['registerPhone'] ? $studentInfos[self::$studentUid]['registerPhone'] : "";  //注册手机号
        //是否反确认
        if ($bindRelationStudents) {
            $bindRelationStudentMap = [];
            foreach ($bindRelationStudents as $bindRelationStudent) {
                $bindRelationStudentMap[$bindRelationStudent['studentUid']] = 1;
            }
            if ($bindRelationStudentMap[self::$studentUid]){
                self::$student['wechatRelationBind'] = 1;
            }
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wechatRelationBind', [
            'title'  => '【学生信息：是否反确认】',
            'source' => '鲲鹏反确认接口',
        ]);

    }

    // 新版学生信息标签字段，只包括标签类字段。
    public static function getStudentInfoTagV3(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
                "leap_user_type",
                "isTransferStudent",
                "isTransferStudentTypeB",
                "isTransferStudentDelayed",
                "isWechatBind",
            ])
            && \AssistantDesk_Data_DataSource::beforeAddOrderFields([
                "isL2r",
                "isBoundDiscountRetain",
                "isBoundDiscount",
                "isRetain",
                "isBoundDiscount",
            ])
        ) {
            return [];
        }

        $courseInfo           = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $newCourseInfo        = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getNewCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esCuData             = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");
        $esContinueData       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");
        $levelInfo            = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLevelData");
        $changeCourseStudents = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTransferData");

        //是否是新例子
        self::$student["isNewLead"] = !self::getWechatBBDCondition();
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'studentUid', [
            'title'        => '【学生信息:学生id,学生姓名，昵称，电话（注册手机号）】',
            'studentUid'   => self::$studentUid,
            'studentName'  => self::$student['studentName'],
            'nickname'     => self::$student['nickname'],
            'phone'        => self::$student['phone'],
            'assistantUid' => AssistantDesk_Data_CommonParams::$assistantUid,
            'source'       => 'dau',
            "isNewLead"    => "判定学生是否是新例子，是否添加微信和帮帮盾有外呼记录"

        ]);
        //用户类型
        self::$student['userType'] = $esCuData[self::$studentUid]['leap_user_type'] ? $esCuData[self::$studentUid]['leap_user_type'] : '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userType', [
            'title'  => '【学生信息：用户类型】',
            'source' => 'cu.leap_user_type',
        ]);
        //二级续报
        self::$student['isLevelTwo'] = intval($esContinueData[self::$studentUid]['isL2r']);               //二级续报
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLevelTwo', [
            'title'  => '【学生信息：二级续报】',
            'source' => 'es订单:is_l2r',
        ]);
        //转班生
        self::$student['isTransferCourse'] = in_array(self::$studentUid, $changeCourseStudents) ? 1 : 0;        //转班生
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferCourse', [
            'title'  => '【学生信息：转班生】',
            'source' => '数据库表tblAssistantChangeCourse是否有这个学生数据',
        ]);
        //插班生
        self::$student['isTransferStudent'] = intval($esCuData[self::$studentUid]['isTransferStudent']);        //插班生
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudent', [
            'title'  => '【学生信息：插班生】',
            'source' => 'cu.is_transfer_student',
        ]);
        //是否是B类插班生
        self::$student['isTransferStudentB'] = 0;
        if (Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]){
            //小学才展示B类插班字段
            self::$student['isTransferStudentB'] = intval($esCuData[self::$studentUid]['isTransferStudentTypeB']);       //B插班生
        }

        //B类插班生和插班生同时存在时，只展示B类插班生
        if (self::$student['isTransferStudentB'] &&self::$student['isTransferStudent']){
            self::$student['isTransferStudent'] = 0;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudentB', [
            'title'  => '【学生信息：B类插班生】',
            'source' => 'cu.is_transfer_student_typeB,B类插班生和插班生同时存在时，只展示B类插班生',
        ]);

//        //延期插班生
//        self::$student['isTransferStudentDelayed'] = 0;
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTransferStudentDelayed', [
//            'title'  => '【学生信息：延期插班生】',
//            'source' => 'cu.is_transfer_student_delayed,延期插班生逻辑初高已经去掉',
//        ]);

        //根据不同课程类型取不同的微信绑定状态
        self::$student['wechatServiceType'] = intval($esCuData[self::$studentUid]['isWechatBind']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wechatServiceType', [
            'title'  => '【学生信息：微信绑定状态】',
            'source' => 'cu.is_wechat_bind',
        ]);
        //暑寒单报留存
        self::$student['singleApplyRetain'] = 0;//暑寒单报留存
        //联报状态根据学季不同取不同字段值
        self::$student['isBound'] = 0;
        self::$student['isBoundHover'] = 0;
        if ($newCourseInfo['season'] == '春' || $newCourseInfo['season'] == '秋') {
            self::$student['isBound'] = intval($esContinueData[self::$studentUid]['isBoundDiscountRetain']);
            self::$student['isBoundHover'] = 1;
        }
        if ($newCourseInfo['season'] == '暑' || $newCourseInfo['season'] == '寒') {
            self::$student['isBound'] = intval($esContinueData[self::$studentUid]['isBoundDiscount']);
            self::$student['isBoundHover'] = 2;
            //暑寒单报留存
            if (isset($esContinueData[self::$studentUid]['isRetain']) && $esContinueData[self::$studentUid]['isRetain'] === 1 && isset($esContinueData[self::$studentUid]['isBoundDiscount']) && $esContinueData[self::$studentUid]['isBoundDiscount'] === 0) {
                self::$student['singleApplyRetain'] = 1;
            }
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isBound', [
            'title'  => '【学生信息：联报】',
            'source' => '春秋：es订单：is_bound_discount_retain，暑寒：es订单：cu.is_bound_discount',
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'singleApplyRetain', [
            'title'  => '【学生信息：暑寒单报留存】',
            'source' => '暑寒学季 && es订单.is_retain==1 && es订单.is_bound_discount == 0',
        ]);

        //会员里程等级 仅小学、低幼展示 20221009下掉
//        self::$student['levelVip'] = 0;
//        self::$student['levelTitle'] = '';
        $department = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']];
        //低价用户
        if (in_array($department, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY, Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL])) {
            self::$student['userRoleLowPrice'] = '非低';
            $userRoles                         = Common_Singleton::getInstanceData(Api_UserRole::class, 'getUserRoleIdsByBatch', [AssistantDesk_Data_CommonParams::$studentUids]);
            $userRole                          = $userRoles[self::$studentUid] ?? [];
            if (in_array(Api_UserRole::ROLE_ID_3780, $userRole)) {
                self::$student['userRoleLowPrice'] = '低';
            }
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userRoleLowPrice', [
                'title'  => '【学生信息：学能低价用户】',
                'source' => '接口：/userrole/coreapi/batchgetuserroles',
            ]);
        }
        //是否保价（仅初高展示）
        self::$student['pricePro'] = 0;

        if (in_array($department, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY, Zb_Const_GradeSubject::GRADE_STAGE_PRESCHOOL])){
//            self::$student['levelTitle'] = $levelInfo[self::$studentUid]['levelTitle'];
//            self::$student['levelVip']   = $levelInfo[self::$studentUid]['level'];
        }else{
            self::$student['pricePro']   = $levelInfo[self::$studentUid]['pricePro'];
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'pricePro', [
            'title'  => '【学生信息：是否保价】',
            'source' => '售卖接口：/viprights/tob/user/getbatchuservipinfo',
        ]);
        self::getGroupName();
    }

    public static function getUserTypeV2() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "leap_user_type",
        ])) {
            return [];
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        //用户类型
        self::$student['userType'] = $esCuData[self::$studentUid]['leap_user_type'] ? $esCuData[self::$studentUid]['leap_user_type'] : '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userType', [
            'title'  => '【学生信息：用户类型】',
            'source' => 'cu.leap_user_type',
        ]);
    }

    /**
     * 监督人信息
     * @throws Common_Exception
     */
    public static function getGuardianInfo(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $studentInfos    = Common_Singleton::getInstanceData(AssistantDesk_Student::class, "getStudentInfo", [AssistantDesk_Data_CommonParams::$studentUids]);
        $wxLightData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getWxLightStudents");

        self::$student['guardian']         = $studentInfos[self::$studentUid]['guardian'] ? $studentInfos[self::$studentUid]['guardian'] : '';
        self::$student['guardianPhone']        = $studentInfos[self::$studentUid]['guardianPhone'] ? $studentInfos[self::$studentUid]['guardianPhone'] : "";
        self::$student['guardianWechat']       = $studentInfos[self::$studentUid]['parentWebchat'] ? $studentInfos[self::$studentUid]['parentWebchat'] : '';
        //20190711微信关联取工作站手动关联和鲲鹏关联数据的并集
        self::$student['guardianWechatLight'] = $wxLightData[self::$studentUid] ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'guardian', [
            'title'  => '【监督人信息：监督人，电话，微信，鲲鹏微信关联状态 + 工作站手动微信关联状态】',
            'source' => '监督人基本信息：dau：guardian,guardianPhone,parentWebchat，绑定状态：鲲鹏接口/kpstaff/oapi/friend/getrelation和数据库表：getWxLightStudents，手动点亮和接口有一个则为绑定',
        ]);
    }

    /**
     * 小班号
     */
    public static function getGroupName(){
        if (AssistantDesk_Data_DataSource::isTogetherApi()) {
            return self::getGroupNameNew();
        }

        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $acsData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsData");

        self::$student['classId']   = $acsData[self::$studentUid]['classId'] ?? 0;
        self::$student['groupName'] = $acsData[self::$studentUid]['groupName'] ?? '未分班';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'classId', [
            'title'  => '【学生信息：小班号】',
            'source' => '数据库获取classId， groupName',
        ]);
    }

    public static function getGroupNameNew() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $acsData                    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLeadsStudentList", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
        ]);
        $classId                    = $acsData[self::$leadsId]['classId'] ?? 0;
        self::$leads['classId']   = $classId;
        $groupNameMap               = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getGroupNameMap");
        self::$leads['groupName'] = $groupNameMap[$classId] ?? '未分班';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'classId', [
            'title'  => '【学生信息：小班号】',
            'source' => '接口/allocate/api/getnormalleadsbycourseassistant, 学生维度获取classId， 接口/assignclass/api/getclasscode获取 groupName',
        ]);
    }

    /**
     * 插班状态，保价状态，退费状态
     * @throws ReflectionException
     */
    public static function getTransferPriceRefundTags() {

        self::$student['TransferStr'] = '';
        $transferPriceRefundTags = [];
        // 不显示这些标签了，第一列有
//        self::$student['priceProStr'] = self::$student['pricePro'] == 1 ? '保' : '';
//        self::$student['priceProStr'] && $transferPriceRefundTags[] = self::$student['priceProStr'];
//        $courseInfo                   = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
//        $gradeStage                   = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']];
//        if (in_array($gradeStage, [Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY])) {
//            self::getStudentTransferStatus();
//            self::$student['TransferStr'] = AssistantDesk_TaskMap::TRANSFER_MAP[self::$student['studentTransferStatus']] ?? '';
//        }
//        if (in_array($gradeStage, [Zb_Const_GradeSubject::GRADE_STAGE_SENIOR])) {
//            self::getSeniorTransferStatus();
//            self::$student['TransferStr'] = AssistantDesk_TaskMap::TRANSFER_MAP_SENIOR[self::$student['seniorTransferStatus']] ?? '';
//        }
//        self::$student['TransferStr'] && $transferPriceRefundTags[] = self::$student['TransferStr'];
//        self::getRefundSpecialStatus();
//        self::getRefundAfterClassSpecialStatus();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $refundStatus                     = self::$student['refundSpecialStatus'] && self::$student['refundAfterClassSpecialStatus'];
        self::$student['refundStatusStr'] = $refundStatus == 1 ? '退' : '';
        self::$student['refundStatusStr'] && $transferPriceRefundTags[] = self::$student['refundStatusStr'];
        self::$student['transferPriceRefundTags'] = $transferPriceRefundTags;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferPriceRefundTags', [
            'title'  => '【退费 标签】',
            'source' => 'refundSpecialStatus与refundAfterClassSpecialStatus为真，则标签为"退"，否则为空',
        ]);
    }

    /**
     * 学员当前课程累计完成预习数（新增） 章节数/总章节数
     * @throws ReflectionException
     */
    public static function getPreviewFinishNumRatio() {
        self::getPreviewFinishNum();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['previewFinishNumRatio'] = intval(self::$student['previewFinishNum']) . "/" . $lessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferPriceRefundTags', [
            'title'  => '【学员当前课程累计完成预习数（新增） 章节数/总章节数】',
            'source' => 'refundSpecialStatus与refundAfterClassSpecialStatus为真，则标签为"退"，否则为空',
        ]);
    }

    /**
     * 到课章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getAttendNumRatio() {
        self::getAttendNum();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['attendNumRatio'] = intval(self::$student['attendNum']) . "/" . $lessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendNumRatio', [
            'title' => '【总章节数】',
            '数据源'   => 'dal获取到 课程下主体章节个数',
        ]);
    }

    /**
     * 到课5min章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getAttendLessonNums5Ratio() {
        self::getAttendLessonNums5();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['attendLessonNums5Ratio'] = intval(self::$student['attendLessonNums5']) . "/" . $lessonCnt;
    }

    /**
     * 到课四分之一章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getAttendLessonNumsQuarterRatio() {
        self::getAttendLessonNumsQuarter();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['attendLessonNumsQuarterRatio'] = intval(self::$student['attendLessonNumsQuarter']) . "/" . $lessonCnt;
    }

    /**
     * 累计完课章节数 / 总章节数
     * @throws ReflectionException
     */
    public static function getHasFinishedNumRatio() {
        self::getHasFinishedNum();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['hasFinishedNumRatio'] = intval(self::$student['hasFinishedNum']) . "/" . $lessonCnt;
    }

    /**
     * 累计回放章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getHasPlaybackNumRatio() {
        self::getHasPlaybackNum();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['hasPlaybackNumRatio'] = intval(self::$student['hasPlaybackNum']) . "/" . $lessonCnt;
    }

    /**
     * 获取累计巩固练习完成数/ 总章节数
     * @throws ReflectionException
     */
    public static function getHomeworkFinishNumRatio() {
        self::getHomeworkFinishNum();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['homeworkFinishNumRatio'] = intval(self::$student['homeworkFinishNum']) . "/" . $lessonCnt;

    }

//    /**
//     * 阶段测提交次数/ 总提交数
//     * @throws ReflectionException
//     */
//    public static function getStageTestSubmitCountRatio() {
//        $courseInfo         = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
//        $lessonList         = $courseInfo['lessonList'];
//        $lessonIds          = array_column($lessonList, 'lessonId');
//        $lessonIdMapExamIds = Common_Singleton::getInstanceData(Api_Exam::class, "getExamIdsByLessonIds", [$lessonIds, Api_Exam::BIND_TYPE_STAGE]);
//        $totalSubmitCnt     = 0;
//        foreach ($lessonIdMapExamIds as $examIds) {
//            $examIds && $totalSubmitCnt++;
//        }
//        self::getStageTestSubmitCount();
//        self::$student['stageTestSubmitCountRatio'] = intval(self::$student['stageTestSubmitCount']) . "/" . $totalSubmitCnt;
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'stageTestSubmitCountRatio', [
//            'title' => '【总提交数】',
//            '数据源'   => '接口：/examcore/v1/getrelation（获取试卷绑定关系信息http://yapi.zuoyebang.cc/project/2535/interface/api/96286）',
//        ]);
//    }

    /**
     * 同步练习提交章节数/ 总应提交次数
     * @throws ReflectionException
     */
    public static function getSynchronousPracticeSubmitCountRatio() {
        self::getSynchronousPracticeSubmitCount();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $courseInfo         = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList         = $courseInfo['lessonList'];
        $lessonIds          = array_column($lessonList, 'lessonId');
        $lessonIdMapExamIds = Common_Singleton::getInstanceData(Api_Exam::class, "getExamIdsByLessonIds", [$lessonIds, Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]);
        $totalSubmitCnt     = 0;
        foreach ($lessonIdMapExamIds as $examIds) {
            $examIds && $totalSubmitCnt++;
        }
        self::$student['synchronousPracticeSubmitCountRatio'] = intval(self::$student['synchronousPracticeSubmitCount']) . "/" . $totalSubmitCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'synchronousPracticeSubmitCountRatio', [
            'title' => '【总应提交次数】',
            '数据源'   => '接口：/examcore/v1/getrelation（获取试卷绑定关系信息http://yapi.zuoyebang.cc/project/2535/interface/api/96286）',
        ]);
    }

    /**
     * 打卡次数/应打卡次数
     */
    public static function getKsTaskDataRatio() {
        self::getKsTaskData();
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentUid = self::$studentUid;
        self::$student['ksCountRatio'] = intval(self::$student['ksCount']) . "/" . intval(self::$student['ksTotalCount']);
    }

    /**
     * 插班生

     */
    public static function getTransferStudent(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "hasLateEnrollee",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['hasLateEnrollee'] = $esCuData[self::$studentUid]['hasLateEnrollee'] == 1 ? AssistantDesk_TaskMap::LATEE_NROLLER : AssistantDesk_TaskMap::NO_LATEE_NROLLER;
    }

    /**
     * 课后1分钟回访状态
     */
    public static function getIsAfterClassPhoneBackInterview1min() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "isAfterClassPhoneBackInterview1min",
        ])) {
            return [];
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['isAfterClassPhoneBackInterview1min'] = $esCuData[self::$studentUid]['isAfterClassPhoneBackInterview1min'] ?: 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isAfterClassPhoneBackInterview1min', [
            'title'  => '【课后1分钟回访状态】',
            'source' => 'es.cu.is_after_class_phone_back_interview_1min',
        ]);
    }

    /**
     * 小学插班生
     */
    public static function getStudentTransferStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "isTransferStudentTypeB",
            "isTransferStudent",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        if ($esCuData[self::$studentUid]['isTransferStudentTypeB'] == 1){
            self::$student['studentTransferStatus'] = AssistantDesk_TaskMap::LATEE_NROLLER_B;//B类插班
        }elseif ($esCuData[self::$studentUid]['isTransferStudent']){
            self::$student['studentTransferStatus'] = AssistantDesk_TaskMap::NO_LATEE_NROLLER_B;//非B类插班==其他插班
        }else{
            self::$student['studentTransferStatus'] = AssistantDesk_TaskMap::NO_LATEE_NROLLER;//非插班
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'studentTransferStatus', [
            'title'  => '【小学插班生】',
            'source' => 'es.cu.is_transfer_student_typeB,es.cu.is_transfer_student,',
            '解释'     => '先判断ransfer_student_typeB为1则为b类插班生，is_transfer_student为1则为非B类插班，否则为非插班'
        ]);
    }

    /**
     * 初高插班生
     */
    public static function getSeniorTransferStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "isTransferStudent",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        if ($esCuData[self::$studentUid]['isTransferStudent']) {
            self::$student['seniorTransferStatus'] = AssistantDesk_TaskMap::LATEE_NROLLER;//插班
        } else {
            self::$student['seniorTransferStatus'] = AssistantDesk_TaskMap::NO_LATEE_NROLLER;//非插班
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'seniorTransferStatus', [
            'title'  => '【初高插班生】',
            'source' => 'es.cu.is_transfer_student,',
        ]);
    }

    /**
     * 获取预约礼盒信息
     */
    public static function getBookGiftBox(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $bookGiftBo   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getBookGiftBox");

        self::$student['bookGiftBoxStatus'] = $bookGiftBo[self::$studentUid]['bookGiftBoxStatus'] ?: -1;
        self::$student['bookGiftBoxInfo']   = $bookGiftBo[self::$studentUid]['bookGiftBoxInfo'] ?: '接口异常';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'bookGiftBoxStatus', [
            'title'  => '【获取预约礼盒信息】',
            'source' => '接口获取：/fnmis/api/formdatalist',
        ]);
    }

    /**
     * 家访状态
     */
    public static function getInterviewStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "hasInterview",
            "interviewType",
            "isInterviewByPhone",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        $studentInfos = Common_Singleton::getInstanceData("AssistantDesk_Student", "getStudentInfo", [AssistantDesk_Data_CommonParams::$studentUids]);

        //监督人类型 监督人手机号是否完善
        $guardianIsCompleted        = $studentInfos[self::$studentUid]['guardian'] && $studentInfos[self::$studentUid]['guardianPhone'] ? 1 : 0;

        //维系记录是否完善
        $studentInterviewIsComplete = $esCuData[self::$studentUid]['hasInterview'] ? $esCuData[self::$studentUid]['hasInterview'] : 0;

        //电话或微信是否合规 isInterviewByWechat es中无数据，db中默认全部是1，$sopData[self::$studentUid]['isInterviewByWechat']改为1
        $interviewIsComplete        = $esCuData[self::$studentUid]['interviewType'] ? ($esCuData[self::$studentUid]['interviewType'] == 1 ? $esCuData[self::$studentUid]['isInterviewByPhone'] : 1) : 0;

        //家访筛选，是否完成家访
        self::$student['interview'] = 0;

        //hasInterview-填写维系记录，填写维系记录并且电话或微信是否合规即为已完成家访
        //start::家访细粒度为未家访、电话家访、微信家访 by pangjunchao 0218
        $studentInterviewIsCompleteTitle    = "维系记录";
        $interviewIsCompleteTitle           = "电话或微信沟通合规";
        if($esCuData[self::$studentUid]['hasInterview']){
            if($esCuData[self::$studentUid]['interviewType'] == Service_Data_StudentInterview::CHANNEL_TYPE_WECHANT){
                $interviewIsCompleteTitle = "微信沟通合规";
                if($interviewIsComplete){
                    self::$student['interview'] = 2;
                }
            }
            if($esCuData[self::$studentUid]['interviewType'] == Service_Data_StudentInterview::CHANNEL_TYPE_PHONE){
                $interviewIsCompleteTitle = "电话沟通合规";
                if($interviewIsComplete){
                    self::$student['interview'] = 1;
                }
            }
        }
        //end::家访细粒度为未家访、电话家访、微信家访 by pangjunchao 0218


        $interviewStatus  = [
            [
                'title'     => '监督人类型及手机号',
                'status'    => $guardianIsCompleted,
            ],
            [
                'title'     => $studentInterviewIsCompleteTitle,
                'status'    => $studentInterviewIsComplete,
            ],
            [
                'title'     => $interviewIsCompleteTitle,
                'status'    => $interviewIsComplete,
            ],
        ];

        self::$student['interviewStatus'] = $interviewStatus;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interviewStatus', [
            'title'  => '【家访状态】',
            'source' => '监督人及手机号：dau数据，
            维系记录：es.cu.has_interview，
            电话或微信是否合规：es：cu：interview_type，is_interview_by_phone，has_interview，根据interview_type和is_interview_by_phone判断类型，has_interview为1',
        ]);
    }

    /**
     * 家访状态（非班课）
     */
    public static function getInterviewStatusOther(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "hasInterview",
        ])) {
            return [];
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        //家访状态字段
        self::$student['isInterview']     = $esCuData[self::$studentUid]['hasInterview'] ? $esCuData[self::$studentUid]['hasInterview'] : 0;

        //维系记录是否完善
        $studentInterviewIsComplete = $esCuData[self::$studentUid]['hasInterview'] ? $esCuData[self::$studentUid]['hasInterview'] : 0;

        $interviewStatus = [
            [
                'title'     => '维系记录',
                'status'    => $studentInterviewIsComplete,
            ],
        ];
        self::$student['interviewStatus'] = $interviewStatus;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interviewStatus', [
            'title'  => '【家访状态（非班课）】',
            'source' => '维系记录：es.cu.has_interview',
        ]);
    }

    public static function getBackInterviewStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "hasBackinterview",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['isBackInterview'] = intval($esCuData[self::$studentUid]['hasBackinterview']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isBackInterview', [
            'title'  => '【回访状态】',
            'source' => 'es.cu.has_backinterview',
        ]);
    }

//    /**
//     * 设备状态
//     */
//    public static function getEquipmentStatus(){
//        $esCuData     = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");
//
//        self::$student['equipmentStatus'] = intval($esCuData[self::$studentUid]['isDeviceDebug']);
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'equipmentStatus', [
//            'title'  => '【设备状态】',
//            'source' => 'es.cu.is_device_debug',
//        ]);
//    }

    /**
     * 续报状态
     */
    public static function getContinueStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "continueStatus",
        ])) {
            return [];
        }
        $continueData     = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");

        self::$student['continueStatus'] = intval($continueData[self::$studentUid]['continueStatus']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'continueStatus', [
            'title'=>'【续报状态】',
            'source' => 'es.idl_trade_order_assistant',
            '解释'     => 'isL3r=1或isL2r=1为二级续报；isL1r=1且isL2r=0为一级续报;isL2r=0且isL2Once=1为退续报,默认为未续报'
        ]);
    }

    /**
     * 转化状态（非班课）
     */
    public static function getContinueStatusOther(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "otherContinueStatus",
        ])) {
            return [];
        }

        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData", [1]);

        //续报状态
        //退转化
        self::$student['continueStatus']      = $esContinueData[self::$studentUid]['otherContinueStatus'] ?? 0;
    }
//
//    /**
//     * 专题课报名状态
//     */
//    public static function getRecruitStatus(){
//        $sopRecruitData     = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getSopRecruitData");
//
//        self::$student['recruitStatus'] = $sopRecruitData[self::$studentUid]['isEnroll'] ? 1 : 0;
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'recruitStatus', [
//            'title'  => '【专题课报名状态】',
//            'source' => '数据库tblKuokeActivity取活动，然后取状态：es.idl_assistant_course_student_recruit_action.is_enroll',
//        ]);
//    }

    /**
     * 获取留存状态
     */
    public static function getRetainStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "retainTime",
            "retainCourseId",
            "retainStatus",
        ])) {
            return [];
        }

        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        $courseIds = Tools_Array::getNewValueArray($esContinueData, 'retainCourseId');
        $courseIds = array_unique(array_filter($courseIds));

        $courseInfo = [];
        if (!empty($courseIds)) {
            $courseInfo = Common_Singleton::getInstanceData(Api_Dal::class, "getCourseBaseByCourseIds", [$courseIds, ['courseId', 'courseName']]);
        }

        $retainCourseId = $esContinueData[self::$studentUid]['retainCourseId'] ?? 0;

        self::$student['retainStatus']     = $esContinueData[self::$studentUid]['retainStatus'] ? $esContinueData[self::$studentUid]['retainStatus'] : 0;
        self::$student['retainTime']       = $esContinueData[self::$studentUid]['retainTime'] ? date('Y-m-d H:i:s', $esContinueData[self::$studentUid]['retainTime']) : '';
        self::$student['retainCourseName'] = $courseInfo[$retainCourseId]['courseName'] ?? '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'retainStatus', [
            'title'  => '【留存状态】',
            'source' => '从es订单：retain_detail详情中获取',
        ]);
    }

    /**
     * 摸底测状态
     */
    public static function getCheckTestStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $examFinishStudentUids = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getExamFinishStudentUids", [AssistantDesk_Data_CommonParams::$courseId]);
        $apiTestCourseIds      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTestCourseIds", [AssistantDesk_Data_CommonParams::$courseId]);
        $examUrls              = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getExamUrls");
        $freeStudents = [];
        if($apiTestCourseIds[AssistantDesk_Data_CommonParams::$courseId]){
            $freeStudents = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFreeStudents", [AssistantDesk_Data_CommonParams::$courseId]);
        }

        self::$student['longUrl'] = '';
        self::$student['examResultUrl'] = '';

        if(is_array($examFinishStudentUids) && in_array(self::$studentUid, $examFinishStudentUids)){
            self::$student['examResult']      = AssistantDesk_TaskMap::EXAM_RESULT_FINISH;      //完成
            self::$student['checkTestFinishStatus']      = AssistantDesk_TaskMap::EXAM_RESULT_FINISH;      //完成
            self::$student['examResultUrl']   = sprintf(AssistantDesk_Config::EXAM_RESULT_URL, AssistantDesk_Data_CommonParams::$courseId, self::$studentUid);
        }else if($freeStudents[self::$studentUid]){
            self::$student['examResult']      = AssistantDesk_TaskMap::EXAM_RESULT_FREE;        //免测
            self::$student['checkTestFinishStatus']      = AssistantDesk_TaskMap::EXAM_RESULT_FREE;        //免测
        }else{
            self::$student['examResult']      = AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH;    //未完成
            self::$student['checkTestFinishStatus']      = AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH;    //未完成
            self::$student['longUrl']         = AssistantDesk_Config::EXAM_BASE_LONG_URL . $examUrls[self::$studentUid];
        }

        if(!$apiTestCourseIds[AssistantDesk_Data_CommonParams::$courseId]){
            self::$student['examResult']  = AssistantDesk_TaskMap::EXAM_RESULT_FREE;        //免测
            self::$student['checkTestFinishStatus']  = AssistantDesk_TaskMap::EXAM_RESULT_FREE;        //免测
            self::$student['longUrl']     = "";
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'examResult', [
            'title'  => '【摸底测状态】',
            'source' => '从教研试卷获取',
        ]);
    }

    /**
     * 获取帮帮定级测状态
     */
    public static function getTmkGradingStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $tmkTest      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTmkTest");
        $tmkCourseUrl = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTmkCourseUrl");

        self::$student['tmkUrl']          = $tmkCourseUrl[AssistantDesk_Data_CommonParams::$courseId] ? : '';
        self::$student['tmkRetStatus']        = $tmkTest[AssistantDesk_Data_CommonParams::$courseId][self::$studentUid]['levelExact'] ? 1 : 0;
        self::$student['tmkLevel']        = $tmkTest[AssistantDesk_Data_CommonParams::$courseId][self::$studentUid]['levelExact'] ? '推荐'.Api_Xeng::$tmkExamLevelMap[$tmkTest[AssistantDesk_Data_CommonParams::$courseId][self::$studentUid]['levelExact']] : '';
    }

    /**
     * 预约状态
     */
    public static function getReserveStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "reserveStatus",
        ])) {
            return [];
        }

        $reserveData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['reserveStatus'] = intval($reserveData[self::$studentUid]['reserveStatus']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'reserveStatus', [
            'title'  => '【预约状态】',
            'source' => 'es.订单.pre_status,pre_time, is_order_summer, is_order_autumn',
        ]);
    }

    /**
     * 联报状态
     */
    public static function getBoundStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "isBoundDiscountRetain",
        ])) {
            return [];
        }

        $continueData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['boundStatus'] = intval($continueData[self::$studentUid]['isBoundDiscountRetain']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'boundStatus', [
            'title'  => '【联报状态】',
            'source' => 'es订单：字段：is_bound_discount_retain',
            '解释'     => '二级续报且联报'
        ]);
    }

//    /**
//     * 一秋三暑
//     */
//    public static function getHasPurchaseQiu() {
//        $continueData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getContinueData");
//
//        self::$student['hasPurchaseQiu'] = intval($continueData[self::$studentUid]['hasPurchaseQiu']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasPurchaseQiu', [
//            'title'  => '【一秋三暑】',
//            'source' => 'es订单：字段：has_purchase_qiu',
//        ]);
//    }

    /**
     * 续报且扩科状态
     */
    public static function getIsExpand(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "continueStatus",
            "isTransfer",
        ])) {
            return [];
        }

        $esContinueData    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['isExpand'] = intval($esContinueData[self::$studentUid]['continueStatus']) && intval($esContinueData[self::$studentUid]['isTransfer']) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isExpand', [
            'title'  => '【续报且扩科状态】',
            'source' => 'es订单：is_transfer代表扩科，续报参考续报字段',
        ]);
    }

    /**
     * 月考答题情况
     */
    public static function getMonthlyExam1(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $arrMonthlyExamConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getMonthlyExamConfig");

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'monthlyExamN', [
            'title'  => '【月考答题情况】',
            'source' => '接口-获取小英月考配置：/flipped/api/monthlytestconfig 获取examId、url和接口-获取学生试卷作答信息：/examcore/v1/getanswer ',
            '解释'=>'code取值：isFinish==1&& answerDetail不为空则code为2，表示 浣熊已提交已完成
score== answerDetail["score"]；浣熊英语相关测试字段:提交状态: 0: 未提交;1: 已提交未完成;2: 已提交已完成',
        ]);

        self::getMonthlyExamByConds(1, $arrMonthlyExamConf);
    }

    /**
     * 月考答题情况
     */
    public static function getMonthlyExam2(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $arrMonthlyExamConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getMonthlyExamConfig");

        self::getMonthlyExamByConds(2, $arrMonthlyExamConf);
    }

    /**
     * 月考答题情况
     */
    public static function getMonthlyExam3(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $arrMonthlyExamConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getMonthlyExamConfig");

        self::getMonthlyExamByConds(3, $arrMonthlyExamConf);
    }

    /**
     * 月考答题情况
     */
    public static function getMonthlyExam4(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $arrMonthlyExamConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getMonthlyExamConfig");

        self::getMonthlyExamByConds(4, $arrMonthlyExamConf);
    }

    /**
     * 月考配置
     * @param $intIndex
     * @param $arrMonthlyExamConf
     */
    public static function getMonthlyExamByConds($intIndex, $arrMonthlyExamConf){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $strExamId = $arrMonthlyExamConf[($intIndex - 1)]['examId'] ?: '';
        $strUrl = $arrMonthlyExamConf[($intIndex - 1)]['url'] ?: '';
        $strTmp = sprintf('monthlyExam%s', $intIndex);
        $strTmpScore = sprintf('%sScore', $strTmp);

        $arrMonthlyExamRet = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentMonthlyExamScore", [$strExamId]);

        self::$student[$strTmpScore] = 0;
        if (empty($strExamId)) {
            self::$student[$strTmp] = [
                'code' => 0,
                'examId' => '',
                'url' => '',
            ];
        } else {
            self::$student[$strTmpScore] = $arrMonthlyExamRet[$strExamId][self::$studentUid]['score'];
            self::$student[$strTmp] = [
                'code' => $arrMonthlyExamRet[$strExamId][self::$studentUid]['code'],
                'examId' => $strExamId,
                'url' => $strUrl,
            ];
        }
    }

    /**
     * 获取帮帮催定电话状态
     */
    public static function getUrgeGradingStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $tmkCallRecords    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTmkCallData");

        self::$student['tmkCall'] = intval($tmkCallRecords[self::$studentUid]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tmkCall', [
            'title' => '【帮帮催定电话状态】',
            '数据源'   => '数据库tblAssistantCallRecord，通话记录',
        ]);
    }

    /**
     * 获取回访电话状态
     */
    public static function getBackInterviewPhoneStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $dailyCall    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getdailyCallData");

        self::$student['dailyCall'] = intval($dailyCall[self::$studentUid]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'dailyCall', [
            'title' => '【获取回访电话状态】',
            '数据源'   => '接口：/muse/call/api/searchcalloutinfo 检索外呼表信息',
            '解释'=>'电话未覆盖0(默认赋值)、已覆盖1、已触达2：sourceType为SOURCE_TYPE_11_VALUE表示已覆盖;duration>0表示已触达;其他为未覆盖',
        ]);
    }

    /**
     * 获取续报电话状态，覆盖与触达
     */
    public static function getContinuePhoneStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $continueCall    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getContinueCallData");

        self::$student['continueCall'] = intval($continueCall[self::$studentUid]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'continueCall', [
            'title' => '【获取续报电话状态，覆盖与触达】',
            '数据源'   => '接口：/muse/call/api/searchcalloutinfo 检索外呼表信息',
            '解释'=>'电话未覆盖0(默认赋值)、已覆盖1、已触达2：sourceType为SOURCE_TYPE_CONTINUE_COURSE_VALUE表示已覆盖;duration>0表示已触达;其他为未覆盖',
        ]);
    }

    /**
     * 获取帮帮标签
     */
    public static function getBangbangTag(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "hasBangbangTag",
        ])) {
            return [];
        }
        $esCuData    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['englishLabel'] = intval($esCuData[self::$studentUid]['hasBangbangTag']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'englishLabel', [
            'title' => '【获取帮帮标签】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段has_bangbang_tag',
            '解释'=>'字段has_bangbang_tag帮帮英语标签',
        ]);
    }

    /**
     * 获取帮帮英语是否应转班
     */
    public static function getShouldChangeStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $tmkTest     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getTmkTest");
        $courseLevel = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTmkCourseLevelMap");

        self::$student['tmkShouldChange'] = !$tmkTest[AssistantDesk_Data_CommonParams::$courseId][self::$studentUid]['levelExact']['tmkLevel'] || $tmkTest[AssistantDesk_Data_CommonParams::$courseId][self::$studentUid]['levelExact'] == $courseLevel[AssistantDesk_Data_CommonParams::$courseId] ? 0 : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tmkShouldChange', [
            'title' => '【帮帮英语是否应转班】',
            '数据源'   => '帮帮英语接口：/xeng/level/getlevellist',
        ]);
    }

    /**
     * 获取家访标签是否完善，如果未完善、则展示家访问卷链接
     */
    public static function getInterviewLabel()
    {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['hasTag'])) {
            return true;
        }

        $courseInfo    = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esCuData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        $labelShortUrl = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getInterviewLabelShortUrl");

        $year = intval($courseInfo['year']);
        $season = intval($courseInfo['season']);

        self::$student['interviewLabel'] = intval($esCuData[self::$studentUid]['hasTag']);
        self::$student['interviewLabelUrl'] = !intval($esCuData[self::$studentUid]['hasTag']) && $year && $season ? $labelShortUrl : "";

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interviewLabel', [
            'title' => '【获取家访标签是否完善】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段has_tag；dal获取课程year和season',
            '解释'=>'通过year和season拼接出interviewLabelUrl（家访问卷短链接）',
        ]);
    }

    /**
     * 获取回访标签
     */
    public static function getBackInterviewTag(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['hasBackinterviewTag'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['backInterviewTag']     = intval($esCuData[self::$studentUid]['hasBackinterviewTag']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'backInterviewTag', [
            'title' => '【获取回访标签】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段has_backinterview_tag',
        ]);
    }

    /**
     * 获取续报标签
     */
    public static function getContinueTag(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['hasRetainTag'])) {
            return true;
        }
        $esCuData              = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        $continueLabelShortUrl     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "setContinueLabelShortUrl");

        self::$student['continueLabel']       = $esCuData[self::$studentUid]['hasRetainTag'] ? 1 : 0;
        self::$student['continueLabelShortUrl'] = $continueLabelShortUrl ?: "";

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'continueLabel', [
            'title' => '【续报标签】',
            '数据源'   => 'es.cu.idl_course_student_assistant.has_retain_tag',
            '解释'=>'通过dal获取课程的year、season、subject、department拼接续报标签短链url',
        ]);
    }

    /**
     * 获取阶段报告解读状态
     */
    public static function getStageReportStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['isReport',])) {
            return true;
        }
        $esCuData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");

        $phaseReport     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getPhaseReportStatus",[$esCuData]);

        self::$student['phaseReport'] = $phaseReport[self::$studentUid] ? $phaseReport[self::$studentUid] : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'phaseReport', [
            'title' => '【获取阶段报告解读状态】',
            '数据源'   => 'es.cu.is_report存在报告，数据库tblPhaseReportRecord字段:phaseKeyIds',
        ]);
    }

    /**
     * 阶段测结果
     */
    public static function getStageTestRecType(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $stageTestRule     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getHasStageTestRule");
        $stageTestRecDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStageTestRecDatas");

        $stageTestRecInfo = $stageTestRecDatas[self::$studentUid];

        //                "currentClassTypeId":1//当前班型
        //                "currentClassTypeName":提高班//当前班型名称
        //                "isAnswerStageTest":0//是否作答阶段测
        //                "examScore":85,//学生阶段测成绩
        //                "recommendClassTypeId":2//推荐的班型
        //                "recommendClassTypeName":2//推荐的班型名称
        //                "recommendCourseId":[123,456],//推荐的courseId数组

        if ($stageTestRule){
            if($stageTestRecInfo['isAnswerStageTest']){
                if (empty($stageTestRecInfo['currentClassTypeId']) || empty($stageTestRecInfo['recommendClassTypeId'])) {
                    $stageTestRecStatus = -1; //数据为空,展示 '-'
                    $stageTestRecName   = '-';//数据为空,展示 '-'
                } elseif ($stageTestRecInfo['currentClassTypeId'] == $stageTestRecInfo['recommendClassTypeId']) {
                    $stageTestRecStatus = 1;//建议续报原班
                    $stageTestRecName   = '建议续报原班';
                } else {
                    $stageTestRecStatus = $stageTestRecInfo['recommendClassTypeId'];//推荐班型
                    $stageTestRecName   = '建议续报' . $stageTestRecInfo['recommendClassTypeName'];
                }
            }else{
                $stageTestRecStatus     = 0;//未作答，默认值0
                $stageTestRecName       = '未作答';//未作答，默认值0
            }
        }else{
            $stageTestRecStatus = -1;//数据为空,展示 '-'
            $stageTestRecName   = '-';//数据为空,展示 '-'
        }

        self::$student['stageTestRecType'] = $stageTestRecStatus;
        self::$student['stageTestRecName'] = $stageTestRecName;
    }

    /**
     * 获取学生续报意愿
     * 如果没有手动标注的结果则展示机器学习的标注结果
     */
    public static function getPreContinueStatus(){
        if (AssistantDesk_Data_DataSource::isTogetherApi()) {
            return self::getPreContinueStatusNew();
        }

        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $studentFudao     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsData");

        self::$student['continueWill'] = intval($studentFudao[self::$studentUid]['preContinue']) ?: intval($studentFudao[self::$studentUid]['machinePreContinue']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'continueWill', [
            'title' => '【学生续报意愿】',
            '数据源'   => '数据库：表tblAssistantCourseStudent 字段：preContinue，machinePreContinue',
        ]);

    }
    public static function getPreContinueStatusNew(){

        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $studentFudao = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsV1Data", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$studentUids,
        ]);

        self::$student['continueWill'] = intval($studentFudao[self::$studentUid]['preContinue']) ?: intval($studentFudao[self::$studentUid]['machinePreContinue']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'continueWill', [
            'title' => '【学生续报意愿】',
            '数据源'   => '数据库：表tblAssistantCourseStudentv1 字段：preContinue，machinePreContinue',
        ]);

    }

    /**
     * 获取服务号是否绑定
     */
    public static function gttServiceTypeStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['isWechatBind',])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['serviceType'] = intval($esCuData[self::$studentUid]['isWechatBind']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'serviceType', [
            'title' => '【服务号是否绑定】',
            '数据源'   => 'es.cu.is_wechat_bind',
        ]);
    }

    /**
     * 获取学生课程维度关注状态
     * @throws Common_Exception
     */
    public static function getCourseFocus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $courseFocusData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCourseFocusData");

        self::$student['courseFocus'] = isset($courseFocusData[self::$studentUid]) ? 1 : 0;
    }

//    /**
//     * 获取上课表现指数数据
//     */
//    public static function getPerformanceScore(){
//        $studentCoursePerfs     = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getPerformanceScore");
//
//        self::$student['performanceIndex']    = $studentCoursePerfs[self::$studentUid]['performanceIndex'] ?
//            floatval(sprintf('%.1f', (float)$studentCoursePerfs[self::$studentUid]['performanceIndex'])) : 0;
//    }

    /**
     * 判断当前课程下学生提交的所有巩固练习中是否有错题(针对小学数学课程)
     * @return array|bool
     */
    public static function getExistHomeworkErrorStudent(){
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields([
            "synchronousPracticeParticipateNum",
            "synchronousPracticeCorrectNum",
            "isHomeworkSubmit",
            "homeworkSubmissions",
            "homeworkPracticeCorrectNum",
            "homeworkPracticeParticipateNum",
        ])) {
            return [];
        }
        $courseInfo         = Common_Singleton::getInstanceData(
            AssistantDesk_Course::class,
            "getCourseInfo",
            [AssistantDesk_Data_CommonParams::$courseId]
        );
        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");
        $dasAllLessonData       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasAllLessonData");
        $isCorrectHomework      = AssistantDesk_ServiceTools::isHomeworkAmendCourse($courseInfo);

        // 初始化数据
        self::$student['hasHomeworkError'] = 0;

        $allStudentLessonData = is_array($allStudentLessonData) && !empty($allStudentLessonData) ? $allStudentLessonData : [];
        $dasAllLessonData = is_array($dasAllLessonData) && !empty($dasAllLessonData) ? $dasAllLessonData : [];

        $department = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']];
        $subject    = $courseInfo['mainSubjectId'];
        $lessonIds  = Tools_Array::getArrayColumn($courseInfo['lessonList'], 'lessonId');
        $lessonIds = is_array($lessonIds) && !empty($lessonIds) ? $lessonIds : [];

        // 小初
        if (AssistantDesk_Cuotiben::showExamErrorButton($department, $subject))
        {

            foreach ($lessonIds as $lessonId){
                // 如果学生已经存在错题，不再进行章节判定
                if(self::$student['hasHomeworkError']){
                    return true;
                }

                $studentInfo = $allStudentLessonData[$lessonId][self::$studentUid];

                // 同步练习 对答总不一致存在错题 'synchronousPracticeParticipateNum', 'synchronousPracticeCorrectNum',  'synchronousPracticeTotalNum'
                if($studentInfo['synchronousPracticeParticipateNum'] != $studentInfo['synchronousPracticeCorrectNum']){
                    self::$student['hasHomeworkError'] = 1;
                    continue;
                }

                // 未提交巩固练习过滤
                if(!$studentInfo['isHomeworkSubmit']){
                    continue;
                }

                // if 提交次数大于1时 有错题
                // elseif 当提交次数是1次需要看作业状态和对答总信息
                if ($studentInfo['homeworkSubmissions'] > 1) {
                    self::$student['hasHomeworkError'] = 1;
                } elseif (
                    $studentInfo['homeworkSubmissions'] == 1){
                    //先判断是否是订正巩固练习 并判定巩固练习是否处于待批改状态。都为真直接跳出此次判断，认为没有错题

                    if($isCorrectHomework){
                        // 是订正巩固练习
                        if($dasAllLessonData[self::$studentUid][$lessonId]['homeworkRecorrect'] == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_CORRECTED){
                            continue;
                        }
                    } else {
                        // 普通的巩固练习
                        if($dasAllLessonData[self::$studentUid][$lessonId]['homeworkStatus'] == AssistantDesk_Config::HOMEWORK_STATUS_SUBMIT){
                            continue;
                        }
                    }

                    // 对答总不一致存在错题
                    if($studentInfo['homeworkPracticeCorrectNum'] != $studentInfo['homeworkPracticeParticipateNum']){
                        self::$student['hasHomeworkError'] = 1;
                    }
                }
            }
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasHomeworkError', [
            'title' => '【所有巩固练习中是否有错题(针对小学数学课程)】',
            '数据源'   => 'es.lu.exam_answer.exam11.participate_num, exam_answer.exam11.right_num两个数不相等则有错题',
        ]);
    }

    /**
     * queStatus 0 表示未开启或未填写， 1 未填写，但是可复制链接 2 表示问卷已填写
     */
    public static function getSurveyQuestionStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(['serviceSurveyDetail',])) {
            return true;
        }

        $esCuData          = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        list($queId, $queUrl)  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getQueUrl");

        // 返回对于问卷数据的处理
        $surveyList = explode(",", strval($esCuData[self::$studentUid]['serviceSurveyDetail']));

        if ($queId && in_array(strval($queId), $surveyList, true)) {
            self::$student['queStatus'] = 2;
            self::$student['queUrl']    = $queUrl;
        } elseif($queId) {
            self::$student['queStatus'] = 1;
            self::$student['queUrl']    = $queUrl;
        } else {
            self::$student['queStatus'] = -1;
            self::$student['queUrl']    = "";
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'queStatus', [
            'title' => '【调查问卷状态】',
            '数据源'   => 'es.cu.service_survey_detail, 问卷地址：数据表：tblFirstLineSurveyQuestionConfig',
        ]);
    }
//
//    /**
//     * 特惠课新加学生退费状态
//     */
//    public static function getRefundSpecialStatus(){
//        $continueData          = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['refundSpecialStatus'] = intval($continueData[self::$studentUid]['isTrueRefundSpecial']);
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'refundSpecialStatus', [
//            'title' => '【特惠课新加学生退费状态】',
//            '数据源'   => 'es：idl_trade_order_assistant.is_true_refund_special',
//            '解释'=>'is_true_refund_special有效退费',
//        ]);
//    }

//    /**
//     * 特惠课新加学生课后退费状态
//     */
//    public static function getRefundAfterClassSpecialStatus(){
//        $continueData          = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['refundAfterClassSpecialStatus'] = intval($continueData[self::$studentUid]['isTrueRefundAfterClassSpecial']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'refundAfterClassSpecialStatus', [
//            'title' => '【学员退费状态】',
//            '数据源'   => 'es订单, is_true_refund_after_class_special',
//        ]);
//    }

    /**
     * 获取微信有效会话数
     */
    public static function getEffectiveWechatNum() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["effectiveWechatNum"])) {
            return [];
        }
        $continueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;


        self::$student['effectiveWechatNum'] = intval($continueData[self::$studentUid]['effectiveWechatNum']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'effectiveWechatNum', [
            'title' => '【获取微信有效会话数】',
            '数据源'   => 'es订单, effective_wechat_num',
        ]);
    }

    /**
     * 获取累计预习完成数量
     */
    public static function getPreviewFinishNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["preFinishNum"])) {
            return [];
        }
        $esCuData          = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['previewFinishNum'] = $esCuData[self::$studentUid]['preFinishNum'] ? intval($esCuData[self::$studentUid]['preFinishNum']) : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'previewFinishNum', [
            'title'  => '【累计预习完成次数】',
            'source' => 'es：idl_course_student_assistant.pre_finish_num',
        ]);
    }

    /**
     * 获取当前章节提交次数
     */
    public static function getHomeworkSubmissions(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "homeworkSubmissions",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['homeworkSubmissions'] = $studentLessonData[self::$studentUid]['homeworkSubmissions'] ? $studentLessonData[self::$studentUid]['homeworkSubmissions'] : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkSubmissions', [
            'title'  => '【获取当前章节提交次数】',
            'source' => 'es：idl_course_student_assistant.exam_answer.exam7.submit_num',
        ]);
    }

    /**
     * 课前任务完成数
     */
    public static function getBeforeFinishNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "hxPretestFinishnum",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['beforeFinishNum'] = intval($studentLessonData[self::$studentUid]['hxPretestFinishnum']);
    }

    /**
     * 累计任务完成数
     */
    public static function getTotalFinishNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "hxAlltestAinishnum",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['totalFinishNum'] = intval($studentLessonData[self::$studentUid]['hxAlltestAinishnum']);
    }

    /**
     * 口述题完成情况
     */
    public static function getOralQuestionFinishStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "oralQuestionSubmit",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        $hasOralQuestion = Common_Singleton::getInstanceData(
            AssistantDesk_Tools::class,
            "isOralQuestionCourse",
            [[AssistantDesk_Data_CommonParams::$lessonId]]
        );

        if ($hasOralQuestion) {
            self::$student['oralQuestionFinishStatus'] = $studentLessonData[self::$studentUid]['oralQuestionSubmit'] ?: AssistantDesk_TaskMap::ORALQU_STATUS_UNSUBMIT;
        } else {
            self::$student['oralQuestionFinishStatus'] = AssistantDesk_TaskMap::ORALQU_STATUS_UNKNOWN;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'oralQuestionFinishStatus', [
            'title'  => '【口述题完成情况】',
            'source' => 'es：idl_assistant_lesson_student_action.exam_answer.exam32.is_submit',
            '解释'=>'0未提交、1已完成、2待批改、-1为-',
        ]);
    }

    /**
     * 录播是否7天内观看
     */
    public static function getIsPlaybackLongAfterUnlock7d(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "isPlaybackLongAfterUnlock7d",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPlaybackLongAfterUnlock7d'] = (int)$studentLessonData[self::$studentUid]['isPlaybackLongAfterUnlock7d'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPlaybackLongAfterUnlock7d', [
            'title'  => '【录播是否7天内观看】',
            'source' => 'es：idl_assistant_lesson_student_action.is_playback_long_after_unlock_7d',
        ]);
    }
    /**
     * 录播是否7天内完成观看
     */
    public static function getIsPlaybackFinishAfterUnlock7d(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "isPlaybackFinishAfterUnlock7d",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPlaybackFinishAfterUnlock7d'] = (int)$studentLessonData[self::$studentUid]['isPlaybackFinishAfterUnlock7d'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPlaybackFinishAfterUnlock7d', [
            'title'  => '【录播是否7天内完成观看】',
            'source' => 'es：idl_assistant_lesson_student_action.is_playback_finish_after_unlock_7d',
        ]);
    }
    /**
     * 录播累计录播观看时长
     */
    public static function getPlaybackTimeAfterUnlock(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields([
            "inclass_teacher_room_total_playback_time_v1",
        ])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $playbackDuration = $esLessonData[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'];
        self::$student['playbackTimeAfterUnlock'] = is_null($playbackDuration) ? "-" : AssistantDesk_Tools::formatDurationTime($playbackDuration);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackTimeAfterUnlock', [
            'title'  => '【录播累计录播观看时长】',
            'source' => 'es：dataware_idl_common_lesson_student.inclass_teacher_room_total_playback_time_v1',
        ]);
    }

    /**
     * 录播7天内累计观看章节数
     * @throws ReflectionException
     */
    public static function getPlaybackCntAttendAfterUnlock7d(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "playbackCntAttendAfterUnlock7d",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        self::$student['playbackCntAttendAfterUnlock7d']   = $esCuData[self::$studentUid]['playbackCntAttendAfterUnlock7d'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackCntAttendAfterUnlock7d', [
            'title'  => '【录播7天内累计观看章节数】',
            'source' => 'es：idl_course_student_assistant.playback_cnt_attend_after_unlock_7d',
        ]);
    }

    /**
     * @param array $fieldRule
     * @param null $secondFilterKey
     * @throws ReflectionException
     */
    public static function getDataSource($fieldRule = [], $secondFilterKey = null) {
        $dataSourceInfo = $fieldRule['dataSourceInfo'] ?? [];
        if (!$dataSourceInfo) {
            return;
        }
        if (AssistantDesk_Data_DataSource::isTogetherApi()) {
            $res = AssistantDesk_Data_DataSource::getStudentDataSourceData(self::$leadsId, $dataSourceInfo, $fieldRule);
        } else {
            $res = AssistantDesk_Data_DataSource::getStudentDataSourceData(self::$studentUid, $dataSourceInfo, $fieldRule);
        }
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return;
        }

        if (!is_array($res)) {
            return;
        }
        self::$student = array_merge(self::$student, $res);
    }

    /**
     * @param array $fieldRule
     * @param null $secondFilterKey
     */
    public static function getDataFromLpcFormat($fieldRule = [], $secondFilterKey = null) {
        $fieldsFuncMap  = $fieldRule['fieldsFuncMap'] ?? [];
        $filterFieldsFuncMap  = $fieldRule['filterFieldsFuncMap'] ?? [];

        if ($secondFilterKey) {
            // 如果有需要filter，仅返回filter 的字段。
            $allMap        = $fieldsFuncMap + $filterFieldsFuncMap;
            $fieldsFuncMap = [];
            if (isset($allMap[$secondFilterKey])) {
                $fieldsFuncMap = [
                    $secondFilterKey => $allMap[$secondFilterKey],
                ];
            }
        }

        if (!$fieldsFuncMap) {
            return;
        }

        foreach ($fieldsFuncMap as $returnKey => $formatFunc) {
            if (!method_exists(AssistantDesk_Data_Duxuesc_Format::class, $formatFunc)) {
                continue;
            }
            self::$leads[$returnKey] = call_user_func_array([AssistantDesk_Data_Duxuesc_Format::class, $formatFunc], [self::$leadsId, self::$studentUid]);
            $currentComment          = AssistantDesk_Data_Duxuesc_Format::getCurrentComment();
            if (!empty($currentComment)) {
                AssistantDesk_Data_CommentAdd::addCommentArr(self::$leads, $returnKey, $currentComment);

            }
        }
    }


    /**
     * 录播7天内累计完成观看章节数
     * @throws ReflectionException
     */
    public static function getPlaybackCntFinishAfterUnlock7d(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "playbackCntFinishAfterUnlock7d",
        ])) {
            return [];
        }

        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        self::$student['playbackCntFinishAfterUnlock7d']   = $esCuData[self::$studentUid]['playbackCntFinishAfterUnlock7d'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackCntFinishAfterUnlock7d', [
            'title'  => '【录播7天内累计完成观看章节数】',
            '数据源' => 'es：idl_course_student_assistant.playback_cnt_finish_after_unlock_7d',
        ]);
    }


    /**
     * 口述题提交时间
     */
    public static function getOralQuestionSubmitTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "oralQuestionSubmitTime",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        if (isset($studentLessonData[self::$studentUid]['oralQuestionSubmitTime'])) {
            self::$student['oralQuestionSubmitTime'] = date('Y-m-d H:i:s', $studentLessonData[self::$studentUid]['oralQuestionSubmitTime']);
        } else {
            self::$student['oralQuestionSubmitTime'] = '-';
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'oralQuestionSubmitTime', [
            'title'  => '【口述题提交时间】',
            '数据源' => 'es：idl_assistant_lesson_student_action.exam_answer.exam32.submit_time',
        ]);
    }

    /**
     * 口述题提交次数
     */
    public static function getOralQuestionFinishNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "oralQuestionFinishNum",
        ])) {
            return [];
        }

        $esCuData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['oralQuestionFinishNum'] = $esCuData[self::$studentUid]['oralQuestionFinishNum'] ?: '-';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'oralQuestionFinishNum', [
            'title' => '【口述题提交次数】',
            '数据源'   => 'es.cu.idl_course_student_assistant.exam32_total_submit_num',
        ]);
    }

    /**
     * 获取累计到课章节数 切到es
     */
    public static function getAttendNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["attendCount"])) {
            return [];
        }
        $esCuData          = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['attendNum'] = intval($esCuData[self::$studentUid]['attendCount']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendNum', [
            'title' => '【累计到课章节数】',
            '数据源'   => 'es.cu.attend_count',
        ]);
    }

    /**
     * 获取累计巩固练习完成数
     */
    public static function getHomeworkFinishNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["homeworkSubmitCount"])) {
            return [];
        }
        $esCuData          = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;
        self::$student['homeworkFinishNum'] = $esCuData[self::$studentUid]['homeworkSubmitCount'] ? intval($esCuData[self::$studentUid]['homeworkSubmitCount']) : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkFinishNum', [
            'title' => '【累计巩固练习完成数】',
            '数据源'   => 'es.cu.idl_course_student_assistant.homework_submit_count',
        ]);
    }

    /**
     * 获取7天通话次数
     */
    public static function getSevenDaysCallNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $callRecords = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getSevenDaysCallNum");

        self::$student['callRecordLength']    = $callRecords[self::$studentUid] ? count($callRecords[self::$studentUid]) : 0;
    }


    /**
     * 获取日常维护次数
     */
    public static function getMaintainNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentDailyData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getBackInterviewCnt");

        self::$student['backInterviewNum']    = intval($studentDailyData[self::$studentUid]);
    }

    /**
     * 预约课程和时间
     */
    public static function getReserveTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "reserveTime",
            "reserveCourseName",
        ])) {
            return [];
        }
        $reserveData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['reserveTime'] = strval($reserveData[self::$studentUid]['reserveTime']);
        self::$student['reserveCourseName'] = strval($reserveData[self::$studentUid]['reserveCourseName']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'reserveTime', [
            'title' => '【预约时间】',
            '数据源'   => 'es：idl_trade_order_assistant.pre_time',
        ]);
    }

    /**
     * 获取续报时间
     */
    public static function getContinueTime() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "continueDetail",
        ])) {
            return [];
        }
        $continueData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        $continueCourseIds = [];
        foreach ($continueData as $continue){
            $continueCourseIds[] = $continue['continueDetail']['courseId'] ?? null;
        }
        $continueCourseIds = array_unique(array_filter($continueCourseIds));
        $continueCourseInfo =  Common_Singleton::getInstanceData("Api_Dal","getCourseBaseByCourseIds", [$continueCourseIds]);


        self::$student['continueTime']        = $continueData[self::$studentUid]['continueDetail']['createTime'] ?
            date('Y-m-d H:i', $continueData[self::$studentUid]['continueDetail']['createTime']) : '';
        self::$student['continueCourse']      = ($continueData[self::$studentUid]['continueDetail']['courseId'] && $continueCourseInfo) ?
            $continueCourseInfo[$continueData[self::$studentUid]['continueDetail']['courseId']]['courseName'] : '';
    }

//    /**
//     * 专题课报名时间
//     */
//    public static function getRecruitTime(){
//        $recruitData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getSopRecruitData");
//
//        self::$student['recruitTime']  = $recruitData[self::$studentUid]['enrollTime'] ? date('Y-m-d H:i:s', $recruitData[self::$studentUid]['enrollTime']) : '-';
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'recruitTime', [
//            'title'  => '【专题课报名时间】',
//            'source' => 'es：idl_assistant_course_student_recruit_action.',
//            '解释'=>'20200725之前的老数据用enroll_time；最近报名时间用专题报名详情里的子字段 other_course_detail.enroll_time',
//        ]);
//    }

    /**
     * 获取留存时间
     */
    public static function getRetainTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "retainTime",
        ])) {
            return [];
        }

        $retainContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getEsContinueData");

        self::$student['retainTime']       = $retainContinueData[self::$studentUid]['retainTime'] ? $retainContinueData[self::$studentUid]['retainTime'] : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'retainTime', [
            'title'  => '【获取留存时间】',
            'source' => 'es：idl_trade_order_assistant.retain_detail',
            '解释'=>'取retain_detail遍历所有的留存课程，取最新的时间的留存课程',
        ]);
    }

    /**
     * 获取报名时间
     */
    public static function getTradeTime(){
        if (AssistantDesk_Data_DataSource::isTogetherApi()) {
            return self::getTradeTimeNew();
        }
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $studentFudao      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsData");

        self::$student['regTime']         = $studentFudao[self::$studentUid]['regTime'] ? date('Y-m-d H:i:s', $studentFudao[self::$studentUid]['regTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'regTime', [
            'title'  => '【获取报名时间】',
            'source' => 'mysql：tblAssistantCourseStudent.reg_time',
        ]);
    }

    public static function getTradeTimeNew(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $acsData                    = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLeadsStudentList", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
        ]);
        $tradeTime                    = $acsData[self::$leadsId]['tradeTime'] ?? 0;

        self::$leads['regTime']         = $tradeTime ? date('Y-m-d H:i:s', $tradeTime) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'regTime', [
            'title'  => '【获取报名时间】',
            'source' => '接口/allocate/api/getnormalleadsbycourseassistant,字段：tradeTime',
        ]);
    }

    /**
     * 学生已报名专题课科目
     */
    public static function getOtherCourseEnrollSubject(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $sopRecruitData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getSopRecruitData");

        self::$student['otherCourseEnrollSubject'] = $sopRecruitData[self::$studentUid]['subjectIds'] ?: [];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'otherCourseEnrollSubject', [
            'title' => '【学生已报名专题课科目】',
            '数据源'   => 'es.idl_assistant_course_student_recruit_action.other_course_detail报名详情中得出',
        ]);
    }

//    /**
//     * 建议扩科科目
//     */
//    public static function getRecommendExtendSubject(){
//        $continueData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getContinueData");
//
//        self::$student['recommendExtendSubject'] = [];
//        if($continueData[self::$studentUid]['transferAvailableSubject'] && is_array($continueData[self::$studentUid]['transferAvailableSubject'])) {
//            self::$student['recommendExtendSubject'] = $continueData[self::$studentUid]['transferAvailableSubject'];
//            self::$student['recommendExtendSubjectCount'] = count($continueData[self::$studentUid]['transferAvailableSubject']);
//        }
//    }


    /**
     * 已扩科目  只有续报且扩科状态为1时展示数据，否则展示'-'   ,  hasTransferSubject=>[1,2,3]
     */
    public static function getHasExpandedSubject(){
        return [];  // 下线
    }

    /**
     * 单报订单数 只有续报且扩科状态为1时展示数据，否则展示'-'
     */
    public static function getTransferSubTradeIdCount(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "continueStatus",
            "isTransfer",
            "transferSubtradeIdCount",
        ])) {
            return [];
        }
        $esContinueData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['transferSubTradeIdCount'] = $esContinueData[self::$studentUid]['continueStatus'] == 2 && intval($esContinueData[self::$studentUid]['isTransfer']) ? $esContinueData[self::$studentUid]['transferSubtradeIdCount'] : '-';
    }

    /**
     * 联报订单数 只有续报且扩科状态为1时展示数据，否则展示'-'
     */
    public static function getTransferBoundSubTradeIdCount(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "continueStatus",
            "isTransfer",
            "transferBoundSubtradeIdCount",
        ])) {
            return [];
        }
        $esContinueData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['transferBoundSubTradeIdCount'] = $esContinueData[self::$studentUid]['continueStatus'] == 2 && intval($esContinueData[self::$studentUid]['isTransfer']) ? $esContinueData[self::$studentUid]['transferBoundSubtradeIdCount'] : '-';
    }
//
//    /**
//     * 专题课(加油课)是否目标转化学员
//     */
//    public static function getIsTransferTarget(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        self::$student['isTransferTarget'] = intval($esCuData[self::$studentUid]['isTransferTarget']);
//    }

//    /**
//     * 专题课(加油课)阶段测提交次数
//     */
//    public static function getStageTestSubmitCount(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        self::$student['stageTestSubmitCount'] = intval($esCuData[self::$studentUid]['stageTestSubmitCount']);
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'stageTestSubmitCount', [
//            'title' => '【专题课(加油课)阶段测提交次数】',
//            '数据源'   => 'es.idl_course_student_assistant.stage_test_submit_count',
//        ]);
//    }

//    /**
//     * 专题课(加油课) 可转化科目
//     */
//    public static function getTransferAvailableSubject(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        if(!is_array($esCuData[self::$studentUid]['transferAvailableSubject']) || empty($esCuData[self::$studentUid]['transferAvailableSubject'])){
//            self::$student['transferAvailableSubject'] = [];
//        }else{
//            $transferAvailableSubject = $esCuData[self::$studentUid]['transferAvailableSubject'];
//            sort($transferAvailableSubject);
//            foreach ($transferAvailableSubject as $subject){
//                if (AssistantDesk_Common_Keyconst::getSubject()[$subject]){
//                    self::$student['transferAvailableSubject'][] = AssistantDesk_Common_Keyconst::getSubject()[$subject];
//                }
//            }
//        }
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferAvailableSubject', [
//            'title' => '【专题课(加油课) 可转化科目】',
//            '数据源'   => 'es.idl_course_student_assistant.transfer_available_subject',
//        ]);
//    }

//    /**
//     * 专题课(加油课) 转化状态 1: 已转化 0: 未转化
//     */
//    public static function getTransferStatus(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        self::$student['transferStatus'] = intval($esCuData[self::$studentUid]['isTrans']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferStatus', [
//            'title' => '【专题课(加油课) 转化状态】',
//            '数据源'   => 'es.idl_course_student_assistant.is_trans',
//        ]);
//    }

//    /**
//     * 专题课(加油课) 转化订单数
//     */
//    public static function getTransferSubtradeIdNum(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        self::$student['transferSubtradeIdNum'] = intval($esCuData[self::$studentUid]['transferSubtradeIdNum']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferSubtradeIdNum', [
//            'title' => '【专题课(加油课) 转化订单数】',
//            '数据源'   => 'es.idl_course_student_assistant.transfer_subtrade_id_num',
//        ]);
//    }

//    /**
//     * 获取转化时间
//     */
//    public static function getContinueTimeOther(){
//        $esCuData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuDataOther");
//
//        self::$student['transferSubtradeIdNum'] = intval($esCuData[self::$studentUid]['transfer_subtrade_id_num']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'transferSubtradeIdNum', [
//            'title' => '【获取转化时间】',
//            '数据源'   => 'es.idl_course_student_assistant.transfer_subtrade_id_num',
//        ]);
//    }

    /**
     * 特惠课新加累计到课5min章节数
     */
    public static function getAttendLessonNums5(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["attendCount5m"])) {
            return [];
        }
        $esCuData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");;

        self::$student['attendLessonNums5'] = intval($esCuData[self::$studentUid]['attendCount5m']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendLessonNums5', [
            'title' => '【特惠课新加累计到课5min章节数】',
            '数据源'   => 'es：idl_course_student_assistant.attend_count_5m',
            '解释'=>'attend_count_5m含义 到课5分钟课中时长章节数',
        ]);
    }

    /**
     * 特惠课新加累计到课1/4章节数
     */
    public static function getAttendLessonNumsQuarter(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["attendCountQuarter"])) {
            return [];
        }
        $esCuData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['attendLessonNumsQuarter'] = intval($esCuData[self::$studentUid]['attendCountQuarter']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendLessonNums5', [
            'title' => '【特惠课新加累计到课1/4章节数】',
            '数据源'   => 'es：idl_course_student_assistant.attend_count_quarter',
            '解释'=>'attend_count_quarter含义 到课四分之一课中时长章节数',
        ]);
    }

    /**
     * 转介绍是否生成海报
     */
    public static function getFenXiaoIsPoster(){
        if (\AssistantDesk_Data_DataSource::beforeAddAuConfigIdFields(['studentUid', 'isPoster'])) {
            return [];
        }

        $fenXiaoData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFenXiaoData");

        self::$student['fenXiaoIsPoster'] = $fenXiaoData[self::$studentUid]['isPoster'] ?: 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'fenXiaoIsPoster', [
            'title' => '【转介绍是否生成海报】',
            '数据源'   => 'es：adl_referral_activity_aas.is_poster',
        ]);
    }

    /**
     * 转介绍 新用户PV
     */
    public static function getFenXiaoNewUserPV(){
        if (\AssistantDesk_Data_DataSource::beforeAddAuConfigIdFields(['studentUid', 'newUserPV'])) {
            return [];
        }
        $fenXiaoData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFenXiaoData");

        self::$student['fenXiaoNewUserPV'] = $fenXiaoData[self::$studentUid]['newUserPV'] ?: 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'fenXiaoNewUserPV', [
            'title' => '【转介绍 新用户PV】',
            '数据源'   => 'es：adl_referral_activity_aas.new_user_pv',
        ]);
    }

    /**
     * 转介绍 新用户UV
     */
    public static function getFenXiaoNewUserUV(){
        if (\AssistantDesk_Data_DataSource::beforeAddAuConfigIdFields(['studentUid', 'newUserUV',])) {
            return [];
        }
        $fenXiaoData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFenXiaoData");

        self::$student['fenXiaoNewUserUV'] = $fenXiaoData[self::$studentUid]['newUserUV'] ?: 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'fenXiaoNewUserUV', [
            'title' => '【转介绍 新用户UV】',
            '数据源'   => 'es：adl_referral_activity_aas.new_user_uv',
        ]);
    }

    /**
     * 转介绍 老师和学生绑定状态
     */
    public static function getFenXiaoBindStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddAuConfigIdFields(['studentUid', 'bindStatus'])) {
            return [];
        }
        $fenXiaoData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFenXiaoData");

        self::$student['fenXiaoBindStatus'] = $fenXiaoData[self::$studentUid]['bindStatus'] ?: 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'fenXiaoBindStatus', [
            'title' => '【转介绍 老师和学生绑定状态】',
            '数据源'   => 'es.adl_referral_activity_aas.bind_status',
        ]);
    }

    /**
     * 单个学生覆盖学生信息
     */
    public static function getFenXiaoCoverSeason(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fenXiaoCoverSeasonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "fenXiaoCoverSeason");

        self::$student['fenXiaoCoverSeason'] = $fenXiaoCoverSeasonData[self::$studentUid] ?: '';
    }

    /**
     * 获取备注信息
     */
    public static function getRemark() {
        if (AssistantDesk_Data_DataSource::isTogetherApi()) {
            return self::getRemarkNew();
        }

        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentFudao = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsData");

        $extData     = $studentFudao[self::$studentUid]['extData'];
        $contactFlag = $extData['contact_flag'] ? $extData['contact_flag'] : Service_Data_AssistantCourseStudent::CONTACT_NORMAL;

        self::$student['contactFlag']     = intval($contactFlag);
        self::$student['contactFlagTime'] = $extData['scRemarkTime'] ? date('Y-m-d H:i', $extData['scRemarkTime']) : '';
        self::$student['scRemark']        = $extData['scRemark'] ?: '';

        if (AssistantDesk_Data_CommonParams::$lessonId) {
            self::handleFsCourseStudentData();
            self::$student['dxCorrectStatusStr'] = self::$student['dxCorrectStatus'] == 1 ? '已开启' : '未开启';
        }
        self::getCourseFocus();
        self::$student['courseFocusStr'] = self::$student['courseFocus'] == 1 ? '已关注' : '未关注';
    }

    /**
     * 通用化备注
     * @return array
     * @throws Common_Exception
     * @throws ReflectionException
     */
    public static function getRemarkNew() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentFudao = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsV1Data", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$studentUids,
        ]);

        $extData     = $studentFudao[self::$studentUid]['extData'];
        $contactFlag = $extData['contact_flag'] ? $extData['contact_flag'] : Service_Data_AssistantCourseStudent::CONTACT_NORMAL;

        self::$student['contactFlag']     = intval($contactFlag);
        self::$student['contactFlagTime'] = $extData['scRemarkTime'] ? date('Y-m-d H:i', $extData['scRemarkTime']) : '';
        self::$student['scRemark']        = $extData['scRemark'] ?: '';

        if (AssistantDesk_Data_CommonParams::$lessonId) {
            self::handleFsCourseStudentData();
            self::$student['dxCorrectStatusStr'] = self::$student['dxCorrectStatus'] == 1 ? '已开启' : '未开启';
        }
        self::getCourseFocus();
        self::$student['courseFocusStr'] = self::$student['courseFocus'] == 1 ? '已关注' : '未关注';
    }

    /**
     * 获取预习完成状态
     */
    public static function getPreviewFinishStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["isPreviewFinish"])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['previewLikeStatus']   = $studentLessonData[self::$studentUid]['isPreviewFinish'] ? 1 : 0;  //根据完成状态判断是否可点赞
        self::$student['previewFinishStatus'] = $studentLessonData[self::$studentUid]['isPreviewFinish'] ? intval($studentLessonData[self::$studentUid]['isPreviewFinish']) : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'previewLikeStatus', [
            'title' => '【获取预习完成状态(previewLikeStatus,previewFinishStatus)】',
            '数据源'   => 'es.lu.bind_status.exam_answer.exam5.is_finish, es.lu.bind_status.exam_answer.exam13.is_finish',
        ]);
    }

    public static function getSuyangGradeSubject() {
        if (\AssistantDesk_Data_DataSource::beforeAddUYearNextSeasonFields(['grade_subject_enum'])) {
            return [];
        }
        $data                      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getNextSeasonStudentSeasonList", [
            AssistantDesk_Data_CommonParams::$courseId,
        ]);
        $gradeSubjectEnum          = $data[self::$studentUid]['grade_subject_enum'];
        $gradeSubjectEnumFormatArr = [];
        if ($gradeSubjectEnum) {
            $gradeSubjectEnumArr       = explode(',', $gradeSubjectEnum);
            foreach ($gradeSubjectEnumArr as $singleInfo) {
                list($gradeId, $subjectId) = explode('-', $singleInfo);
                $gradeStr                    = Zb_Const_GradeSubject::$GRADE[$gradeId] ?? '未知';
                $subjectStr                  = AssistantDesk_Common_Keyconst::getSubject()[$subjectId] ?? '未知';
                $gradeSubjectEnumFormatArr[] = "{$gradeStr}-{$subjectStr}";
            }
        }
        self::$student['suyangGradeSubject'] = implode(',', $gradeSubjectEnumFormatArr);  //
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'suyangGradeSubject', [
            'title' => '素养课领取 年级科目',
            '数据源'   => 'es.dataware_trade_student_season_agg.grade_subject_enum',
        ]);
    }

    public static function getWxSendHistoryIsSend(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $sendHistoryUids      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getWxSendHistoryMap", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
            AssistantDesk_Data_CommonParams::$sendType,
            AssistantDesk_Data_CommonParams::$lessonId,
            false, // 是否是章节维度
        ]);
        $hasSend = isset($sendHistoryUids[self::$studentUid]) ? 1 : 0;

        // 将发送记录拼接到全局学生列表数据
        self::$student['wxSendHistoryIsSend']   = $hasSend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wxSendHistoryIsSend', [
            'title' => '【场景化群发是否发送】',
            '数据源'   => 'mysql.tblWxMessageSendRecord ，作文报告反馈：查询批改详情',
        ]);
    }

    public static function getWxSendHistoryIsSendField(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fieldRule     = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $sendType = intval($fieldRule['serviceConfig']['sendType'] ?? 0);

        $sendHistoryUids      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getWxSendHistoryMapInYear", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
            $sendType,
            AssistantDesk_Data_CommonParams::$lessonId,
            false, // 是否是章节维度
        ]);
        $hasSend = isset($sendHistoryUids[self::$studentUid]) ? 1 : 0;

        // 将发送记录拼接到全局学生列表数据
        self::$student['wxSendHistoryIsSend_' . $sendType]   = $hasSend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wxSendHistoryIsSendField', [
            'title' => '【场景化群发是否发送】用于字段列',
            '数据源'   => 'mysql.tblWxMessageSendRecord ',
        ]);
    }

    public static function getWxSendHistoryLessonIsSend(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $sendHistoryUids      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getWxSendHistoryMap", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
            AssistantDesk_Data_CommonParams::$sendType,
            AssistantDesk_Data_CommonParams::$lessonId,
            true, // 是否是章节维度
        ]);
        $hasSend = isset($sendHistoryUids[self::$studentUid]) ? 1 : 0;
        if (AssistantDesk_Data_CommonParams::$sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT) {
            $stuFeedbackDetail = Common_Singleton::getInstanceData(AssistantDesk_KpMessageService_SpecialSendTypeHandle::class, "stuFeedbackDetail", [
                AssistantDesk_Data_CommonParams::$courseId,
                AssistantDesk_Data_CommonParams::$lessonId,
                AssistantDesk_Data_CommonParams::$sendType,
                AssistantDesk_Data_CommonParams::$assistantUid,
            ]);
            // 如果批改系统反馈过作文报告图片也算反馈过
            isset($stuFeedbackDetail[self::$studentUid]) && $hasSend = 1;
        }
        if (AssistantDesk_Data_CommonParams::$sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER) {
            // 小鹿工作台也可以单独发送
            $studentUids = Api_DeerCorrect::getFeedUids(AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$assistantUid, 2000);
            $hasSend = (in_array(self::$studentUid, $studentUids) || $hasSend) ? 1 : 0;
        }

        // 将发送记录拼接到全局学生列表数据
        self::$student['wxSendHistoryLessonIsSend']   = $hasSend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'wxSendHistoryLessonIsSend', [
            'title' => '【是否已发批改反馈】章节维度',
            '数据源'   => 'mysql.tblWxMessageSendRecord ，作文报告反馈：查询批改详情',
        ]);
    }

    //阶段测是否生成
    public static function getStageTestReportIsComplete(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        //var_dump(['oooooooo0000000000000000000000ooo', 'wwwwwwwwwwwwwwwwwwwwwww']);

        $courseReports = Common_Singleton::getInstanceData("AssistantDesk_FieldToData", "getStageTestList", [AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$lessonId]);
        if (empty($courseReports)) {
            Bd_Log::notice("getStageTestReportReturn_empty:" . json_encode([AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$courseId]));
        }
        self::$student['stageTestReportIsComplete'] = isset($courseReports[self::$studentUid]) ? 1 : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'stageTestReportIsComplete', [
            'title' => '【阶段测报告是否生成】',
            '数据源'   => '接口：/jxreport/api/getcoursereporturllist 获取学生阶段测数据',
        ]);
    }

//

    //阶段测报告分数
    public static function getStageTestScoreLimit(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $courseReports = Common_Singleton::getInstanceData("AssistantDesk_FieldToData", "getStageTestReportScore", [AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$lessonId]);
        if (empty($courseReports)) {
            Bd_Log::notice("getExamRet_empty:" . json_encode([AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$lessonId]));
        }
        self::$student['stageTestScoreLimit'] = isset($courseReports[self::$studentUid]) ? $courseReports[self::$studentUid] : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'getStageTestReportScore', [
            'title' => '【阶段测报告分数】',
            '数据源'   => '接口：/examcore/v1/getanswer 获取学生阶段测报告分数',
        ]);
    }


    public static function getCorrectTimes() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $studentCorrectTimes      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCorrectTimes", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$lessonId,
            AssistantDesk_Data_CommonParams::$assistantUid,
        ]);

        self::$student['correctTimes'] = intval($studentCorrectTimes[self::$studentUid] ?? 0);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'correctTimes', [
            'title' => '批改次数【(correctTimes)】',
            '数据源'   => 'api:pcassistant:/pcassistant/correction/api/getcorrecttimes',
        ]);


    }


    /**
     * 获取预习完成时间
     */
    public static function getPreviewFinishTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["previewFinishTime"])) {
            return [];
        }
        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['previewFinishTime'] = $studentLessonData[self::$studentUid]['previewFinishTime'] ? date('Y-m-d H:i:s', $studentLessonData[self::$studentUid]['previewFinishTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'previewFinishTime', [
            'title' => '【获取预习完成时间】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.exam_answer.exam5.finish_time和exam_answer.exam13.finish_time',
            '解释'=>'exam5.finish_time与exam13.finish_time加和',
        ]);
    }

    /**
     * 小试牛刀
     */
    public static function getTryKnifeStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["tryKnifeSubmitStatus", "tryKnifeScore"])) {
            return [];
        }
        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        // 已完成返回分数 未完成返回-1 未提交返回-2 无数据返回 -3
        $tryKnifeSubmitStatus = !is_null($studentLessonData[self::$studentUid]['tryKnifeSubmitStatus']) ? $studentLessonData[self::$studentUid]['tryKnifeSubmitStatus'] : 3;
        switch ($tryKnifeSubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['tryKnife']     = -2;
                self::$student['tryKnifeCode'] = intval($tryKnifeSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['tryKnife']     = -1;
                self::$student['tryKnifeCode'] = intval($tryKnifeSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['tryKnife']     = intval($studentLessonData[self::$studentUid]['tryKnifeScore']);
                self::$student['tryKnifeCode'] = intval($tryKnifeSubmitStatus);
                break;
            default:
                self::$student['tryKnife']     = -3;
                self::$student['tryKnifeCode'] = 3;
                break;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tryKnifeCode', [
            'title' => '【小试牛刀】',
            '数据源'   => 'es:lu:exam_answer.exam211.submit_status',
        ]);

    }

    /**
     * 浣熊lesson状态
     */
    public static function getHxLessonStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["lessonOneSubmitStatus", "lessonTwoSubmitStatus","lessonTwoScore","lessonThreeSubmitStatus","lessonThreeScore"])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        // 已完成返回分数 未完成返回-1 未提交返回-2 无数据返回 -3
        $lesson1SubmitStatus = !is_null($studentLessonData[self::$studentUid]['lessonOneSubmitStatus']) ? $studentLessonData[self::$studentUid]['lessonOneSubmitStatus'] : 3;
        switch ($lesson1SubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['lesson1']     = -2;
                self::$student['lesson1Code'] = intval($lesson1SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['lesson1']     = -1;
                self::$student['lesson1Code'] = intval($lesson1SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                //self::$student['lesson1']     = intval($studentLessonData[self::$studentUid]['lessonOneScore']);
                self::$student['lesson1']     = $lesson1SubmitStatus == 2 ? '已完成' : '未完成';
                self::$student['lesson1Code'] = intval($lesson1SubmitStatus);
                break;
            default:
                self::$student['lesson1']     = -3;
                self::$student['lesson1Code'] = 3;
                break;
        }

        $lesson2SubmitStatus = !is_null($studentLessonData[self::$studentUid]['lessonTwoSubmitStatus']) ? $studentLessonData[self::$studentUid]['lessonTwoSubmitStatus'] : 3;
        switch ($lesson2SubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['lesson2']     = -2;
                self::$student['lesson2Code'] = intval($lesson2SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['lesson2']     = -1;
                self::$student['lesson2Code'] = intval($lesson2SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['lesson2']     = intval($studentLessonData[self::$studentUid]['lessonTwoScore']);
                self::$student['lesson2Code'] = intval($lesson2SubmitStatus);
                break;
            default:
                self::$student['lesson2']     = -3;
                self::$student['lesson2Code'] = 3;
                break;
        }

        $lesson3SubmitStatus = !is_null($studentLessonData[self::$studentUid]['lessonThreeSubmitStatus']) ? $studentLessonData[self::$studentUid]['lessonThreeSubmitStatus'] : 3;
        switch ($lesson3SubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['lesson3']     = -2;
                self::$student['lesson3Code'] = intval($lesson3SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['lesson3']     = -1;
                self::$student['lesson3Code'] = intval($lesson3SubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['lesson3']     = intval($studentLessonData[self::$studentUid]['lessonThreeScore']);
                self::$student['lesson3Code'] = intval($lesson3SubmitStatus);
                break;
            default:
                self::$student['lesson3']     = -3;
                self::$student['lesson3Code'] = 3;
                break;
        }
    }

    /**
     * 综合演练
     */
    public static function getSynthesisManoeuvre(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["synthesisManoeuvreSubmitStatus", "synthesisManoeuvreScore"])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        // 已完成返回分数 未完成返回-1 未提交返回-2 无数据返回 -3
        $synthesisManoeuvreSubmitStatus = !is_null($studentLessonData[self::$studentUid]['synthesisManoeuvreSubmitStatus']) ? $studentLessonData[self::$studentUid]['synthesisManoeuvreSubmitStatus'] : 3;
        switch ($synthesisManoeuvreSubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['synthesisManoeuvre']     = -2;
                self::$student['synthesisManoeuvreCode'] = intval($synthesisManoeuvreSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['synthesisManoeuvre']     = -1;
                self::$student['synthesisManoeuvreCode'] = intval($synthesisManoeuvreSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['synthesisManoeuvre']     = intval($studentLessonData[self::$studentUid]['synthesisManoeuvreScore']);
                self::$student['synthesisManoeuvreCode'] = intval($synthesisManoeuvreSubmitStatus);
                break;
            default:
                self::$student['synthesisManoeuvre']     = -3;
                self::$student['synthesisManoeuvreCode'] = 3;
                break;
        }
    }

    /**
     * 能力挑战
     */
    public static function getPowerChallenge(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["powerChallengeSubmitStatus", "powerChallengeScore"])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        // 已完成返回分数 未完成返回-1 未提交返回-2 无数据返回 -3
        $powerChallengeSubmitStatus = !is_null($studentLessonData[self::$studentUid]['powerChallengeSubmitStatus']) ? $studentLessonData[self::$studentUid]['powerChallengeSubmitStatus'] : 3;
        switch ($powerChallengeSubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['powerChallenge']     = -2;
                self::$student['powerChallengeCode'] = intval($powerChallengeSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['powerChallenge']     = -1;
                self::$student['powerChallengeCode'] = intval($powerChallengeSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['powerChallenge']     = intval($studentLessonData[self::$studentUid]['powerChallengeScore']);
                self::$student['powerChallengeCode'] = intval($powerChallengeSubmitStatus);
                break;
            default:
                self::$student['powerChallenge']     = -3;
                self::$student['powerChallengeCode'] = 3;
                break;
        }
    }

    /**
     * 综合秀场
     */
    public static function getSynthesisShow(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["synthesisShowSubmitStatus", "synthesisShowScore"])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        // 已完成返回分数 未完成返回-1 未提交返回-2 无数据返回 -3
        $synthesisShowSubmitStatus = !is_null($studentLessonData[self::$studentUid]['synthesisShowSubmitStatus']) ? $studentLessonData[self::$studentUid]['synthesisShowSubmitStatus'] : 3;
        switch ($synthesisShowSubmitStatus){
            case AssistantDesk_DataConfig::HX_NOT_SUBMIT:
                self::$student['synthesisShow']     = -2;
                self::$student['synthesisShowCode'] = intval($synthesisShowSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_UNFINISHED:
                self::$student['synthesisShow']     = -1;
                self::$student['synthesisShowCode'] = intval($synthesisShowSubmitStatus);
                break;
            case AssistantDesk_DataConfig::HX_FINISHED:
                self::$student['synthesisShow']     = intval($studentLessonData[self::$studentUid]['synthesisShowScore']);
                self::$student['synthesisShowCode'] = intval($synthesisShowSubmitStatus);
                break;
            default:
                self::$student['synthesisShow']     = -3;
                self::$student['synthesisShowCode'] = 3;
                break;
        }
    }

    /**
     * 获取预到课状态
     */
    public static function getPreAttendStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $lessonStudents      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getPreAttendStatus");
        $lessonInfo          = Common_Singleton::getInstanceData("Api_Dal", "getLessonBaseByLessonIds", [AssistantDesk_Data_CommonParams::$lessonId]);
        $isCourseInTempDelayEditPreAttendConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "isCourseInTempDelayEditPreAttendConf", [AssistantDesk_Data_CommonParams::$courseId]);

        //章节结束
        self::$student['isLessonFinish'] = $lessonInfo['stopTime'] && $lessonInfo['stopTime'] <= time() ? ($isCourseInTempDelayEditPreAttendConf ? 0 : 1) : 0;
        // 预到课状态
        self::$student['preAttend']   = intval($lessonStudents[self::$studentUid]['preAttend']);

        self::$student['isSyncRemind'] = $lessonStudents[self::$studentUid]['extData']['isSyncRemind'] ? 1 : 0;
        self::$student['remindTime'] = $lessonStudents[self::$studentUid]['extData']['remindTime'];
        // 老师自定义的原因
        self::$student['leaveSeason'] = $lessonStudents[self::$studentUid]['extData']['leaveSeason'] ? $lessonStudents[self::$studentUid]['extData']['leaveSeason'] : "";
        self::$student['preAttendText'] = isset(Service_Data_LessonStudent::$arrPreAttend[self::$student['preAttend']]) ? Service_Data_LessonStudent::$arrPreAttend[self::$student['preAttend']] : '';
        //如果当前预到课状态为请假，附上请假理由
        if (self::$student['preAttend'] == Service_Data_LessonStudent::PRE_ATTEND_LEAVE) {
            // 一级请假路理由
            self::$student['firstLeaveReason'] = $lessonStudents[self::$studentUid]['extData']['firstLeaveReason'] ?? "";
            self::$student['leaveReason'] = self::$student['leaveSeason'] ?? "";
            // 观看回放时间
            $contentTime = $lessonStudents[self::$studentUid]['extData']['contentTime'] ?? "";
            self::$student['contentTime'] = $contentTime;
            $contentTime = intval($contentTime) ? date('Y-m-d H:i:s', intval($contentTime)) : "";
            self::$student['contentTimeFormat'] = $contentTime;
            self::$student['preAttendText'] .= ':';
            if (self::$student['firstLeaveReason']) {
                self::$student['preAttendText'] .= "#" .self::$student['firstLeaveReason'] . "#,";
            }
            if ($contentTime) {
                self::$student['preAttendText'] .= "#观看回放时间：{$contentTime}#";
            }
            self::$student['preAttendText'] .= self::$student['leaveSeason'];
        }
    }

    /**
     * 获取上课表现 （到课 + 互动题）时间线
     */
    public static function getOnClassBehavior(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","interactionAnswerDetail","attendOverview"])) {
            return [];
        }

        $studentStatusTimeLine = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getOnClassBehavior");

        $lessonInfo          = Common_Singleton::getInstanceData("Api_Dal", "getLessonBaseByLessonIds", [AssistantDesk_Data_CommonParams::$lessonId]);

        self::$student['onClassBehavior'] = [];

        if (isset($studentStatusTimeLine[self::$studentUid])) {
            self::$student['statusTimeLine'] = $studentStatusTimeLine[self::$studentUid];
        } else {
            $timeLineInfo['timeLineStart'] = $lessonInfo['startTime'] - 1800;
            $timeLineInfo['timeLineEnd'] = $lessonInfo['stopTime'] + 1800;
            $timeLineInfo['lessonStart'] = $lessonInfo['startTime'];
            $timeLineInfo['lessonEnd'] = $lessonInfo['stopTime'];
            $timeLineInfo['attendTimeLine'] = [];
            $timeLineInfo['interactTimeLine'] = [];
            self::$student['statusTimeLine'] = $timeLineInfo;
        }
    }

    /**
     * 回放上课标签
     * @throws ReflectionException
     */
    public static function getPlaybackOnClassBehavior() {
        self::getPlaybackAttendStatusLabel();
        self::getPlaybackInteractionLabel();
    }

    /**
     * 回放到课标签
     * @throws ReflectionException
     */
    public static function getPlaybackAttendStatusLabel() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid",
            "inclass_teacher_room_total_playback_time_v1",
            "inclass_teacher_room_attend_duration",
            "is_inclass_teacher_room_view_finish_total_playback_three_five_v1",

        ])) {
            return [];
        }
        $commonStudentLessonData              = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        self::$student['playbackAttendLabel'] = AssistantDesk_Data_PlayBackAttendLabel::getPlayBackAttendLabel(
            $commonStudentLessonData[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'] ?? 0,
            $commonStudentLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] ?? 0,
            $commonStudentLessonData[self::$studentUid]['is_inclass_teacher_room_view_finish_total_playback_three_five_v1'] ?? 0
        );

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackAttendLabel', [
            'title' => '【回放到课标签】',
            '数据源'   => 'es:commonlu:inclass_teacher_room_total_playback_time_v1,inclass_teacher_room_attend_duration,is_inclass_teacher_room_view_finish_total_playback_three_five_v1',
            '解释'    => 'https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=435816655',
        ]);
    }

    /**
     * 回放互动题标签
     * @throws ReflectionException
     */
    public static function getPlaybackInteractionLabel() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","playbackInteractionLabel"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['playbackInteractionLabel'] = $studentLessonData[self::$studentUid]['playbackInteractionLabel'] ?? [];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackInteractionLabel', [
            'title' => '【回放互动题标签】',
            '数据源'   => 'es:idl_assistant_lesson_student_action',
            '解释'=>'代码中无计算和取值逻辑',
        ]);
    }

    /**
     * 获取到课状态标签 和 到课答题时间线信息
     */
    public static function getAttendStatusLabel(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","attendLabel"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $attendStatusLabelData = AssistantDesk_Inclass::attendStatusLabel($studentLessonData);
        self::$student['attendStatusLabel'] = is_array($attendStatusLabelData[self::$studentUid]) ? $attendStatusLabelData[self::$studentUid] : [];
        //数据类型装换
        if (!empty(self::$student['attendStatusLabel'])) {
            self::$student['attendStatusLabel'] = array_map(function ($v) {return intval($v);},self::$student['attendStatusLabel']);
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendStatusLabel', [
            'title' => '【获取到课状态标签 和 到课答题时间线信息】',
            '数据源'   => 'es:lu.student_attend_label',
        ]);
    }

    /**
     * 获取做题状态标签信息
     */
    public static function getInteractStatusLabel(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","attendLabel","interactionLabel"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $interactStatusLabelData = AssistantDesk_Inclass::interactStatusLabel($studentLessonData);
        self::$student['interactStatusLabel'] = is_array($interactStatusLabelData[self::$studentUid]) ? $interactStatusLabelData[self::$studentUid] : [];
        //数据类型装换
        if (!empty(self::$student['interactStatusLabel'])) {
            self::$student['interactStatusLabel'] = array_map(function ($v) {return intval($v);},self::$student['interactStatusLabel']);
        }
    }

    /**
     * 获取堂堂测完成状态
     */
    public static function getInClassTestFinishStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isTangTangExamSubmit"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['inClassTestFinishStatus'] = $studentLessonData[self::$studentUid]['isTangTangExamSubmit'] ? AssistantDesk_TaskMap::EXAM_RESULT_FINISH : AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'inClassTestFinishStatus', [
            'title' => '【获取堂堂测完成状态】',
            '数据源'   => 'es:lu.exam_answer.exam10.is_submit',
        ]);
    }

    /**
     * 获取巩固练习完成状态
     */
    public static function getHomeworkFinishStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $dasLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasLessonData");
        $studentHomeworkInfo = $dasLessonData[self::$studentUid][AssistantDesk_Data_CommonParams::$lessonId];
        //根据完成状态判断是否可点赞 待批改和已批改都是完成
        self::$student['homeworkLikeStatus']   = in_array($studentHomeworkInfo['homeworkStatus'], [
            AssistantDesk_Config::HOMEWORK_STATUS_SUBMIT,
            AssistantDesk_Config::HOMEWORK_STATUS_COMMENT
        ]) ? 1: 0;
        self::$student['homeworkFinishStatus'] = $studentHomeworkInfo['homeworkStatus'] ? intval($studentHomeworkInfo['homeworkStatus']) : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkFinishStatus', [
            'title' => '【巩固练习完成状态】',
            '数据源'   => 'das.homeworkStatus',
        ]);
    }

    /**
     * 巩固练习提交状态
     * @return array
     * @throws ReflectionException
     */
    public static function getEsHomeworkSubmitStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(['ilabHomeworkMustIsSubmit', 'isHomeworkSubmit',])) {
            return [];
        }
        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        $homeworkSubmit = 0;
        if ($studentHomeworkInfo['isHomeworkSubmit']) {
            $homeworkSubmit = 1;
        }
        // ilab的课
        if ($studentHomeworkInfo['ilabHomeworkMustIsSubmit']) {
            $homeworkSubmit = 1;
        }
        self::$student['homeworkSubmitStatus'] = $homeworkSubmit;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkSubmitStatus', [
            'title' => '【巩固练习提交状态】',
            '数据源'   => 'es.lu.ilabHomeworkMustIsSubmit || es.lu.isHomeworkSubmit',
        ]);
    }

    /**
     * 获取巩固练习评级
     */
    public static function getHomeworkLevel(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","homeworkLevel"])) {
            return [];
        }

        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        self::$student['homeworkLevel'] = $studentHomeworkInfo['homeworkLevel'] > 0 ? Api_Das::$homeworkLevelMap[$studentHomeworkInfo['homeworkLevel']] : "暂无等级";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkLevel', [
            'title' => '【获取巩固练习评级】',
            '数据源'   => 'es:lu.exam_answer.exam7.correct_level',
        ]);
    }

    /**
     * 获取巩固练习相似题评级
     */
    public static function getSimilarHomeworkLevel(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam_answer.exam33.correct_level"])) {
            return [];
        }

        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        $level  = $studentHomeworkInfo['exam_answer.exam33.correct_level'] ?? 0;
        self::$student['similarHomeworkLevel'] = $level > 0
            ? (Api_Das::$homeworkLevelMap[$level] ?? '暂无等级')
            : "暂无等级";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkLevel', [
            'title' => '【获取相似题评级】',
            '数据源'   => 'es:lu.exam_answer.exam33.correct_level',
        ]);
    }

    /**
     * 获取巩固练习相似题评级
     */
    public static function getSimilarHomeworkStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam_answer.exam33.last_correct_status"])) {
            return [];
        }

        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        $status  = $studentHomeworkInfo['exam_answer.exam33.last_correct_status'] ?? 0;
        self::$student['similarHomeworkStatus'] = $status;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkStatus', [
            'title' => '【获取相似题状态】',
            '数据源'   => 'es:lu.exam_answer.exam33.last_correct_status',
        ]);
    }

    /**
     * 获取巩固练习相似题首次作答提交题目数
     */
    public static function getSimilarHomeworkFirstCorrectCnt(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam33_first_correct_cnt"])) {
            return [];
        }

        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        $status  = $studentHomeworkInfo['exam33_first_correct_cnt'] ?? 0;
        self::$student['similarHomeworkFirstCorrectCnt'] = $status;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkFirstCorrectCnt', [
            'title' => '【获取相似题首次作答提交题目数】',
            '数据源'   => 'es:lu.exam33_first_correct_cnt',
        ]);
    }

    /**
     * 获取巩固练习相似题首次作答正确题目数
     */
    public static function getSimilarHomeworkFirstRightCnt(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam33_first_right_cnt"])) {
            return [];
        }

        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $dasStudentData[self::$studentUid];
        $status  = $studentHomeworkInfo['exam33_first_right_cnt'] ?? 0;
        self::$student['similarHomeworkFirstRightCnt'] = $status;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkFirstRightCnt', [
            'title' => '【获取相似题首次作答正确题目数】',
            '数据源'   => 'es:lu.exam33_first_right_cnt',
        ]);
    }

    /**
     * 获取巩固练习完成时间
     */
    public static function getHomeworkFinishTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","homeworkFinishTime"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['homeworkFinishTime'] = $studentLessonData[self::$studentUid]['homeworkFinishTime'] ? date('Y-m-d H:i:s', $studentLessonData[self::$studentUid]['homeworkFinishTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkFinishTime', [
            'title' => '【获取巩固练习完成时间】',
            '数据源'   => 'es:lu.exam_answer.exam7.submit_time',
        ]);
    }

    /**
     * 获取巩固练习相似题完成时间
     */
    public static function getSimilarHomeworkFinishTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam_answer.exam33.submit_time"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        $submitTime = $studentLessonData[self::$studentUid]['exam_answer.exam33.submit_time'] ?? 0;
        self::$student['similarHomeworkFinishTime'] = $submitTime
            ? date('Y-m-d H:i:s', $submitTime)
            : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkFinishTime', [
            'title' => '【获取相似题完成时间】',
            '数据源'   => 'es:lu.exam_answer.exam33.submit_time',
        ]);
    }

    /**
     * 获取巩固练习相似题提交次数
     */
    public static function getSimilarHomeworkSubmitNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exam_answer.exam33.submit_num"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        $submitTime = $studentLessonData[self::$studentUid]['exam_answer.exam33.submit_num'] ?? 0;
        self::$student['similarHomeworkSubmitNum'] = $submitTime
            ?
            : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'similarHomeworkSubmitNum', [
            'title' => '【获取相似题提交次数】',
            '数据源'   => 'es:lu.exam_answer.exam33.submit_num',
        ]);
    }

    /**
     * 获取巩固练习最后提交时间（订正项目）
     */
    public static function getHomeworkLastSubmitTime(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","homeworkLastSubmitTime"])) {
            return [];
        }
        $dasStudentData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['homeworkLastSubmitTime'] = $dasStudentData[self::$studentUid]['homeworkLastSubmitTime'] ? date('Y-m-d H:i:s', $dasStudentData[self::$studentUid]['homeworkLastSubmitTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkLastSubmitTime', [
            'title' => '【获取巩固练习最后提交时间（订正项目）】',
            '数据源'   => 'es:lu.exam_answer.exam7.last_submit_time',
        ]);
    }

    /**
     * 巩固练习状态（含订正）
     */
    public static function getHomeworkCorrectStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $dasLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasLessonData");
        $lessonStudentHomeworkNeedAudit = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLessonStudentHomeworkNeedAudit");

        $hwBindExams = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "lessonBindExams",
            [[AssistantDesk_Data_CommonParams::$lessonId], Api_Exam::BIND_TYPE_HOMEWORK]
        );
        $lessonIsBindHw = !empty($hwBindExams[AssistantDesk_Data_CommonParams::$lessonId]);

        $isCorrectHomework = AssistantDesk_ServiceTools::isHomeworkAmendCourse(['courseId' => AssistantDesk_Data_CommonParams::$courseId]);

        $studentHomeworkInfo = $dasLessonData[self::$studentUid][AssistantDesk_Data_CommonParams::$lessonId];
        $homeworkCorrectStatus = $lessonIsBindHw
            ? ($studentHomeworkInfo['homeworkRecorrect']
                ? $studentHomeworkInfo['homeworkRecorrect']
                : AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_SUBMITTED)       //待提交
            : AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_UNASSIGNED;         //未布置
        self::$student['homeworkCorrectStatus'] = $homeworkCorrectStatus;

        //对于订正巩固练习，如果学生巩固练习状态为 待批改 且批改系统中状态为 待审核（兼职批改后需辅导老师审核），则设置状态为 待审核
        if ($isCorrectHomework && ($homeworkCorrectStatus == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_CORRECTED || $homeworkCorrectStatus == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_RECORRECTED)) {
            if (isset($lessonStudentHomeworkNeedAudit[self::$studentUid])) {
                self::$student['homeworkCorrectStatus'] = AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_AUDIT;
            }
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkCorrectStatus', [
            'title' => '【巩固练习状态（含订正）】',
            '数据源'   => 'das:获取das章节汇聚信息中字段homeworkRecorrect；/pcassistant/correction/api/getcorrectionstudentlistbystatus 获取课程章节辅导老师维度巩固练习待审核学生列表（http://yapi.zuoyebang.cc/project/369/interface/api/99622 lina02）',
            '解释'=>'对于订正巩固练习，如果学生巩固练习状态为 待批改 且批改系统中状态为 待审核（兼职批改后需辅导老师审核），则设置状态为 待审核；状态枚举值：0"未布置"、1"待提交"、2"待批改"、3"待重提"、4"待重批"、5"已订正"、6"待审核"',
        ]);
    }


    // 第 X 章节巩固练习状态
    public static function getHomeworkCorrectStatusByLesson(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        if (empty($fieldRule['serviceConfig'])) {
            return;
        }

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];

        if($fieldRule['serviceConfig']['lessonType'] > 0) {
            foreach($lessonList as $key => $lessonInfo) {
                if ($lessonInfo['lessonType'] != $fieldRule['serviceConfig']['lessonType']) {
                    unset($lessonList[$key]);
                }
            }
        }

        if($fieldRule['serviceConfig']['playType'] > 0) {
            foreach($lessonList as $key => $lessonInfo) {
                if ($lessonInfo['playType'] != $fieldRule['serviceConfig']['playType']) {
                    unset($lessonList[$key]);
                }
            }
        }

        $number = $fieldRule['serviceConfig']['number']-1;
        if($number < 0) {
            $number = 0;
        } else if ($number >= count($lessonList)) {
            $number = count($lessonList)-1;
        }

        //按上课时间升序
        array_multisort(array_column($lessonList, 'startTime'), SORT_ASC, $lessonList);
        if(!isset($lessonList[$number])) {
            return;
        }

        $selectLessonId = $lessonList[$number]['lessonId'];

        Bd_Log::notice("getHomeworkCorrectStatusByLesson, selectLessonId:{$selectLessonId}");
        AssistantDesk_Data_CommonParams::setLessonId($selectLessonId);

        $dasLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasLessonData");
        $lessonStudentHomeworkNeedAudit = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLessonStudentHomeworkNeedAudit");

        $hwBindExams = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "lessonBindExams",
            [[AssistantDesk_Data_CommonParams::$lessonId], Api_Exam::BIND_TYPE_HOMEWORK]
        );
        $lessonIsBindHw = !empty($hwBindExams[AssistantDesk_Data_CommonParams::$lessonId]);

        $isCorrectHomework = AssistantDesk_ServiceTools::isHomeworkAmendCourse(['courseId' => AssistantDesk_Data_CommonParams::$courseId]);

        $studentHomeworkInfo = $dasLessonData[self::$studentUid][AssistantDesk_Data_CommonParams::$lessonId];
        $homeworkCorrectStatus = $lessonIsBindHw
            ? ($studentHomeworkInfo['homeworkRecorrect']
                ? $studentHomeworkInfo['homeworkRecorrect']
                : AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_SUBMITTED)       //待提交
            : AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_UNASSIGNED;         //未布置
        self::$student[$fieldRule['key']] = $homeworkCorrectStatus;

        //对于订正巩固练习，如果学生巩固练习状态为 待批改 且批改系统中状态为 待审核（兼职批改后需辅导老师审核），则设置状态为 待审核
        if ($isCorrectHomework && ($homeworkCorrectStatus == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_CORRECTED || $homeworkCorrectStatus == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_RECORRECTED)) {
            if (isset($lessonStudentHomeworkNeedAudit[self::$studentUid])) {
                self::$student[$fieldRule['key']] = AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_AUDIT;
            }
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldRule['key'], [
            'title' => '【巩固练习状态（含订正）】',
            '数据源'   => 'das:获取das章节汇聚信息中字段homeworkRecorrect；/pcassistant/correction/api/getcorrectionstudentlistbystatus 获取课程章节辅导老师维度巩固练习待审核学生列表（http://yapi.zuoyebang.cc/project/369/interface/api/99622 lina02）',
            '解释'=>'对于订正巩固练习，如果学生巩固练习状态为 待批改 且批改系统中状态为 待审核（兼职批改后需辅导老师审核），则设置状态为 待审核；状态枚举值：0"未布置"、1"待提交"、2"待批改"、3"待重提"、4"待重批"、5"已订正"、6"待审核"',
        ]);
    }


    /**
     * 作文报告 - 小学语文
     * 可以查看条件：小学语文课程（方舟控制）+ 章节巩固练习有作文题 + 巩固练习有评级
     */
    public static function getCompositionReport(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","homeworkLevel"])) {
            return [];
        }

        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //章节巩固练习作文配置
        $lessonCompositionConf = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLessonComposition");

        //从es lu 获取 巩固练习评级数据
        $studentHomeworkInfo = $studentLessonData[self::$studentUid];

        $hasReport = 0;
        if (isset($lessonCompositionConf[AssistantDesk_Data_CommonParams::$lessonId]['tidList']) && !empty($lessonCompositionConf[AssistantDesk_Data_CommonParams::$lessonId]['tidList']) && isset($studentHomeworkInfo['homeworkLevel']) && $studentHomeworkInfo['homeworkLevel']) {
            $hasReport = 1;
        }
        self::$student['hasCompositionReport'] = $hasReport;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasCompositionReport', [
            'title' => '【作文报告】',
            '数据源'   => 'es.lu.exam_answer.exam7.correct_level有数据且接口/pcassistant/api/composition/getcompositiontid，字段tidList有值',
        ]);
    }

    /**
     * 获取阶段测完成状态
     */
    public static function getStageTestFinishStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isStageTestExamSubmit"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['stageTestFinishStatus'] = $studentLessonData[self::$studentUid]['isStageTestExamSubmit'] ? intval(AssistantDesk_TaskMap::EXAM_RESULT_FINISH) : AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH;
        self::$student['stageTestCommitStatus'] = $studentLessonData[self::$studentUid]['isStageTestExamSubmit'] ? intval(AssistantDesk_TaskMap::EXAM_RESULT_FINISH) : AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'stageTestFinishStatus', [
            'title' => '【获取阶段测完成状态】',
            '数据源'   => 'es.lu.exam_answer.exam9.is_submit',
        ]);
    }

    /**
     * 获取催到课电话状态
     */
    public static function getUrgeAttendPhoneStatus(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $attendCall = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAttendCallData");
        self::$student['attendCall'] = $attendCall[self::$studentUid] ? $attendCall[self::$studentUid] : 0;
    }

    /**
     * 获取巩固练习相似题作答结果（有相似卷）
     */
    public static function getHomeworkSimilar(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","examAnswer"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        $examInfo7  = $studentLessonData[self::$studentUid]['examAnswer']['exam7'];
        $examInfo33 = $studentLessonData[self::$studentUid]['examAnswer']['exam33'];
        self::$student['homeworkSimilar'] = 0;
        //未提交
        if($examInfo7['is_submit'] == 0){
            self::$student['homeworkSimilar'] = 0;
        }
        //全对
        if($examInfo7['is_amend']){
            self::$student['homeworkSimilar'] = 1;
        }
        //待看解析视频
        if($examInfo7['is_submit'] && !$examInfo7['is_amend'] && !$examInfo7['is_view_wrong_expound_video']){
            self::$student['homeworkSimilar'] = 2;
        }
        //待练相似题
        if($examInfo7['is_view_wrong_expound_video'] && !$examInfo33['is_submit']){
            self::$student['homeworkSimilar'] = 3;
        }
        //相似题练习已完成
        if($examInfo33['is_submit']){
            self::$student['homeworkSimilar'] = 4;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkSimilar', [
            'title' => '【获取巩固练习相似题作答结果（有相似卷）】',
            '数据源'   => 'es.lu.exam_answer.exam7.is_submit，es.lu.exam_answer.exam33.is_submit，is_amend，is_view_wrong_expound_video',
        ]);

    }

    /**
     * 获取巩固练习相似题作答结果（无相似卷）
     */
    public static function getHomeworkSimilarUn(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","examAnswer"])) {
            return [];
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examInfo7  = $studentLessonData[self::$studentUid]['examAnswer']['exam7'];
        self::$student['homeworkSimilarUn'] = 0;
        //未提交
        if($examInfo7['is_submit'] == 0){
            self::$student['homeworkSimilarUn'] = 0;
        }
        //全对
        if($examInfo7['is_amend']){
            self::$student['homeworkSimilarUn'] = 1;
        }
        //待看解析视频
        if($examInfo7['is_submit'] && !$examInfo7['is_amend'] && !$examInfo7['is_view_wrong_expound_video']){
            self::$student['homeworkSimilar'] = 2;
        }
        //已看解析视频
        if($examInfo7['is_submit'] && !$examInfo7['is_amend'] && $examInfo7['is_view_wrong_expound_video']){
            self::$student['homeworkSimilar'] = 2;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkSimilarUn', [
            'title' => '【获取巩固练习相似题作答结果（无相似卷）】',
            '数据源'   => 'es.lu.exam_answer.exam7.is_submit，es.lu.exam_answer.exam33.is_submit，is_amend，is_view_wrong_expound_video',
        ]);
    }

    /**
     * 学分（课程）
     */
    public static function getCourseScore(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $excitationDatas  = Common_Singleton::getInstanceData("AssistantDesk_FieldToData", "getExcitation", [AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$courseId]);
        self::$student['courseScore'] = $excitationDatas[self::$studentUid][AssistantDesk_Data_CommonParams::$courseId]['score'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'courseScore', [
            'title' => '【学分（课程）】',
            '数据源'   => '接口：/excitation/score/getcoursescorelist 获取激励系统数据，字段score',
        ]);
    }

//    /**
//     * 全勤奖
//     */
//    public static function getIsFullAttendance(){
//        $esCuData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");
//
//        self::$student['isFullAttendance'] = '无数据';
//        if ($esCuData[self::$studentUid]['isFullAttendance'] == 1) {
//            self::$student['isFullAttendance'] = '未获得';
//        }
//        if ($esCuData[self::$studentUid]['isFullAttendance'] == 2) {
//            self::$student['isFullAttendance'] = '已获得已领取';
//        }
//        if ($esCuData[self::$studentUid]['isFullAttendance'] == 3) {
//            self::$student['isFullAttendance'] = '已获得未领取';
//        }
//        if ($esCuData[self::$studentUid]['isFullAttendance'] == 4) {
//            self::$student['isFullAttendance'] = '已获得未领取';
//        }
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isFullAttendance', [
//            'title' => '【全勤奖】',
//            '数据源'   => 'es.cu.is_full_attendance',
//        ]);
//    }

    /**
     * 累计完课章节数
     */
    public static function getHasFinishedNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["finishNum"])) {
            return [];
        }
        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");
        self::$student['hasFinishedNum'] = $esCuData[self::$studentUid]['finishNum'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasFinishedNum', [
            'title' => '【累计完课章节数】',
            '数据源'   => 'idl_course_student_assistant.finish_num',
        ]);
    }

    /**
     * 累计回放章节数
     */
    public static function getHasPlaybackNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $dasCourseData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasCourseData");
        self::$student['hasPlaybackNum'] = $dasCourseData[self::$studentUid][AssistantDesk_Data_CommonParams::$courseId]['hasPlaybackNum'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasPlaybackNum', [
            'title' => '【累计回放章节数】',
            '数据源'   => 'Das：获取das课程汇聚信息，字段hasPlaybackNum.finish_num',
        ]);
    }

    /**
     * 堂堂测提交次数
     */
    public static function getTangtangceSubmitNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["tangTangExamSubmitCount"])) {
            return [];
        }

        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");

        $isHxCourse = Common_Singleton::getInstanceData("AssistantDesk_Tools", "checkIsHx", [AssistantDesk_Data_CommonParams::$courseId]);
        if(!$isHxCourse){
            self::$student['tangtangceSubmitNum'] = $lessonStudentAggData[self::$studentUid]['tangTangExamSubmitCount'];
        }else{
            //堂堂测完成次数：取值实际完成提交堂堂测章节的次数/总需提交堂堂测数量
            //总需提交堂堂测数量：已开课章节的数量；
            $courseInfo         = Common_Singleton::getInstanceData(
                AssistantDesk_Course::class,
                "getCourseInfo",
                [AssistantDesk_Data_CommonParams::$courseId]
            );
            $lessonList =$courseInfo['lessonList'];
            $time = time();
            $totalNum = 0;
            foreach ($lessonList as $lessonId => $lesson){
                if($lesson['startTime'] < $time && $lesson['lessonType'] == 1){
                    $totalNum++;
                }
            }
            self::$student['tangtangceSubmitNum'] = sprintf("%s/%s", intval($lessonStudentAggData[self::$studentUid]['tangTangExamSubmitCount']), $totalNum);
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tangtangceSubmitNum', [
            'title' => '【堂堂测提交次数】',
            '数据源'   => 'es.idl_assistant_lesson_student_action.exam_answer.exam10.is_submit',
            '解释'=>'exam_answer.exam10.is_submit有值则堂堂测提交次数加一',
        ]);
    }

    /**
     * 同步练习提交章节数
     */
    public static function getSynchronousPracticeSubmitCount(){
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["synchronousPracticeSubmitCount"])) {
            return [];
        }

        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");
        self::$student['synchronousPracticeSubmitCount'] = $lessonStudentAggData[self::$studentUid]['synchronousPracticeSubmitCount'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'synchronousPracticeSubmitCount', [
            'title' => '【堂堂测提交次数】',
            '数据源'   => 'es.idl_assistant_lesson_student_action.exam_answer.exam11.is_submit',
            '解释'=>'exam_answer.exam11.is_submit有值则堂堂测提交次数加一',
        ]);
    }
    public static function getisLbpAttendCount(){
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["isLbpAttendCnt"])) {
            return [];
        }

        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");
        self::$student['isLbpAttendCnt'] = $lessonStudentAggData[self::$studentUid]['isLbpAttendCnt'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLbpAttendCnt', [
            'title' => '【录播累计观看章节数】',
            '数据源'   => 'es.idl_assistant_lesson_student_action.is_lbp_attend',
            '解释'=>'exam_answer.is_lbp_attend有值则堂堂测提交次数加一',
        ]);
    }
    public static function getisLbpAttendFinishCount(){
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["isLbpAttendFinishCnt"])) {
            return [];
        }

        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");
        self::$student['isLbpAttendFinishCnt'] = $lessonStudentAggData[self::$studentUid]['isLbpAttendFinishCnt'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLbpAttendFinishCnt', [
            'title' => '【录播累计观看完成数】',
            '数据源'   => 'es.idl_assistant_lesson_student_action.is_lbp_attend_finish',
            '解释'=>'exam_answer.is_lbp_attend_finish有值则堂堂测提交次数加一',
        ]);
    }

    /**
     * 优秀笔记章节数
     */
    public static function getExecllentNoteNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fsCourseStudent = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFsCourseStudentData");
        self::$student['execllentNoteNum'] = $fsCourseStudent[self::$studentUid]['execllentNoteNum'] ?: 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'execllentNoteNum', [
            'title' => '【优秀笔记章节数】',
            '数据源'   => '数据库tblFirstLineCourseStudent字段execllentNoteNum',
        ]);
    }

    /**
     * 笔记点评章节数
     */
    public static function getCommentNoteNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $fsCourseStudent = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFsCourseStudentData");
        self::$student['commentNoteNum'] = $fsCourseStudent[self::$studentUid]['allNoteReviews'] ?: 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'commentNoteNum', [
            'title' => '【笔记点评章节数】',
            '数据源'   => '数据库tblFirstLineCourseStudent字段allNoteReviews',
        ]);
    }

    /**
     * 回放时长
     */
    public static function getPlayback(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","playbackTimeIn7d"])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['playback'] = AssistantDesk_Tools::formatRemainTime($esLessonData[self::$studentUid]['playbackTimeIn7d'], true);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playback', [
            'title' => '【回放时长】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.playback_time_in_7d',
        ]);
    }

    /**
     * 观看状态（30min）
     */
    public static function getHasWatch(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","attendDuration","playbackTotalTime","playbackTimeIn7d"])) {
            return [];
        }
        $courseInfo   = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        //高中取全部回放时长，  小学和初中取7天内回放时长
        if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            self::$student['hasWatch'] = ($esLessonData[self::$studentUid]['attendDuration'] >= 1800 || $esLessonData[self::$studentUid]['playbackTotalTime'] >= 1800) ? '是' : '否';;
        } else {
            self::$student['hasWatch'] = ($esLessonData[self::$studentUid]['attendDuration'] >= 1800 || $esLessonData[self::$studentUid]['playbackTimeIn7d'] >= 1800) ? '是' : '否';;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasWatch', [
            'title' => '【观看状态（30min）】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.attend_duration、playback_time和playback_time_in_7d',
            '解释'=>'高中取全部回放时长，小学和初中取7天内回放时长',
        ]);
    }

    /**
     * 课前出镜到课状态
     */
    public static function getIsPreclassAttend(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isPreclassAttend",])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPreclassAttend'] = $esLessonData[self::$studentUid]['isPreclassAttend'] ? '是' : '否';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPreclassAttend', [
            'title' => '【课前出镜到课状态】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.is_preclass_attend',
            '解释'=>'is_preclass_attend含义 课前直播是否到课达标',
        ]);
    }

    /**
     * 课前出镜完课状态
     */
    public static function getIsPreclassFinishAttend(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isPreclassFinishAttend",])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPreclassFinishAttend'] = $esLessonData[self::$studentUid]['isPreclassFinishAttend'] ? '是' : '否';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPreclassFinishAttend', [
            'title' => '【课前出镜完课状态】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.is_preclass_finish_attend',
            '解释'=>'is_preclass_finish_attend含义 课前直播是否完课',
        ]);
    }

    /**
     * 课后出镜到课状态
     */
    public static function getIsPostclassAttend(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isPostclassAttend",])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPostclassAttend'] = $esLessonData[self::$studentUid]['isPostclassAttend'] ? '是' : '否';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPostclassAttend', [
            'title' => '【课后出镜到课状态】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.is_postclass_attend',
            '解释'=>'is_postclass_attend含义 课后直播是否到课达标',
        ]);
    }

    /**
     * 课后出镜完课状态
     */
    public static function getIsPostclassFinishAttend(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","isPostclassFinishAttend",])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['isPostclassFinishAttend'] = $esLessonData[self::$studentUid]['isPostclassFinishAttend'] ? '是' : '否';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isPostclassFinishAttend', [
            'title' => '【课后出镜完课状态】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.is_postclass_finish_attend',
            '解释'=>'is_postclass_finish_attend含义 课后直播是否完课',
        ]);
    }

    const LESSON_EXERCISE_DETAIL = '%d|%d|%d';

    /**
     * 预习（对/答/总）
     */
    public static function getPreviewAnswer(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","previewCorrectNum","previewParticipateNum"])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $courseInfo = Common_Singleton::getInstanceData(
            AssistantDesk_Course::class,
            "getCourseInfo",
            [AssistantDesk_Data_CommonParams::$courseId]
        );
        $examTotalInfo = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );
        $examInfo = Tools_Array::getNewKeyArray($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId], 'examType');
        $previewExamType = Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']] === Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY ? Api_Exam::BIND_TYPE_PREVIEW : Api_Exam::BIND_TYPE_POSTTEST_MORE;
        $totalNum = $examInfo[$previewExamType]['totalNum'];
        self::$student['previewAnswer'] = $totalNum ? sprintf(self::LESSON_EXERCISE_DETAIL, $esLessonData[self::$studentUid]['previewCorrectNum'], $esLessonData[self::$studentUid]['previewParticipateNum'], $totalNum) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'previewAnswer', [
            'title' => '【预习（对/答/总）】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.exam_answer.exam5.right_num、exam_answer.exam13.right_num、exam_answer.exam5.participate_num和exam_answer.exam13.participate_num；bdl_exam_relation.exam_type',
            '解释'=>'exam_type.totalNum大于0，则拼接（exam_answer.exam5.right_num+exam_answer.exam13.right_num）、（exam_answer.exam5.participate_num+exam_answer.exam13.participate_num）、（exam_type.totalNum）；否则为空字符串',
        ]);
    }

    /**
     * 互动题（对/答/总）
     */
    public static function getInteractionAnswer(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","exerciseRightNum","exerciseParticipateNum"])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //直播互动题
        self::$student['interactionAnswer'] = isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) ? sprintf(
            '直播：' . self::LESSON_EXERCISE_DETAIL,
            ($esLessonData[self::$studentUid]['exerciseRightNum'] ?? 0),
            ($esLessonData[self::$studentUid]['exerciseParticipateNum'] ?? 0),
            $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) : '-';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionAnswer', [
            'title' => '【互动题（对/答/总）】',
            '数据源'   => 'es:lu:exam_answer.exam1.right_num, es:lu:exam_answer.exam1.participate_num',
        ]);

    }

    /**
     * 观看互动题参与率
     */
    public static function getInteractionParticipateRate() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["student_uid", "inclass_participate_cnt", "playback_participate_cnt"])) {
            return [];
        }

        $commonEsLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $examTotalInfo      = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //观看互动题参与率
        if (isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) && $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] != 0) {
            $rate = (($commonEsLessonData[self::$studentUid]['inclass_participate_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_participate_cnt'] ?? 0)) / $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId];
            $rate = round($rate, 4) * 100 . '%';

            self::$student['interactionParticipateRate'] = $rate;

        } else {
            self::$student['interactionParticipateRate'] = '0%';
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionParticipateRate', [
            'title' => '【观看互动题参与率】',
            '数据源'   => 'es:公共数仓lu:inclass_participate_cnt + playback_participate_cnt 除总数',
        ]);
    }

    public static function getInteractionParticipateRateFilter() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["student_uid", "inclass_participate_cnt", "playback_participate_cnt", "inclass_question_cnt"])) {
            return [];
        }

        $commonEsLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        //观看互动题参与率
        static $grayHit;
        if (!isset($grayHit)) {
            $grayHit = Api_Assistantdeskgo_Api::grayHit(AssistantDesk_Data_CommonParams::$personUid, "inact_total_num_trans_gray");
        }
        if ($grayHit) {
            $totalNum = $commonEsLessonData[self::$studentUid]['inclass_question_cnt'] ?? 0;
            $num = 0;
            if (isset($totalNum) && $totalNum != 0) {
                $rate = (($commonEsLessonData[self::$studentUid]['inclass_participate_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_participate_cnt'] ?? 0)) / $totalNum;
                $num = round($rate, 4) * 100;
                $rate = round($rate, 4) * 100 . '%';
                self::$student['interactionParticipateRate'] = $rate;
            } else {
                self::$student['interactionParticipateRate'] = '0%';
            }
        } else {
            $examTotalInfo      = Common_Singleton::getInstanceData(
                AssistantDesk_ExamBind::class,
                "getInteractTotalNum",
                [AssistantDesk_Data_CommonParams::$lessonId]
            );

            $num = 0;
            if (isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) && $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] != 0) {
                $rate = (($commonEsLessonData[self::$studentUid]['inclass_participate_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_participate_cnt'] ?? 0)) / $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId];
                $num = round($rate, 4) * 100;
                $rate = round($rate, 4) * 100 . '%';
                self::$student['interactionParticipateRate'] = $rate;
            } else {
                self::$student['interactionParticipateRate'] = '0%';
            }
        }

        // 区间筛选支持
        switch (true) {
            case $num <= 50:
                self::$student['interactionParticipateRateFilter'] = 1;
                break;
            case 50 < $num && $num <= 60:
                self::$student['interactionParticipateRateFilter'] = 2;
                break;
            case 60 < $num && $num <= 70:
                self::$student['interactionParticipateRateFilter'] = 3;
                break;
            case 70 < $num && $num <= 80:
                self::$student['interactionParticipateRateFilter'] = 4;
                break;
            case 80 < $num && $num <= 90:
                self::$student['interactionParticipateRateFilter'] = 5;
                break;
            case 90 < $num && $num <= 99:
                self::$student['interactionParticipateRateFilter'] = 6;
                break;
            case $num > 99:
                self::$student['interactionParticipateRateFilter'] = 7;
                break;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionParticipateRate', [
            'title' => '【观看互动题参与率】',
            '数据源'   => 'es:公共数仓lu:inclass_participate_cnt + playback_participate_cnt 除总数',
        ]);
    }

    /**
     * 观看互动题正确率
     */
    public static function getInteractionRightRate() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["student_uid", "inclass_right_cnt", "playback_right_cnt"])) {
            return [];
        }

        $commonEsLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $examTotalInfo      = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //观看互动题正确率
        if (isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) && $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] != 0) {
            $rate = (($commonEsLessonData[self::$studentUid]['inclass_right_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_right_cnt'] ?? 0)) / $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId];
            $rate = round($rate, 4) * 100 . '%';

            self::$student['interactionRightRate'] = $rate;

        } else {
            self::$student['interactionRightRate'] = '0%';
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionRightRate', [
            'title' => '【观看互动题正确率】',
            '数据源'   => 'es:公共数仓lu:inclass_right_cnt + playback_right_cnt 除总数',
        ]);
    }

    public static function getInteractionRightRateFilter() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["student_uid", "inclass_right_cnt", "playback_right_cnt", "inclass_question_cnt"])) {
            return [];
        }

        $commonEsLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        //观看互动题正确率
        static $grayHit;
        if (!isset($grayHit)) {
            $grayHit = Api_Assistantdeskgo_Api::grayHit(AssistantDesk_Data_CommonParams::$personUid, "inact_total_num_trans_gray");
        }
        if ($grayHit) {
            $totalNum = $commonEsLessonData[self::$studentUid]['inclass_question_cnt'] ?? 0;
            $num = 0;
            if (isset($totalNum) && $totalNum != 0) {
                $rate = (($commonEsLessonData[self::$studentUid]['inclass_right_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_right_cnt'] ?? 0)) / $totalNum;
                $num = round($rate, 4) * 100;
                $rate = round($rate, 4) * 100 . '%';
                self::$student['interactionRightRate'] = $rate;
            } else {
                self::$student['interactionRightRate'] = '0%';
            }
        } else {
            $examTotalInfo      = Common_Singleton::getInstanceData(
                AssistantDesk_ExamBind::class,
                "getInteractTotalNum",
                [AssistantDesk_Data_CommonParams::$lessonId]
            );
            $num = 0;
            if (isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) && $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] != 0) {
                $rate = (($commonEsLessonData[self::$studentUid]['inclass_right_cnt'] ?? 0) + ($commonEsLessonData[self::$studentUid]['playback_right_cnt'] ?? 0)) / $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId];
                $num = round($rate, 4) * 100;
                $rate = round($rate, 4) * 100 . '%';
                self::$student['interactionRightRate'] = $rate;
            } else {
                self::$student['interactionRightRate'] = '0%';
            }
        }
        // 区间筛选支持
        switch (true) {
            case $num <= 50:
                self::$student['interactionRightRateFilter'] = 1;
                break;
            case 50 < $num && $num <= 60:
                self::$student['interactionRightRateFilter'] = 2;
                break;
            case 60 < $num && $num <= 70:
                self::$student['interactionRightRateFilter'] = 3;
                break;
            case 70 < $num && $num <= 80:
                self::$student['interactionRightRateFilter'] = 4;
                break;
            case 80 < $num && $num <= 90:
                self::$student['interactionRightRateFilter'] = 5;
                break;
            case 90 < $num && $num <= 99:
                self::$student['interactionRightRateFilter'] = 6;
                break;
            case $num > 99:
                self::$student['interactionRightRateFilter'] = 7;
                break;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionRightRate', [
            'title' => '【观看互动题正确率】',
            '数据源'   => 'es:公共数仓lu:inclass_right_cnt + playback_right_cnt 除总数',
        ]);
    }

    /**
     * 观看互动题（对/答/总），不是互动题
     */
    public static function getInteractionAnswerAllOld() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid","playback_right_cnt","inclass_right_cnt","playback_participate_cnt","inclass_participate_cnt"])) {
            return [];
        }

        $esLessonData  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //观看互动题
        self::$student['interactionAnswerAll'] = isset($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) ? sprintf(
            '观看：' . self::LESSON_EXERCISE_DETAIL,
            (($esLessonData[self::$studentUid]['playback_right_cnt'] ?? 0) + ($esLessonData[self::$studentUid]['inclass_right_cnt'] ?? 0)),
            (($esLessonData[self::$studentUid]['playback_participate_cnt'] ?? 0) + ($esLessonData[self::$studentUid]['inclass_participate_cnt'] ?? 0)),
            $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId]) : '-';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionAnswerAll', [
            'title' => '【观看互动题】',
            '数据源'   => 'es:commonlu:playback_right_cn + es:commonlu:inclass_right_cnt, es:commonlu:playback_participate_cnt + es:commonlu:inclass_participate_cnt',
        ]);
    }

    public static function getInteractionAnswerAll() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid","playback_right_cnt","inclass_right_cnt","playback_participate_cnt","inclass_participate_cnt", "exam1"])) {
            return [];
        }

        $esLessonData  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $totalNum = 0;
        if (isset($esLessonData[self::$studentUid]['exam1'])) {
            $exam1 = json_decode($esLessonData[self::$studentUid]['exam1'], true);
            $totalNum = $exam1['total_num'];
        }

        //观看互动题
        self::$student['interactionAnswerAll'] = $totalNum ? sprintf(
            '观看：' . self::LESSON_EXERCISE_DETAIL,
            (($esLessonData[self::$studentUid]['playback_right_cnt'] ?? 0) + ($esLessonData[self::$studentUid]['inclass_right_cnt'] ?? 0)),
            (($esLessonData[self::$studentUid]['playback_participate_cnt'] ?? 0) + ($esLessonData[self::$studentUid]['inclass_participate_cnt'] ?? 0)),
            $totalNum) : '-';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionAnswerAll', [
            'title' => '【观看互动题】',
            '数据源'   => 'es:commonlu:playback_right_cn + es:commonlu:inclass_right_cnt, es:commonlu:playback_participate_cnt + es:commonlu:inclass_participate_cnt',
        ]);
    }

    public static function getInteractionSubmitStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","playbackParticipateNum","exerciseParticipateNum"])) {
            return [];
        }

        $esLessonData  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //互动题提交情况 0全部提交，1全部未提交，2部分提交
        $TotalNum  = $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] ?? 0;
        $playbackParticipateNum  = $esLessonData[self::$studentUid]['playbackParticipateNum'] ?? 0;
        $exerciseParticipateNum  = $esLessonData[self::$studentUid]['exerciseParticipateNum'] ?? 0;
        if (($playbackParticipateNum + $exerciseParticipateNum ) >= $TotalNum) {
            //全部提交
            self::$student['interactionSubmitStatus']="1";
        }else if (($playbackParticipateNum + $exerciseParticipateNum ) == 0) {
            //全部未提交
            self::$student['interactionSubmitStatus']="2";
        }else if (($playbackParticipateNum + $exerciseParticipateNum ) < $TotalNum) {
            //部分提交
            self::$student['interactionSubmitStatus']="3";
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionSubmitStatus', [
            'title' => '【互动题提交情况】',
            '数据源'   => 'es:lu:playback_participate_num + es:lu:exam_answer.exam1.participate_num',
        ]);
    }

    public static function getInteractionRightStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","playbackRightNum","exerciseRightNum"])) {
            return [];
        }

        $esLessonData  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(
            AssistantDesk_ExamBind::class,
            "getInteractTotalNum",
            [AssistantDesk_Data_CommonParams::$lessonId]
        );

        //互动题答题情况 0全部正确，1全部错误，2部分正确
        $TotalNum  = $examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId] ?? 0;
        $playbackRightNum  = $esLessonData[self::$studentUid]['playbackRightNum'] ?? 0;
        $exerciseRightNum  = $esLessonData[self::$studentUid]['exerciseRightNum'] ?? 0;
        if (($playbackRightNum + $exerciseRightNum ) >= $TotalNum) {
            //全部正确
            self::$student['interactionRightStatus']="1";
        }else if (($playbackRightNum + $exerciseRightNum ) == 0) {
            //全部不正确
            self::$student['interactionRightStatus']="2";
        }else if (($playbackRightNum + $exerciseRightNum ) < $TotalNum) {
            //部分正确
            self::$student['interactionRightStatus']="3";
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactionRightStatus', [
            'title' => '【互动题答题情况】',
            '数据源'   => 'es:lu:playback_right_num + es:lu:exam_answer.exam1.right_num',
        ]);
    }

    /**
     * 堂堂测完成情况
     */
    public static function getInclassTest(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","tangTangExamCorrectNum","tangTangExamParticipateNum","isTangTangExamSubmit","tangTangExamScore"])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(AssistantDesk_ExamBind::class, "getTotalNum", [AssistantDesk_Data_CommonParams::$lessonId]);
        $examTotalInfo = Tools_Array::getNewKeyArray($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId], 'examType');

        $tangTangToalNum    = ($examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['totalNum'] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['bindStatus']) ?
            intval($examTotalInfo[Api_Exam::BIND_TYPE_TEST_IN_CLASS]['totalNum']) : 0;

        $isHxCourse = Common_Singleton::getInstanceData(AssistantDesk_Tools::class, "checkIsHx", [AssistantDesk_Data_CommonParams::$courseId]);

        if(!$isHxCourse){
            self::$student['inclassTest'] = sprintf(
                self::LESSON_EXERCISE_DETAIL,
                $esLessonData[self::$studentUid]['tangTangExamCorrectNum'],
                $esLessonData[self::$studentUid]['tangTangExamParticipateNum'],
                $tangTangToalNum
            );
        }else{
            //小英的课展示得分
            self::$student['inclassTest'] = $esLessonData[self::$studentUid]['isTangTangExamSubmit']
                ? sprintf('%s分', ($esLessonData[self::$studentUid]['tangTangExamScore'] / 10))
                : '未提交';
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'inclassTest', [
            'title' => '【堂堂测完成情况】',
            '数据源'   => 'es:lu:exam_answer.exam10.right_num、exam_answer.exam10.participate_num、exam_answer.exam10.is_submit、exam_answer.exam10.answer_score；es：bdl_exam_relation.total_num、bind_status',
            '解释'=>'非浣熊得分：right_num+participate_num+total_num；小英的课展示得分：is_submit>0?answer_score/10：未提交',
        ]);
    }

    /**
     * 同步练习（对/答/总）
     */
    public static function getSynchronousPractice(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","synchronousPracticeCorrectNum","synchronousPracticeParticipateNum"])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $examTotalInfo = Common_Singleton::getInstanceData(AssistantDesk_ExamBind::class, "getTotalNum", [AssistantDesk_Data_CommonParams::$lessonId]);

        $examTotalInfo = Tools_Array::getNewKeyArray($examTotalInfo[AssistantDesk_Data_CommonParams::$lessonId], 'examType');

        self::$student['synchronousPractice'] = ($examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['totalNum'] &&
            $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['bindStatus']) ?
            sprintf(self::LESSON_EXERCISE_DETAIL,
                $esLessonData['synchronousPracticeCorrectNum'],
                $esLessonData['synchronousPracticeParticipateNum'],
                $examTotalInfo[Api_Exam::BIND_TYPE_PRIMARY_MATH_PREVIEW]['totalNum']) :
            '-';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'synchronousPractice', [
            'title' => '【同步练习（对/答/总）】',
            '数据源'   => 'es:lu:exam_answer.exam11.right_num、exam_answer.exam11.participate_num；es：bdl_exam_relation.total_num、bind_status',
            '解释'=>'exam_answer.exam11.right_num、exam_answer.exam11.participate_num、total_num、bind_status三者拼接',
        ]);
    }

    /**
     * 聊天次数
     */
    public static function getChatNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","chatNum"])) {
            return [];
        }

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['chatNum'] = intval($esLessonData[self::$studentUid]['chatNum']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'chatNum', [
            'title' => '【聊天次数】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.chat_num',
        ]);
    }

    /**
     * 抢麦次数
     */
    public static function getMicroNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","micNum"])) {
            return [];
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['microNum'] = intval($esLessonData[self::$studentUid]['micNum']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'microNum', [
            'title' => '【抢麦次数】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.mic_num',
        ]);
    }

    /**
     * 处理一线课程学生数据表的数据信息
     * @param $studentUid
     * @return bool
     */
    public static function handleFsCourseStudentData(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $examTypeConfig = $fieldRule['serviceConfig']['examType'];
        $fsCourseStudent = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFsCourseStudentData");
        self::$student['dxCorrectStatus'] = 0;
        self::$student['dxCorrectStatus_' . $examTypeConfig] = 0;
        $extData             = $fsCourseStudent[self::$studentUid]['extData'] ? @json_decode($fsCourseStudent[self::$studentUid]['extData'], true) : [];
        $examInfo            = is_array($extData['lessonList'][AssistantDesk_Data_CommonParams::$lessonId]['examInfo']) ? $extData['lessonList'][AssistantDesk_Data_CommonParams::$lessonId]['examInfo'] : [];

        foreach ($examInfo as $examType => $info) {
            // 测试类型 7 巩固练习 $info['dxCorrectStatus'] 1启用 2禁用
            if ($examType == 7 && $info['dxCorrectStatus'] == 1) {
                self::$student['dxCorrectStatus'] = $info['dxCorrectStatus'];
            }
            // 老的字段lpc字段再用，老的 dxCorrectStatus 不做下线
            if ($info['dxCorrectStatus'] == 1) {
                self::$student['dxCorrectStatus_' . $examType] = $info['dxCorrectStatus'];
            }
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'dxCorrectStatus', [
            'title'  => '【定向批改状态】',
            'source' => 'mysql：tblFirstLineCourseStudent，字段ext_data，',
            '解释'=>'ext_data.dxCorrectStatus枚举值：1启用、2禁用。并且ext_data.dxCorrectStatus为1，则取1否则取0',
        ]);
    }

    /**
     * 操作-跳转维系详情
     */
    public static function getKeepDetail(){

    }

    /**
     * 章节7天内累计回放时长状态
     * 使用回放总时长判断是否观看、使用7天累计回放时长判断回放是否合格，等于大于30分钟为合格
     * 1 回放合格 2回放不合格 3未观看
     */
    public static function getLessonPlaybackIn7Day()
    {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid","playbackTimeIn7d","playbackTotalTime"])) {
            return [];
        }
        $studentUid = self::$studentUid;
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        self::$student['playbackStatus'] = AssistantDesk_Playback::in7DayStatus($esLessonData[$studentUid]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackStatus', [
            'title' => '【章节7天内累计回放时长状态】',
            '数据源'   => 'es：idl_assistant_lesson_student_action.playback_time_in_7d和playback_time',
            '解释'=>'状态枚举值：1回放合格、2回放不合格、3未观看。处理逻辑：playback_time<0为未观看；playback_time<0且playback_time_in_7d>30分钟为回访合格否则不合格',
        ]);
    }

    /**
     * 初中高中章节回放是否下载
     * playbackIsDownload
     */
    public static function getPlaybackIsDownload()
    {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentUid = self::$studentUid;
        $stuPlaybackData = Common_Singleton::getInstanceData(AssistantDesk_Playback::class, "lessonStudentData", [AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]);
        self::$student['playbackIsDownload'] = AssistantDesk_Playback::downloadStatus($stuPlaybackData[$studentUid]['ext_data']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackIsDownload', [
            'title' => '【初中高中章节回放是否下载】',
            '数据源'   => '接口：/playback/api/getplaybackbystulessonIds（单章节多学生回放数据获取 http://yapi.zuoyebang.cc/project/269/interface/api/151033) 字段ext_data.doneDownload',
            '解释'=>'状态枚举值：1已下载、2未下载',
        ]);
    }

    /**
     * 获取学员分层数据相关信息
     */
    public static function getDelaminationLevel(){
        $fields = AssistantDesk_Data_DataQuery::getAcsDataFromIDLUserDelaminationDetailFormatFields();
        if (AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields($fields)) {
            return true;
        }

        // 查询学员分层数据信息
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelamination");

        // 用户分层
        self::$student['userBedType'] = $userDelaminationDatas[self::$studentUid]['userDelaminationContinueLevel'] ?: "-";

        // 渠道
        self::$student['userSecondSource'] = $userDelaminationDatas[self::$studentUid]['userSecondSource'] ?: "-";

        //分层排名
        self::$student['userBedRankRate'] = $userDelaminationDatas[self::$studentUid]['userBedRankRate'] ?: "-";

        //当前学季报名科目
        self::$student['subject_num_semester'] = $userDelaminationDatas[self::$studentUid]['subject_num_semester'] ?: "-";

        // 用户类型
        self::$student['userBedUserType'] = $userDelaminationDatas[self::$studentUid]['userBedUserType'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userBedType', [
            'title'  => '【用户分层】',
            'source' => '用户分层：es.idl_user_delamination_alias.user_delamination_continue_level，
            渠道：es.idl_user_delamination_alias.user_second_source，
            用户类型：es.idl_user_delamination_alias.leap_user_type,
            分层排名：分子：es.idl_user_delamination_alias.same_type_rank，分母：same_type_pv,
            当前学季报名科目：es.idl_user_delamination_alias.subject_num_semester,',
            '解释'     => '用户分层指标详解：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=319395735',

        ]);

        $courseInfo   = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        // 高中用户分层
        if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            // 章节到课率
            self::$student['attendLongLessonNumTimelessRate'] = $userDelaminationDatas[self::$studentUid]['attendLongLessonNumTimelessRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendLongLessonNumTimelessRate', [
                'title'  => '【章节观看率】',
                'source' => 'es：idl_user_delamination_alias.retain_detail.attend_long_lesson_num_timelessZ和need_attend_lesson_num字段',
                '解释'=>'attend_long_lesson_num/need_attend_lesson_num',
            ]);
            // 章节完课率
            self::$student['attendFinishLessonNumTimelessRate'] = $userDelaminationDatas[self::$studentUid]['attendFinishLessonNumTimelessRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendFinishLessonNumTimelessRate', [
                'title'  => '【章节完课率】',
                'source' => 'es：idl_user_delamination_alias.retain_detail.attend_finish_lesson_num_timeless和need_attend_lesson_num字段',
                '解释'=>'attend_finish_lesson_num_timeless/need_attend_lesson_num',
            ]);

            // 总语音沟通次数
            self::$student['totalVoiceChatNum'] = $userDelaminationDatas[self::$studentUid]['totalVoiceChatNum'];
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'totalVoiceChatNum', [
                'title'  => '【总语音沟通次数】',
                'source' => 'es：idl_user_delamination_alias.retain_detail.total_voice_chat_num',
            ]);

        }else{
            // 章节到课率
            self::$student['attendLessonRate'] = $userDelaminationDatas[self::$studentUid]['attendLessonRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendLessonRate', [
                'title'  => '【章节到课率】',
                'source' => 'es：idl_user_delamination_alias.retain_detail.attend_long_lesson_num和need_attend_lesson_num字段',
                '解释'=>'attend_long_lesson_num/need_attend_lesson_num',
            ]);
            // 章节完课率
            self::$student['finshLessonRate'] = $userDelaminationDatas[self::$studentUid]['finshLessonRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'finshLessonRate', [
                'title'  => '【章节完课率】',
                'source' => 'es：idl_user_delamination_alias.retain_detail.attend_finish_lesson_num和need_attend_lesson_num字段',
                '解释'=>'attend_finish_lesson_num/need_attend_lesson_num',
            ]);

        }

        // 2022春工作台分层 新增指标 https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=344776324
        // 小学分层
        if (Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            // 小灶课完课率
            self::$student['assistantCourseAttendFinishRate'] = $userDelaminationDatas[self::$studentUid]['assistantCourseAttendFinishRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'assistantCourseAttendFinishRate', [
                'title'  => '【小灶课完课率】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.assistantcourse_attend_finish_num和assistantcourse_need_attend_num字段',
                '解释'=>'assistantcourse_attend_finish_num/assistantcourse_attend_finish_num',
            ]);

            // 小灶课到课率
            self::$student['assistantCourseAttendRate'] = $userDelaminationDatas[self::$studentUid]['assistantCourseAttendRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'assistantCourseAttendRate', [
                'title'  => '【小灶课到课率】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.assistantcourse_attend_num和assistantcourse_need_attend_num字段',
                '解释'=>'assistantcourse_attend_num/assistantcourse_need_attend_num',
            ]);

            // 全场景有效沟通次数
            self::$student['sent_total_num'] = $userDelaminationDatas[self::$studentUid]['sent_total_num'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'sent_total_num', [
                'title'  => '【全场景有效沟通次数】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.sent_total_num',
                '解释'=>'',
            ]);

            // 群聊用户发送的消息数
            self::$student['group_user_message_sent_total_num'] = $userDelaminationDatas[self::$studentUid]['group_user_message_sent_total_num'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'group_user_message_sent_total_num', [
                'title'  => '【群聊用户发送的消息数】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.group_user_message_sent_total_num',
                '解释'=>'',
            ]);

        }else{
            // 初高
            // 学员在读科目数
            self::$student['subject_num'] = $userDelaminationDatas[self::$studentUid]['subject_num'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'subject_num', [
                'title'  => '【学员在读科目数】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.subject_num',
                '解释'=>'',
            ]);

            // 章节观看到课率（T+14）
            self::$student['attendLongLessonNum14dRate'] = $userDelaminationDatas[self::$studentUid]['attendLongLessonNum14dRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendLongLessonNum14dRate', [
                'title'  => '【章节观看到课率（T+14）】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.attend_long_lesson_num_14d/need_attend_lesson_num',
                '解释'=>'',
            ]);

            // 章节观看完课率（T+14） 60%
            self::$student['attendFinishLessonNum14dRate'] = $userDelaminationDatas[self::$studentUid]['attendFinishLessonNum14dRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendFinishLessonNum14dRate', [
                'title'  => '【章节观看完课率（T+14） 60%】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.attend_finish_lesson_num_14d/need_attend_lesson_num',
                '解释'=>'',
            ]);

            // 巩固练习订正率
            self::$student['consolidationExamSRate'] = $userDelaminationDatas[self::$studentUid]['consolidationExamSRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'consolidationExamSRate', [
                'title'  => '【巩固练习订正率】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.consolidation_exam_s_num/need_attend_lesson_num',
                '解释'=>'',
            ]);

            // 巩固练习首次作答正确率
            self::$student['homeworkTidFirstRightCntRate'] = $userDelaminationDatas[self::$studentUid]['homeworkTidFirstRightCntRate'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'homeworkTidFirstRightCntRate', [
                'title'  => '【巩固练习首次作答正确率】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.homework_tid_first_right_cnt/homework_tid_first_correct_cnt',
                '解释'=>'',
            ]);

            // 总语音沟通时长
            self::$student['total_voice_chat_duration'] = $userDelaminationDatas[self::$studentUid]['total_voice_chat_duration'] ?: "-";
            AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'total_voice_chat_duration', [
                'title'  => '【总语音沟通时长】',
                'source' => 'es：ads_zbk_idl_user_delamination_v1.total_voice_chat_duration',
                '解释'=>'',
            ]);

        }

        // 巩固练习提交率
        self::$student['consolidationExamFinishRate'] = $userDelaminationDatas[self::$studentUid]['consolidationExamFinishRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'consolidationExamFinishRate', [
            'title'  => '【巩固练习提交率】',
            'source' => 'es：ads_zbk_idl_user_delamination_v1.consolidation_exam_finish_num/consolidation_exam_total_num',
            '解释'=>'',
        ]);

        // 用户发起的全场景有效沟通次数
        self::$student['user_sent_total_num'] = $userDelaminationDatas[self::$studentUid]['user_sent_total_num'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'user_sent_total_num', [
            'title'  => '【用户发起的全场景有效沟通次数】',
            'source' => 'es：ads_zbk_idl_user_delamination_v1.user_sent_total_num',
            '解释'=>'',
        ]);

        // 全场景有效沟通次数
        self::$student['sent_total_num'] = $userDelaminationDatas[self::$studentUid]['sent_total_num'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'sent_total_num', [
            'title'  => '【全场景有效沟通次数】',
            'source' => 'es：ads_zbk_idl_user_delamination_v1.user_sent_total_num',
            '解释'=>'',
        ]);

        // 互动题题目完成率
        self::$student['interactiveExamFinshRate'] = $userDelaminationDatas[self::$studentUid]['interactiveExamFinshRate'];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactiveExamFinshRate', [
            'title'  => '【互动题题目完成率】',
            'source' => 'es：idl_user_delamination_alias.retain_detail.interactive_exam_finish_num和interactive_exam_total_num字段',
            '解释'=>'interactive_exam_finish_num/interactive_exam_total_num',
        ]);

        // 互动题题目正确率
        self::$student['interactiveExamRightRate'] = $userDelaminationDatas[self::$studentUid]['interactiveExamRightRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'interactiveExamRightRate', [
            'title'  => '【互动题题目正确率】',
            'source' => 'es：idl_user_delamination_alias.retain_detail.interactive_exam_right_num和need_attend_lesson_num字段',
            '解释'=>'interactive_exam_right_num/interactive_exam_finish_num',
        ]);

        // 堂堂测题目正确率（只有语文）
        self::$student['alwaysTestExamRightRate'] = $userDelaminationDatas[self::$studentUid]['alwaysTestExamRightRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'alwaysTestExamRightRate', [
            'title'  => '【堂堂测题目正确率（只有语文）】',
            'source' => 'es：idl_user_delamination_alias.always_test_exam_right_num和always_test_exam_finish_num字段',
            '解释'=>'always_test_exam_right_num/always_test_exam_finish_num',
        ]);

        // 巩固练习题目正确率
        self::$student['consolidationExamRightRate'] = $userDelaminationDatas[self::$studentUid]['consolidationExamRightRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'consolidationExamRightRate', [
            'title'  => '【巩固练习题目正确率】',
            'source' => 'es：idl_user_delamination_alias.retain_detail.consolidation_exam_right_num和consolidation_exam_finish_num字段',
            '解释'=>'consolidation_exam_right_num/consolidation_exam_finish_num',
        ]);

        // 课前预习完成率
        self::$student['previewExamFinishRate'] = $userDelaminationDatas[self::$studentUid]['previewExamFinishRate'] ?: "-";
        // 堂堂测完成率
        self::$student['alwaysTestExamFinishRate'] = $userDelaminationDatas[self::$studentUid]['alwaysTestExamFinishRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'alwaysTestExamFinishRate', [
            'title'  => '【堂堂测完成率】',
            'source' => 'es：idl_user_delamination_alias.always_test_exam_finish_num和always_test_exam_total_num字段',
            '解释'=>'always_test_exam_finish_num/always_test_exam_total_num',
        ]);

        // 观看互动题题目完成率
        self::$student['playbackParticipateRate'] = $userDelaminationDatas[self::$studentUid]['playbackParticipateRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackParticipateRate', [
            'title'  => '【观看互动题题目完成率】',
            'source' => 'es：idl_user_delamination_alias.playback_participate_num和playback_all_num字段',
            '解释'=>'playback_participate_num/playback_all_num',
        ]);

        // 观看互动题题目正确率
        self::$student['playbackRightRate'] = $userDelaminationDatas[self::$studentUid]['playbackRightRate'] ?: "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playbackRightRate', [
            'title'  => '【观看互动题题目正确率】',
            'source' => 'es：idl_user_delamination_alias.playback_right_num和playback_all_num字段',
            '解释'=>'playback_right_num/playback_all_num',
        ]);
        // 微信及时回复率
        self::$student['wechatReplyRate'] = $userDelaminationDatas[self::$studentUid]['wechatReplyRate'] ?: "-";
        // 学员发起的有效session数
        self::$student['userActiveValidSessionCount'] = $userDelaminationDatas[self::$studentUid]['userActiveValidSessionCount'] ?: "-";


        // 累计有效沟通会话数
        self::$student['validSessionTotalNum'] = $userDelaminationDatas[self::$studentUid]['validSessionTotalNum'] ?: "-";
        // 用户历史累计群内发言数
        self::$student['groupUserMessageSentTotalNum'] = $userDelaminationDatas[self::$studentUid]['groupUserMessageSentTotalNum'] ?: "-";

    }


    /**
     * 获取维系记录
     * @throws ReflectionException
     */
    public static function getStudentInterview() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $studentInterview = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentInterview");

        self::$student['studentInterview']         = $studentInterview[self::$studentUid];
        self::$student['studentInterviewCnt']      = count($studentInterview[self::$studentUid]);
        self::$student['studentInterviewLastTime'] = $studentInterview[self::$studentUid][0]['interviewTime'] ?? '';
        self::$student['studentInterviewLast']     = $studentInterview[self::$studentUid][0]['content'] ?? '';

    }

    /**
     * 获取课后电话回访状态
     * @throws ReflectionException
     */
    public static function getAfterClassPhoneBackInterviewStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["isAfterClassPhoneBackInterview","isAfterClassPhoneBackInterview1","isAfterClassPhoneBackInterview2","isAfterClassPhoneBackInterview3","isAfterClassPhoneBackInterview4"])) {
            return [];
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['afterClassPhoneBackInterviewStatus']  = intval($esCuData[self::$studentUid]['isAfterClassPhoneBackInterview']);
//        self::$student['afterClassPhoneBackInterviewStatus1'] = intval($esCuData[self::$studentUid]['isAfterClassPhoneBackInterview1']);
//        self::$student['afterClassPhoneBackInterviewStatus2'] = intval($esCuData[self::$studentUid]['isAfterClassPhoneBackInterview2']);
//        self::$student['afterClassPhoneBackInterviewStatus3'] = intval($esCuData[self::$studentUid]['isAfterClassPhoneBackInterview3']);
//        self::$student['afterClassPhoneBackInterviewStatus4'] = intval($esCuData[self::$studentUid]['isAfterClassPhoneBackInterview4']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'afterClassPhoneBackInterviewStatus', [
            'title'  => '【课后电话回访状态】',
            'source' => 'es.cu.is_after_class_phone_back_interview,',
            '解释'     => '包括课后电话回访状态，1次，2次，3次，4次',
        ]);
    }

    /**
     * 获取3分钟电话回访状态
     * @throws ReflectionException
     */
    public static function getThreePhoneBackInterviewStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["phoneBackinterviewCount"])) {
            return [];
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['threePhoneBackInterviewStatus']     = intval($esCuData[self::$studentUid]['phoneBackinterviewCount'] >= 1);
    }

    /**
     * 获取3分钟电话家访状态
     * getThreePhoneInterviewStatus
     */
    public static function getThreePhoneInterviewStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["isInterviewCall3min"])) {
            return [];
        }
        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");

        self::$student['isInterviewCall3min'] = intval($esCuData[self::$studentUid]['isInterviewCall3min']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isInterviewCall3min', [
            'title' => '【获取3分钟电话家访状态】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段is_interview_call_3min',
            '解释'=>'',
        ]);
    }

    /**
     * 课中触达次数
     */
    public static function getInclassTouchCnt() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $studentUid = self::$studentUid;
        $arrTouchCnt = Common_Singleton::getInstanceData(
            Api_Interactui::class,
            'getTouchCnt',
            [
                AssistantDesk_Data_CommonParams::$courseId,
                AssistantDesk_Data_CommonParams::$studentUids,
                AssistantDesk_Data_CommonParams::$lessonId,
                AssistantDesk_Data_CommonParams::$personUid,
            ]
        );

        self::$student['inclassTouchCnt'] = $arrTouchCnt[$studentUid]['totalCnt'] ?: 0;
        self::$student['inclassMicCnt'] = $arrTouchCnt[$studentUid]['micCnt'] ?: 0;
        self::$student['inclassShowCommentCnt'] = $arrTouchCnt[$studentUid]['showCommentCnt'] ?: 0;
        self::$student['inclassPhotoCardCnt'] = $arrTouchCnt[$studentUid]['photoCardCnt'] ?: 0;
        self::$student['inclassRecognizeReadCnt'] = $arrTouchCnt[$studentUid]['recognizeReadCnt'] ?: 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'inclassTouchCnt', [
            'title'  => '【课中触达次数，视频连麦总次数，评论上墙总次数，拍照主观卡总次数，认读题总次数】',
            '数据源' => '接口/interactui/v2/gettouchcnt获取，这部分数据均是此接口获取：inclassTouchCnt，inclassMicCnt，inclassShowCommentCnt，inclassPhotoCardCnt，inclassRecognizeReadCnt',
        ]);
    }

    /**
     * 获取学员秋下标签
     */
    public static function getQiuxiaLable() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $studentQiuxiaLabel = Common_Singleton::getInstanceData(
            AssistantDesk_Qiuxia::class,
            'getLabels',
            [
                AssistantDesk_Data_CommonParams::$courseId,
                AssistantDesk_Data_CommonParams::$assistantUid,
                AssistantDesk_Data_CommonParams::$lessonId,
            ]
        );


        self::$student['qiuxiaLabel'] = $studentQiuxiaLabel[self::$studentUid] ?: -1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'qiuxiaLabel', [
            'title'  => '【学员秋下标签】',
            'source' => 'es订单：字段：discount_type，699为第一阶段，899为第二阶段',
        ]);
    }

//    /**
//     * 获取预约状态
//     */
//    public static function getPreOrder(){
//        $continueData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery","getEsContinueData");
//
//        //预约状态
//        self::$student['preOrder'] = $continueData[self::$studentUid]['preIdentity'] ? intval($continueData[self::$studentUid]['preIdentity']) : 0;
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'preOrder', [
//            'title'  => '【预约状态】',
//            'source' => 'es订单：字段：pre_identity',
//        ]);
//    }

    /**
     * 获取大促页勾选的科目   上游无法提供批量接口且限流，所以由列表展示改为hover展示
     */
    public static function getSelectedSubjects(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        self::$student['selectedSubjects'] = "查看";
    }

    /**
     * 获取下一学季报名科目数
     */
    public static function getNextTermEnrollNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $getStudentPreOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getStudentPreOrder");

        self::$student['nextTermEnrollNum'] = intval($getStudentPreOrders[self::$studentUid]['subjectNum']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'nextTermEnrollNum', [
            'title'  => '【下一学季报名科目数】',
            'source' => 'es：idl_season_student_info.subject_num',
        ]);

    }

    /**
     * 获取下一学季报名科目
     */
    public static function getNextTermEnrollDetail(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $getStudentPreOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"getStudentPreOrder");
        $nextTermEnrollDetail = [];
        if(is_array($getStudentPreOrders[self::$studentUid]['subject'])){
            foreach ($getStudentPreOrders[self::$studentUid]['subject'] as $preSubject){
                $nextTermEnrollDetail[] = AssistantDesk_Common_Keyconst::getSubject()[$preSubject];
            }
        }

        self::$student['nextTermEnrollDetail'] = $nextTermEnrollDetail ? implode(',',$nextTermEnrollDetail) : "";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'nextTermEnrollDetail', [
            'title'  => '【下一学季报名科目】',
            'source' => 'es：idl_season_student_info.subject',
        ]);
    }

    /**
     * 获取隔季报名科目详情
     */
    public static function getSpaceSeasonSubjectDetail(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $getStudentPreOrderInfo = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery","getStudentSpaceSeasonPreOrder");
        $gradeId = $getStudentPreOrderInfo['gradeId'];
        $getStudentPreOrders = $getStudentPreOrderInfo['preOrder'];
        $nextTermEnrollDetail = [];
        if(is_array($getStudentPreOrders[self::$studentUid]['subject'])){
            foreach ($getStudentPreOrders[self::$studentUid]['subject'] as $preSubject){
                $nextTermEnrollDetail[] = Zb_Const_GradeSubject::$GRADE[$gradeId].AssistantDesk_Common_Keyconst::getSubject()[$preSubject];
            }
        }

        self::$student['spaceSeasonSubjectDetail'] = $nextTermEnrollDetail ? implode(',',$nextTermEnrollDetail) : "";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'spaceSeasonSubjectDetail', [
            'title'  => '【隔季报名科目】',
            'source' => 'es：idl_season_student_info.subject',
        ]);
    }

    /**
     * 获取下一学季预约科目
     */
    public static function getNextTermPreOrderDetail(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $getStudentPreOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class,"get2022HanchunStudentPreOrder");
        $nextTermPreOrderDetail = [];
        if(is_array($getStudentPreOrders[self::$studentUid])){
            $subjects = [];
            foreach ($getStudentPreOrders[self::$studentUid] as $studentPreOrder){
                $subjects = is_array($studentPreOrder['preSubject']) ? array_merge($subjects, $studentPreOrder['preSubject']) : $subjects;
            }
            if (!empty($subjects)) {
                foreach ($subjects as $_subjectId) {
                    $nextTermPreOrderDetail[] = AssistantDesk_Common_Keyconst::getSubject()[$_subjectId];
                }
            }
        }

        self::$student['nextTermPreOrderDetail'] = $nextTermPreOrderDetail ? implode(',',$nextTermPreOrderDetail) : "";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'nextTermPreOrderDetail', [
            'title'=>'【下一学季预约科目】',
            'source' => 'idl_season_student_info：字段：preSubject',
            '解释'     => '预约课程详情'
        ]);
    }

//    /**
//     * 春3报名状态：学员在春3报名状态
//     */
//    public static function getChun3ApplyStatus() {
//        $esContinueData       = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['chun3ApplyStatus'] = intval($esContinueData[self::$studentUid]['hasYati']) ?: 0;
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'chun3ApplyStatus', [
//            'title'  => '【春3报名状态】',
//            'source' => 'es订单:has_yati',
//        ]);
//    }

//    /**
//     * 春1春3联报状态：学员在当前学科春1春3联报状态
//     */
//    public static function getChun1Chun3BoundStatus() {
//        $esContinueData       = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['chun1Chun3BoundStatus'] = intval($esContinueData[self::$studentUid]['hasLianbao']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'chun1Chun3BoundStatus', [
//            'title'  => '【春1春3联报状态】',
//            'source' => 'es订单:has_lianbao',
//        ]);
//    }

    /**
     * 总预约课程数
     * @throws ReflectionException
     */
    public static function getAllReserveCourseCnt()
    {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $allStudentPreOrders       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentPreOrder");

        self::$student['allReserveCourseCnt'] = $allStudentPreOrders[self::$studentUid]['allReserveCourseCnt'] ?? 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'allReserveCourseCnt', [
            'title'  => '【总预约课程数】',
            'source' => 'es:idl_season_student_info。字段：pre_subject_num（小学预约课程id枚举）个数加和',
            '解释' => 'pre_subject_num（小学预约课程id枚举）个数加和',
        ]);
    }

    /**
     * 总预约课程详情
     * @throws ReflectionException
     */
    public static function getAllReserveCourseDetail() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $allStudentPreOrders       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentPreOrder");

        self::$student['allReserveCourseDetail']     = $allStudentPreOrders[self::$studentUid]['allReserveCourseDetail'] ?? "";
        self::$student['allReserveCourseDetailInfo'] = $allStudentPreOrders[self::$studentUid]['allReserveCourseDetailInfo'] ?? "";

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'allReserveCourseDetail', [
            'title'  => '【总预约课程详情】',
            'source' => 'es:idl_season_student_info(学生预约表)',
            '解释'=>'获取到gradeId+subject，拼接得到allReserveCourseDetail',
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'allReserveCourseDetailInfo', [
            'title'  => '【总预约课程详情】',
            'source' => 'dal:courseName、onlineFormatTimeAll; dau:teacherName',
            '解释'=>'getAllReserveCourseDetail由courseName+onlineFormatTimeAll+teacherName拼接而成',
        ]);
    }

//    /**
//     * 本学科密训班的报名状态
//     */
//    public static function getHasYati() {
//        $esContinueData       = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['hasYati'] = intval($esContinueData[self::$studentUid]['hasYati']);
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'hasYati', [
//            'title'  => '【本学科密训班的报名状态】',
//            'source' => 'es续报：idl_trade_order_assistant.has_yati',
//            '解释'=>'has_yati含义是否报名押题课',
//        ]);
//    }
//
//    /**
//     * 密训班报名时间
//     */
//    public static function getYatiRegistrationTime() {
//        $esContinueData       = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['yatiRegistrationTime'] = !$esContinueData[self::$studentUid]['yatiRegistrationTime'] ? "" : date('Y-m-d H:i:s', $esContinueData[self::$studentUid]['yatiRegistrationTime']);
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'yatiRegistrationTime', [
//            'title'  => '【密训班报名时间】',
//            'source' => 'es订单:yati_registration_time',
//        ]);
//    }

    /**
     * 寒特惠预习提交状态
     */
    public static function getTehuikePreviewSubmit() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $courseInfo = $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $thkPreviewSubmitData = Common_Singleton::getInstanceData(
            AssistantDesk_Tehuike::class,
            "previewSubmit",
            [$courseInfo['mainGradeId'], AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]
        );

        self::$student['tehuikePreviewSubmit'] = $thkPreviewSubmitData[self::$studentUid] ?: 0;
    }

    /**
     * 寒特惠巩固练习提交状态
     */
    public static function getTehuikeHomeworkSubmit() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $thkHomeworkSubmitData = Common_Singleton::getInstanceData(
            AssistantDesk_Tehuike::class,
            "homeworkSubmit",
            [AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]
        );

        self::$student['tehuikeHomeworkSubmit'] = $thkHomeworkSubmitData[self::$studentUid] ?: 0;
    }

    /**
     * 计算每一个学生的当前学季当前课程类型报名的学科数量
     */
    public static function currentSeasonSingnSubjectNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);

        $year           = $courseInfo['year'];
        $season         = AssistantDesk_Season::$serviceSeasonNameMap[$courseInfo['season']];
        $type           = $courseInfo['courseType'];

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids]
        );

        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];

        self::$student['currentSeasonSingnSubjectNum'] = $studentSubjectNumArr[self::$studentUid] && is_array($studentSubjectNumArr[self::$studentUid]) ? count($studentSubjectNumArr[self::$studentUid]) : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'currentSeasonSingnSubjectNum', [
            'title'  => '【计算每一个学生的当前学季当前课程类型报名的学科数量】',
            'source' => '数据库表tblAssistantCourseStudent程序计算',
        ]);
    }

    /**
     * 计算每一个学生的当前学季当前课程类型报名的学科数量
     */
    public static function currentSeasonSignUpSubjectInfo(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);

        $year           = $courseInfo['year'];
        $season         = AssistantDesk_Season::$serviceSeasonNameMap[$courseInfo['season']];
        $type           = $courseInfo['courseType'];

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids]
        );
        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];
        $subjectList = $studentSubjectNumArr[self::$studentUid] ?? [];
        $subjectNameList = [];


        foreach ($subjectList as $subjectId) {
            $subjectName = AssistantDesk_Common_Keyconst::getSubject()[$subjectId] ?? '';
            if ($subjectName) {
                $subjectNameList[] = $subjectName;
            }
        }
        self::$student['currentSeasonSignUpSubjectInfo'] = implode(',', $subjectNameList);
        foreach($subjectNameList as &$v) {
            if ($v == '历史') {
                $v = '史';
            } else {
                $v = mb_substr($v, 0, 1, 'utf-8');
            }
        }
        unset($v);
        self::$student['currentSeasonSignUpSubjectInfoArr'] = $subjectNameList;
        self::$student['currentSeasonSignUpSubjectIdArr']   = array_values($subjectList);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'currentSeasonSignUpSubjectInfoArr', [
            'title'  => '【计算每一个学生的当前学季当前课程类型报名的学科数量】',
            'source' => '数据库表tblAssistantCourseStudent程序计算',
        ]);
    }

    /**
     * 根据tag 获取cid
     * @param $tag
     * @return int|string
     */
    public static function getCidByTag($tag) {
        $keyList        = self::getAllKeyList();
        $currentCid     = Api_Tag::CID_STUDENT;
        foreach ($keyList as $cid => $tags) {
            if (in_array($tag, $tags)) {
                $currentCid = $cid;
                break;
            }
        }
        return $currentCid;
    }

    public static function getAllKeyList() {
        return [
            // 学员维度
            Api_Tag::CID_STUDENT => [
                'tagGoHomeFrequency',
                'tagHolidayType',
                'tagStayType',
                'communicatePeopleType',
                'policymakerType',
                'playbackPlanType',
                'homeTimeType',
                'holidayTimeType',
            ],
            // 学科维度
            Api_Tag::CID_SUBJECT => [
                'tagTeachingVersion',
                'subjectScoreType',
                'goalScoreType',
            ],
            // 课程维度
            Api_Tag::CID_COURSE => [
                'livePlaybackType',
            ],
        ];
    }

    public static function getConfigMaps($key) {
        $configFields = AssistantDesk_Data_CommonArkConfig::getShowFieldRules();
        $keyList = self::getAllKeyList();
        $allKey = array_merge(...$keyList);
        if (!in_array($key, $allKey)) {
            return [];
        }
        foreach ($configFields as $config) {
            if ($key == ($config['key'] ?? '')) {
                return $config['filterMap'] ?? [];
            }
        }
        return [];
    }

    public static function getConfigCidTags($filterTags = [])
    {
        $configFields = AssistantDesk_Data_CommonArkConfig::getShowFieldRules();
        $keyList = self::getAllKeyList();

        $allKey = array_merge(...$keyList);

        $filterKeys = [];
        $keyMapConfig = [];
        foreach ($configFields as $config) {
            $key = $config['key'];
            if (in_array($key, $allKey)) {
                $filterKeys[] = $key;
                $thisMap = $config['filterMap'] ?? [];

                $keyMapConfig[$key] = array_keys($thisMap);
            }
        }
        if (is_array($filterTags) && !empty($filterTags)) {
            $filterKeys = array_intersect($filterKeys, $filterTags);
        }
        $returnMap  = [];
        foreach($keyList as $cid =>$tagList) {
            foreach($tagList as $tag) {
                if (in_array($tag, $filterKeys) && !empty($keyMapConfig[$tag]) && is_array($keyMapConfig[$tag])) {
                    empty($returnMap[$cid]) && $returnMap[$cid] = [];
                    $returnMap[$cid] = array_merge($returnMap[$cid], $keyMapConfig[$tag]);
                }
            }
        }


        return $returnMap;
    }


    /**
     * tag info
     * @return mixed
     * @throws ReflectionException
     */
    public static function getAllTagInfo() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $cidTags = self::getConfigCidTags();
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        // 课程id 换课程id
        $courseLessonInfo = self::initDalCourseLessonData();
        $subjectid = $courseLessonInfo['subjects'][0] ?? 0;
        $studentAllTags = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'getCidsTagInfo',
            [AssistantDesk_Data_CommonParams::$studentUids, $cidTags, $subjectid, $courseId]
        );

        return $studentAllTags;
    }

    public static function initDalCourseLessonData() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        self::$dalData = Common_Singleton::getInstanceData(
            Api_Dal::class,
            "getCourseLessonInfoByCourseIds",
            [
                AssistantDesk_Data_CommonParams::$courseId,
                ['courseId', 'subjects', 'year', 'learnSeason', 'mainGradeId'],
                ['lessonId', 'courseId', 'startTime', 'stopTime']
            ]
        );

        return self::$dalData;
    }

    /**
     * tag 信息获取
     * @param string $tag
     * @throws ReflectionException
     */
    public static function tagSingleInfoSet(string $tag) {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $studentAllTags = Common_Singleton::getInstanceData(
            AssistantDesk_Data_StudentListFormat::class,
            'getAllTagInfo',
            []
        );
        $thisMap        = self::getConfigMaps($tag);
        $currentCid     = self::getCidByTag($tag);
        $studentTags    = $studentAllTags[$currentCid][self::$studentUid] ?? [];
        $studentTagKeys = [];
        foreach ($thisMap as $key => $value) {
            if (!empty($studentTags[$key])) {
                $studentTagKeys[] = $key;
            }
        }
        self::$student[$tag] = $studentTagKeys;
    }

    /**
     * 学科教材版本
     */
    public static function tagTeachingVersion() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        self::tagSingleInfoSet('tagTeachingVersion');
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tagTeachingVersion', [
            'title'  => '【学科教材版本】',
            'source' => '批量从tag服务获取：/tag/api/tag/getbybatch',
        ]);
    }

    /**
     * @throws ReflectionException
     * 地区
     */
    public static function getUserProvinceCity()
    {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $studentAllTags                = Common_Singleton::getInstanceData(
            Api_UserPortrait::class,
            'getUsersProvinceCity',
            [AssistantDesk_Data_CommonParams::$studentUids]
        );
        $studentProvinceCity           = $studentAllTags[self::$studentUid] ?? [];
        $province                      = $studentProvinceCity['province_traffic'] ?? '';
        $city                          = $studentProvinceCity['city_traffic'] ?? '';
        self::$student['provinceCity'] = $province . $city;
    }

    /**
     * 获取章节对应的观看状态
     * @param $lessonNum
     */
    public static function watchStatusLesson($lessonNum) {
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["studentUid", "lessonId", 'attendDuration', 'inclass_teacher_room_total_playback_time_v1', 'playbackTotalTime', 'playbackTimeIn7d'])) {
            return true;
        }
        list($lessonStudentData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");

        $courseLessonInfo = self::initDalCourseLessonData();
        $lessonList = $courseLessonInfo['lessonList'];
        if (empty($lessonList)) {
            return [];
        }
        $lessonList = Tools_Array::sortByMultiCols($lessonList, ['startTime' => SORT_ASC]);
        $lessonStartTimeMap = [];
        $lessonIds = [];
        foreach ($lessonList as $lessonInfo) {
            $lessonStartTimeMap[$lessonInfo['lessonId']] = $lessonInfo['startTime'];
            $lessonIds[] = $lessonInfo['lessonId'];
        }


        $currentLessonId = $lessonIds[$lessonNum - 1] ?? 0;
        $currentRecord = $lessonStudentData[$currentLessonId][self::$studentUid] ?? [];
        $hasWatch = self::WATCH_STATUS_LESSON_PENDING;
        if (!$currentLessonId) {
            // 无章节信息
        } elseif ($lessonStartTimeMap[$currentLessonId] > time()) {
            // 章节未开始
            $hasWatch = self::WATCH_STATUS_LESSON_PENDING;
        } else {
            // 高中
            if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseLessonInfo['mainGradeId']]) {
                if ($currentRecord['attendDuration'] >= 1800 || $currentRecord['inclass_teacher_room_total_playback_time_v1'] >= 1800) {
                    $hasWatch = self::WATCH_STATUS_LESSON_YES;
                } else {
                    $hasWatch = self::WATCH_STATUS_LESSON_NO;
                }
            } else {
                // 其他
                if ($currentRecord['attendDuration'] >= 1800 || $currentRecord['playbackTimeIn7d'] >= 1800) {
                    $hasWatch = self::WATCH_STATUS_LESSON_YES;
                } else {
                    $hasWatch = self::WATCH_STATUS_LESSON_NO;
                }
            }
        }

        self::$student["watchStatusLesson{$lessonNum}"] = $hasWatch;


        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "watchStatusLesson{$lessonNum}", [
            'title'  => '【第n章观看状态】',
            'source' => 'es：idl_assistant_lesson_student_action.inclass_teacher_room_total_playback_time_v1、playback_time_in_7d和attend_duration；Dal获取章节开始时间startTime',
            '解释'     => '枚举值：章节未开始、章节未观看、章节已观看。未到章节开始时间则章节未开始；已到章节开始时间时；高中：attend_duration>30分钟且inclass_teacher_room_total_playback_time_v1>30分钟则已观看否则未观看；已到章节开始时间时；其他：attend_duration>30分钟且playback_time_in_7d>30分钟则已观看否则未观看',
        ]);
    }

    /**
     * 章节观看数量
     * @return array
     * @throws ReflectionException
     */
    public static function watchLessonCnt() {
        if (\AssistantDesk_Data_DataSource::beforeAddCLuFields(["studentUid", "watchLessonCntHigh", 'watchLessonCntOther',])) {
            return true;
        }
        $courseLessonInfo = self::initDalCourseLessonData();
        $lessonList       = $courseLessonInfo['lessonList'];
        if (empty($lessonList)) {
            return [];
        }
        $lessonList         = Tools_Array::sortByMultiCols($lessonList, ['startTime' => SORT_ASC]);
        $lessonIds          = [];
        foreach ($lessonList as $lessonInfo) {
            $lessonIds[]                                 = $lessonInfo['lessonId'];
        }

        list($allStudentLessonData, $lessonStudentAggData)   = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAllStudentLessonData");

        if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseLessonInfo['mainGradeId']]) {
            $watchCnt = $lessonStudentAggData[self::$studentUid]['watchLessonCntHigh'];
        } else {
            // 其他
            $watchCnt = $lessonStudentAggData[self::$studentUid]['watchLessonCntOther'];
        }
        self::$student["watchLessonCnt"] = $watchCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'watchLessonCnt', [
            'title'  => '【章节观看数量】',
            'source' => 'es：idl_assistant_lesson_student_action.playback_time、playback_time_in_7d和attend_duration',
            '解释'=>'高中：attend_duration>30分钟且playback_time>30分钟则数量加一；其他：attend_duration>30分钟且playback_time_in_7d>30分钟则数量加一',
        ]);
    }

    public static function getStudentLessonEsData($courseId, $studentUids, $fields = []) {
        if (!$courseId || !$studentUids) {
            return [];
        }

        if (!$fields) {
            $fields = ["courseId"];
        }
        $fields = array_unique(array_merge($fields, ['tradeStatus']));
        $luList = \Assistant_Common_Service_DataService_Query_LessonStudentData::getListByCourseIdsStudentUids([$courseId], $studentUids, $fields);
        if (false === $luList) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '获取章节观看状态数据异常!', [[$courseId], $studentUids, $fields]);
        }
        foreach($luList as $k => $v) {
            if($v['tradeStatus'] != 1){
                unset($luList[$k]);
            }
        }
        $mixedRet = array_values($luList);

        $lessonStudentData = [];
        foreach ($mixedRet as $record) {
            $thisLessonId                                      = $record['lessonId'];
            $thisStudentUid                                    = $record['studentUid'];
            $lessonStudentData[$thisLessonId][$thisStudentUid] = $record;
        }
        return $lessonStudentData;
    }

    /**
     * 第1章观看状态
     */
    public static function watchStatusLesson1() {
        self::watchStatusLesson(1);
    }

    /**
     * 第2章观看状态
     */
    public static function watchStatusLesson2() {
        self::watchStatusLesson(2);
    }

    /**
     * 第3章观看状态
     */
    public static function watchStatusLesson3() {
        self::watchStatusLesson(3);
    }

    /**
     * 第4章观看状态
     */
    public static function watchStatusLesson4() {
        self::watchStatusLesson(4);
    }

    /**
     * 第5章观看状态
     */
    public static function watchStatusLesson5() {
        self::watchStatusLesson(5);
    }

    /**
     * 第6章观看状态
     */
    public static function watchStatusLesson6() {
        self::watchStatusLesson(6);
    }

    /**
     * 第7章观看状态
     */
    public static function watchStatusLesson7() {
        self::watchStatusLesson(7);
    }

    /**
     * 第8章观看状态
     */
    public static function watchStatusLesson8() {
        self::watchStatusLesson(8);
    }

    /**
     * 第9章观看状态
     */
    public static function watchStatusLesson9() {
        self::watchStatusLesson(9);
    }

    /**
     * 第10章观看状态
     */
    public static function watchStatusLesson10() {
        self::watchStatusLesson(10);
    }

    /**
     * 第11章观看状态
     */
    public static function watchStatusLesson11() {
        self::watchStatusLesson(11);
    }

    /**
     * 第12章观看状态
     */
    public static function watchStatusLesson12() {
        self::watchStatusLesson(12);
    }

    /**
     * 第13章观看状态
     */
    public static function watchStatusLesson13() {
        self::watchStatusLesson(13);
    }

    /**
     * 第14章观看状态
     */
    public static function watchStatusLesson14() {
        self::watchStatusLesson(14);
    }

    /**
     * 第15章观看状态
     */
    public static function watchStatusLesson15() {
        self::watchStatusLesson(15);
    }

    /**
     * 第16章观看状态
     */
    public static function watchStatusLesson16() {
        self::watchStatusLesson(16);
    }

    /**
     * 第17章观看状态
     */
    public static function watchStatusLesson17() {
        self::watchStatusLesson(17);
    }

    /**
     * 第18章观看状态
     */
    public static function watchStatusLesson18() {
        self::watchStatusLesson(18);
    }

    /**
     * 第19章观看状态
     */
    public static function watchStatusLesson19() {
        self::watchStatusLesson(19);
    }

    /**
     * 第20章观看状态
     */
    public static function watchStatusLesson20() {
        self::watchStatusLesson(20);
    }

    public static function watchStatusLessonNum() {
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $lessonNum = intval($fieldRule['serviceConfig']['lessonNum'] ?? 0);
        self::watchStatusLesson($lessonNum);
    }
    /**
     *沟通人
     */
    public static function communicatePeopleType() {
        self::tagSingleInfoSet('communicatePeopleType');
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'communicatePeopleType', [
            'title'  => '【沟通人，学科成绩，目标成绩，报名决策人，直播/回放，回放规划，放学时间，放假时间，走读回家频率】',
            'source' => '批量从tag服务获取：/tag/api/tag/getbybatch',
        ]);
    }

    /**
     *学科成绩
     */
    public static function subjectScoreType() {
        self::tagSingleInfoSet('subjectScoreType');
    }

    /**
     *目标成绩
     */
    public static function goalScoreType() {
        self::tagSingleInfoSet('goalScoreType');
    }

    /**
     *报名决策人
     */
    public static function policymakerType() {
        self::tagSingleInfoSet('policymakerType');
    }

    /**
     *直播/回放
     */
    public static function livePlaybackType() {
        self::tagSingleInfoSet('livePlaybackType');
    }

    /**
     *回放规划
     */
    public static function playbackPlanType() {
        self::tagSingleInfoSet('playbackPlanType');
    }

    /**
     *放学时间
     */
    public static function homeTimeType() {
        self::tagSingleInfoSet('homeTimeType');
    }

    /**
     *放假时间
     */
    public static function holidayTimeType() {
        self::tagSingleInfoSet('holidayTimeType');
    }

    /**
     * 走读回家频率
     */
    public static function tagGoHomeFrequency()
    {
        self::tagSingleInfoSet('tagGoHomeFrequency');
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tagGoHomeFrequency', [
            'title'  => '【走读回家频率】',
            'source' => '批量从tag服务获取：/tag/api/tag/getbybatch',
        ]);
    }

    /**
     * 走读住校情况
     */
    public static function tagStayType() {
        self::tagSingleInfoSet('tagStayType');
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tagStayType', [
            'title'  => '【走读住校情况，休假时间（高中）】',
            'source' => '批量从tag服务获取：/tag/api/tag/getbybatch',
        ]);
    }

    /**
     * 休假时间（高中）
     */
    public static function tagHolidayType() {
        self::tagSingleInfoSet('tagHolidayType');
    }

    /**
     * 本活动可扩科目
     */
    public static function getActivityExpendSubject() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $activityExpendData = Common_Singleton::getInstanceData(AssistantDesk_Kuoke::class, "activityRecruitData", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$assistantUid]);

        self::$student['activityExpendSubject'] = $activityExpendData[self::$studentUid]['activityExpendSubject'] ?: '';
    }

    /**
     * 本活动报名专题课科目
     */
    public static function getActivityEnrollSubject() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $activityExpendData = Common_Singleton::getInstanceData(AssistantDesk_Kuoke::class, "activityRecruitData", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$assistantUid]);

        self::$student['activityEnrollSubject'] = $activityExpendData[self::$studentUid]['activityEnrollSubject'] ?: '';
    }

    /**
     * 口算APP作业相关数据计算 -- 打卡状态，打卡次数，总打卡次数，批改状态，评价状态，
     */
    public static function getKsTaskData() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $studentCourseTaskData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentCourseTaskFromEs");


        $studentUid = self::$studentUid;

        if (isset($studentCourseTaskData[$studentUid])) {
            self::$student['ksDakaStatus']       = $studentCourseTaskData[$studentUid]['isSubmit'];
            self::$student['ksCount']            = $studentCourseTaskData[$studentUid]['ksCount'];
            self::$student['ksCorrectStatus']    = $studentCourseTaskData[$studentUid]['isCorrect'];
            self::$student['ksEvaluationStatus'] = $studentCourseTaskData[$studentUid]['isComment'];
            self::$student['ksTotalCount']       = $studentCourseTaskData[$studentUid]['ksTotalCount'];
            self::$student['ksDakaRate']         = $studentCourseTaskData[$studentUid]['ksTotalCount'] > 0 ? round($studentCourseTaskData[$studentUid]['ksCount'] / $studentCourseTaskData[$studentUid]['ksTotalCount'], 3) * 100 . "%" : 0;
        } else {
            self::$student['ksDakaStatus']       = -1;
            self::$student['ksCount']            = "";
            self::$student['ksCorrectStatus']    = -1;
            self::$student['ksEvaluationStatus'] = -1;
            self::$student['ksTotalCount']       = "";
            self::$student['ksDakaRate']         = "";
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'ksDakaStatus', [
            'title'  => '【打卡状态，打卡次数，总打卡次数，批改状态，评价状态】',
            'source' => '一份数据取自es：bdl_oral_count_detail,字段：is_correct,is_submit,is_comment',
            '解释'     => '此部分字段逻辑较复杂，多个数据源获取数据且有字段，一部分数据取自任务接口：/parenthomework/assistantdesk/getfatherhomeworklist'
        ]);
    }

    /**
     *获取高光时刻
     */
    public static function getHighlightInfo() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $hightLightData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getHighlightInfo");
        $studentUid     = self::$studentUid;

        if (!empty($hightLightData[self::$studentUid])) {
            self::$student['highlightInfo'] = [
                'highlightStatus' => intval($hightLightData[$studentUid]['status']),
                'highlightImg'    => 'https://bj.bcebos.com/duxue-sop/gaoguanghaibao_606beb96.jpg',
                'highlightLink'   => $hightLightData[$studentUid]['visitUrl']];
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'highlightInfo', [
            'title'  => '【获取高光时刻】',
            'source' => '接口/duxuesc/scenes/api/gethighlightsstudentsdata',
        ]);
    }

//    /**
//     * 押题课春1 同学科报名状态
//     */
//    public static function getSpring1SameSubjectStatus() {
//        $esContinueData                            = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//        self::$student['spring1SameSubjectStatus'] = intval($esContinueData[self::$studentUid]['is13SameSubject']);
//    }

    /**
     * 章节课前完课率（出境）
     */
    public static function getPreclassFinishAttendLessonRate() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['preclassAttendNum', 'preclassFinishAttendLessonNum'])) {
            return true;
        }
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        self::$student['preclassFinishAttendLessonRate'] = empty($userDelaminationDatas[self::$studentUid]['preclassAttendNum']) ? 0 : round($userDelaminationDatas[self::$studentUid]['preclassFinishAttendLessonNum'] / $userDelaminationDatas[self::$studentUid]['preclassAttendNum'], 4) * 100 . '%';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'preclassFinishAttendLessonRate', [
            'title' => '【章节课前完课率（出境）】',
            '数据源'   => 'es：idl_user_delamination_alias.preclass_attend_num和preclass_finish_attend_lesson_num',
            '解释'=>'preclass_finish_attend_lesson_num/preclass_attend_num',
        ]);
    }

    /**
     * 学员分层--自定义
     */
    public static function getUserDelaminationCommon() {
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        if (empty($fieldRule['serviceConfig'])) {
            return;
        }
        $allFields = [];
        //有分子和分母的展示率
        $numeratorArr   = [];
        $denominatorArr = [];
        $data           = [];
        if (isset($fieldRule['serviceConfig']['numerator']) && isset($fieldRule['serviceConfig']['denominator'])) {
            $numerator      = $fieldRule['serviceConfig']['numerator'];
            $denominator    = $fieldRule['serviceConfig']['denominator'];
            $numeratorArr   = explode('+', $numerator);
            $denominatorArr = explode('+', $denominator);
            if (empty($numeratorArr) || empty($denominatorArr)) {
                return;
            }
            $allFields = array_merge($allFields, $numeratorArr);
            $allFields = array_merge($allFields, $denominatorArr);

        }
        if (isset($fieldRule['serviceConfig']['data'])) {
            $data        = $fieldRule['serviceConfig']['data'];
            $allFields[] = $data;
        }
        if (empty($allFields)) {
            return;
        }
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields($allFields)) {
            return true;
        }

        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        $fetchData = '';
        if (!empty($data)) {
            $fetchData = $userDelaminationDatas[self::$studentUid][$data] ?? 0;
        }
        $denominatorVal = 0;
        if (!empty($denominatorArr)) {
            foreach ($denominatorArr as $v) {
                $tmp            = isset($userDelaminationDatas[self::$studentUid][$v]) ? $userDelaminationDatas[self::$studentUid][$v] : 0;
                $denominatorVal += $tmp;
            }
        }

        $numeratorVal = 0;
        if (!empty($numeratorArr)) {
            foreach ($numeratorArr as $v) {
                $tmp          = isset($userDelaminationDatas[self::$studentUid][$v]) ? $userDelaminationDatas[self::$studentUid][$v] : 0;
                $numeratorVal += $tmp;
            }
        }

        $returnKey = $fieldRule['key'];


        $outputData = 0;
        if (isset($fieldRule['serviceConfig']['data'])) {
            $outputData = $fetchData;
        } else {
            $outputData = $denominatorVal == 0 ? 0 : round($numeratorVal / $denominatorVal, 4) * 100 . '%';
        }
        self::$student[$returnKey] = $outputData;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'preclassFinishAttendLessonRate', [
            'title' => '【学员分层自定义key：' . $returnKey . '】',
            '数据源'   => 'es：idl_user_delamination_alias，具体字段在方舟serviceConfig中配置',
        ]);
    }

    /**
     * 近7天天鹰外呼有效沟通次数（60秒）
     */
    public static function getRecent7DaysTianyingValidChatNum() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['recent7DaysTianyingValidChatNum',])) {
            return true;
        }
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        self::$student['recent7DaysTianyingValidChatNum'] = $userDelaminationDatas[self::$studentUid]['recent7DaysTianyingValidChatNum'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'recent7DaysTianyingValidChatNum', [
            'title' => '【近7天天鹰外呼有效沟通次数（60秒）】',
            '数据源'   => 'es：idl_user_delamination_alias.recent_7_days_tianying_valid_chat_num',
        ]);
    }

    /**
     * 获取省份
     */
    public static function getProvinceName() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['provinceName'])) {
            return true;
        }
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        self::$student['provinceName'] = $userDelaminationDatas[self::$studentUid]['provinceName'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'provinceName', [
            'title' => '【获取省份】',
            '数据源'   => 'es：idl_user_delamination_alias.province_name',
        ]);
    }

    /**
     * 获取单报或联报
     */
    public static function getIsBoundDiscount() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['isBoundDiscount'])) {
            return true;
        }
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        self::$student['isBoundDiscount'] = $userDelaminationDatas[self::$studentUid]['isBoundDiscount'];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isBoundDiscount', [
            'title'=>'【联报状态】',
            'source' => 'es订单：字段：isBoundDiscountRetain或者hasPurchaseQiu',
            '解释'     => '续报且联报或者一秋+三暑报名'
        ]);

    }

    /**
     * 预习题目正确率
     */
    public static function getPreviewExamRightRate() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['previewExamFinishNum', 'previewExamRightNum'])) {
            return true;
        }
        $userDelaminationDatas = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");

        self::$student['previewExamRightRate'] = empty($userDelaminationDatas[self::$studentUid]['previewExamFinishNum']) ? 0 : round($userDelaminationDatas[self::$studentUid]['previewExamRightNum'] / $userDelaminationDatas[self::$studentUid]['previewExamFinishNum'], 4) * 100 . '%';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'previewExamRightRate', [
            'title'=>'【预习题目正确率】',
            'source' => 'es：idl_user_delamination_alias.preview_exam_right_num和preview_exam_finish_num',
            '解释'     => 'preview_exam_right_num/preview_exam_finish_num'
        ]);
    }

    /**
     * 城市级别
     */
    public static function getCityLevel() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['cityLevel',])) {
            return true;
        }
        $userDelaminationDatas      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");
        self::$student['cityLevel'] = $userDelaminationDatas[self::$studentUid]['cityLevel'];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'cityLevel', [
            'title'=>'【城市级别】',
            'source' => 'es：idl_user_delamination_alias.city_level',
        ]);
    }

    /**
     * 班课连续续报学季数
     */
    public static function getSeriseSeason() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['seriseSeason'])) {
            return true;
        }
        $userDelaminationDatas         = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");
        self::$student['seriseSeason'] = $userDelaminationDatas[self::$studentUid]['seriseSeason'];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'seriseSeason', [
            'title'=>'【班课连续续报学季数】',
            'esSource' => 'idl_user_delamination_alias 字段：seriseSeason'
        ]);

    }


    /**
     * 是否为原班老生
     */
    public static function getIsOriginStudent() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuSaveTimeFields(['isOriginStudent',])) {
            return true;
        }
        $userDelaminationDatas            = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAcsDataFromIDLUserDelaminationDetail");
        self::$student['isOriginStudent'] = $userDelaminationDatas[self::$studentUid]['isOriginStudent'];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isOriginStudent', [
            'title'=>'【是否为原班老生】',
            'esSource' => 'idl_user_delamination_alias 字段：isOriginStudent'
        ]);
    }


//    /**
//     * 押题课 暑同学科报名状态
//     */
//    public static function getSummerSameSubjectStatus() {
//        $esContinueData                           = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//        self::$student['summerSameSubjectStatus'] = $esContinueData[self::$studentUid]['is13Retain'] == 1 && $esContinueData[self::$studentUid]['is13BoundRetain'] == 0 ? 1 : 0;
//    }

//    /**
//     * 押题课 秋同学科报名状态
//     */
//    public static function getAutumnSameSubjectStatus() {
//        $esContinueData                           = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//        self::$student['autumnSameSubjectStatus'] = intval($esContinueData[self::$studentUid]['is13Retain'] == 1 && $esContinueData[self::$studentUid]['is13BoundRetain'] == 1);
//    }

    /**
     * 获取续报归属人信息
     */
    public static function getFirstPayAssistant() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "firstPayAssistant",
        ])) {
            return [];
        }
        $continueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['firstPayAssistant'] = $continueData[self::$studentUid]['firstPayAssistant'] ? $continueData[self::$studentUid]['firstPayAssistant'] : '';
    }

    /**
     * 处理转介绍数据
     */
    public static function getUnconvertedNum() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $unconvertedList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getUnconvertedList");
        $unconvertedList = is_array($unconvertedList) && $unconvertedList ? $unconvertedList : [];
        self::$student['unconvertedNum'] = $unconvertedList[self::$studentUid] ? count($unconvertedList[self::$studentUid]) : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'unconvertedNum', [
            'title' => '【未转化数量】',
            '数据源'   => '分销接口：http://yapi.zuoyebang.cc/project/355/interface/api/176927',
        ]);
    }
    /**
     * 处理转介绍数据
     */
    public static function getUnconvertedUserNum() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $unconvertedList                 = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getActivityStudentData", [
            AssistantDesk_Data_CommonParams::$taskId,
            AssistantDesk_Data_CommonParams::$studentUids,
            ["inviteeUtransNum"]
        ]);
        $unconvertedList                 = is_array($unconvertedList) && $unconvertedList ? $unconvertedList : [];
        self::$student['unconvertedUserNum'] = intval($unconvertedList[self::$studentUid]['inviteeUtransNum'] ?? 0);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'unconvertedUserNum', [
            'title' => '【未转化新用户uv】',
            '数据源'   => 'es adl_referral_activity_bind_relation.invitee_utrans_num ：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=332106585',
        ]);
    }

    /**
     * 处理转介绍绑定状态
     */
    public static function getUnconvertedBindStatus() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $unconvertedList = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getUnconvertedList");
        $unconvertedList = is_array($unconvertedList) && $unconvertedList ? $unconvertedList : [];
        if (!isset($unconvertedList[self::$studentUid][0]['recruiterUid'])) {
            self::$student['unconvertedBindStatus'] = 3;//未绑定
        } else if ($unconvertedList[self::$studentUid][0]['recruiterUid'] == AssistantDesk_Data_CommonParams::$assistantUid) {
            self::$student['unconvertedBindStatus'] = 1;//已绑定
        } else {
            self::$student['unconvertedBindStatus'] = 2;//已被其他老师绑定

        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'unconvertedBindStatus', [
            'title' => '【绑定状态】',
            '数据源'   => '分销接口：http://yapi.zuoyebang.cc/project/355/interface/api/176927',
        ]);
    }

//    /**
//     * 是否报秋
//     */
//    public static function getIsSubjectQiu() {
//        $esContinueData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");
//
//        self::$student['isSubjectQiu'] = ((isset($esContinueData[self::$studentUid]['isBoundDiscountRetain']) && ($esContinueData[self::$studentUid]['isBoundDiscountRetain'] > 0)) || (isset($esContinueData[self::$studentUid]['isSingleQiu']) && ($esContinueData[self::$studentUid]['isSingleQiu'] > 0)) || (isset($esContinueData[self::$studentUid]['isDoubleShuQiu']) && ($esContinueData[self::$studentUid]['isDoubleShuQiu'] > 0))) ? 1 : 0;
//
//        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isSubjectQiu', [
//            'title'   => '【是否报秋】',
//            '数据源'   => 'es：idl_trade_order_assistant.is_bound_discount_retain、is_single_qiu、is_double_shu_qiu',
//            '解释'    =>'is_bound_discount_retain或is_single_qiu或is_double_shu_qiu大于0，则值为1，否则为0',
//        ]);
//    }

    /**
     * 是否报暑
     */
    public static function getIsSubjectShu() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "isL2r",
        ])) {
            return [];
        }
        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['isSubjectShu'] = isset($esContinueData[self::$studentUid]['isL2r']) && ($esContinueData[self::$studentUid]['isL2r'] > 0) ? 1 : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isSubjectShu', [
            'title'   => '【是否报暑】',
            '数据源'   => 'es：idl_trade_order_assistant.is_l2r',
            '解释'    =>'is_l2r大于0，则值为1，否则为0',
        ]);
    }

    /**
     * 是否小鹿写字
     */
    public static function getIssDeerWriting() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "issDeerWriting",
        ])) {
            return [];
        }
        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['issDeerWriting'] = isset($esContinueData[self::$studentUid]['issDeerWriting']) ? $esContinueData[self::$studentUid]['issDeerWriting'] : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'issDeerWriting', [
            'title'   => '【是否报名过小鹿写字】',
            '数据源'   => 'es：idl_trade_order_assistant.iss_deer_writing',
        ]);
    }

    /**
     * 是否小鹿编程
     */
    public static function getIssDeerProgramming() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "issDeerProgramming",
        ])) {
            return [];
        }
        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['issDeerProgramming'] = isset($esContinueData[self::$studentUid]['issDeerProgramming']) ? $esContinueData[self::$studentUid]['issDeerProgramming'] : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'issDeerProgramming', [
            'title'   => '【是否报名过小鹿编程】',
            '数据源'   => 'es：idl_trade_order_assistant.iss_deer_programming',
        ]);
    }

    /**
     * 报名小鹿编程时间
     */
    public static function getEnrollDeerProgrammingLastTime() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "enrollDeerProgrammingLastTime",
        ])) {
            return [];
        }
        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['enrollDeerProgrammingLastTime'] = !empty($esContinueData[self::$studentUid]['enrollDeerProgrammingLastTime']) ? date("Y-m-d H:i:s", $esContinueData[self::$studentUid]['enrollDeerProgrammingLastTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'enrollDeerProgrammingLastTime', [
            'title'   => '【报名小鹿编程时间】',
            '数据源'   => 'es：idl_trade_order_assistant.enroll_deer_programming_last_time',
        ]);
    }

    /**
     * 报名小鹿写字时间
     */
    public static function getEnrollDeerWritingLastTime() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "enrollDeerWritingLastTime",
        ])) {
            return [];
        }
        $esContinueData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");

        self::$student['enrollDeerWritingLastTime'] = !empty($esContinueData[self::$studentUid]['enrollDeerWritingLastTime']) ? date("Y-m-d H:i:s", $esContinueData[self::$studentUid]['enrollDeerWritingLastTime']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'enrollDeerWritingLastTime', [
            'title'   => '【报名小鹿写字时间】',
            '数据源'   => 'es：idl_trade_order_assistant.enroll_deer_writing_last_time',
        ]);
    }

    /**
     * 21暑学员带来新例子pv数(非双算)
     */
    public static function getActualNewStudentPv() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $fenxiaoDetail = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getFenXiaoData21ShuDetail");

        self::$student['actualNewStudentPv'] = isset($fenxiaoDetail[self::$studentUid]['actualNewStudentPv']) ? $fenxiaoDetail[self::$studentUid]['actualNewStudentPv'] : 0;
    }


    /**
     * 获取自定义数据信息
     */
    public static function getCustomData() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        //同一学生字段只初始化一次
        if (self::$student[AssistantDesk_Data_CustomFieldData::CUSTOM_FIELD_INIT_KEY]) {
            return;
        }
        $customData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCustomFieldData");

        //学生数据
        $studentData = empty($customData['customData'][self::$studentUid]) ? [] : $customData['customData'][self::$studentUid];
        $studentData = array_column($studentData, null, 'fieldId');

        //字段元数据
        $fieldInfos = empty($customData['fieldInfos']) ? [] : $customData['fieldInfos'];
        foreach ($fieldInfos as $v) {
            //单选返回空串，多选返回空数组
            $emptyValue                                                                                                     = $v['optionType'] == 1 ? '' : [];
            self::$student[AssistantDesk_Data_CustomFieldData::CUSTOM_FIELD_KEY_PREFIX . $v['id'] . '_' . $v['optionType']] = isset($studentData[$v['id']]['value']) ? @json_decode($studentData[$v['id']]['value'], true) : $emptyValue;
        }
        self::$student[AssistantDesk_Data_CustomFieldData::CUSTOM_FIELD_INIT_KEY] = 1;
    }

    /**
     * 获取观看完课状态
     */
    public static function getAttendFinishStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "isViewFinished"])) {
            return true;
        }
        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['attendFinishStatus'] = (isset($studentLessonData[self::$studentUid]['isViewFinished']) && ($studentLessonData[self::$studentUid]['isViewFinished'] > 0)) ? 1 : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendFinishStatus', [
            'title' => '【观看完课状态】',
            '数据源'   => 'es.lu.is_view_finished',
        ]);
    }

    /**
     * 初中获取观看完课状态
     */
    public static function getAttendFinishStatusJunior() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "isViewFinishIn14d"])) {
            return true;
        }
        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['attendFinishStatusJunior'] = (isset($studentLessonData[self::$studentUid]['isViewFinishIn14d']) && ($studentLessonData[self::$studentUid]['isViewFinishIn14d'] > 0)) ? 1 : 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendFinishStatusJunior', [
            'title' => '【初中观看完课状态】',
            '数据源'   => 'es.lu.is_view_finish_in_14d',
        ]);
    }

    /**
     * 小灶课到课状态（>1/3）
     */
    public static function getIsAssistantcourseAttend() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "isAssistantcourseAttend"])) {
            return true;
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['isAssistantcourseAttend'] = $studentLessonData[self::$studentUid]['isAssistantcourseAttend'] ?? 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isAssistantcourseAttend', [
            'title' => '【小灶课到课状态（>1/3）】',
            '数据源'   => 'es.lu.is_assistantcourse_attend',
        ]);
    }

    /**
     * 小灶课完课状态（>2/3)
     */
    public static function getIsAssistantcourseFinish() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "isAssistantcourseFinish"])) {
            return true;
        }
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");

        self::$student['isAssistantcourseFinish'] = $studentLessonData[self::$studentUid]['isAssistantcourseFinish'] ?? 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isAssistantcourseFinish', [
            'title' => '【小灶课完课状态（>2/3)】',
            '数据源'   => 'es.lu.idl_assistant_lesson_student_action.is_assistantcourse_finish',
        ]);
    }

    /**
     * 观看状态（30min）
     */
    public static function getWatchStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "attendDuration", "playbackTimeIn7d", "playbackTotalTime"])
            && \AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid", "inclass_teacher_room_attend_duration", "inclass_teacher_room_total_playback_time_v1"])) {
            return true;
        }
        $courseInfo         = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esLessonData       = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $esCommonLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        $attend = 0;
        if ($esCommonLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] && $esCommonLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] < 1800) {
            $attend = 1;
        }
        //初高和小学回放不同
        if (in_array(Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']], [Zb_Const_GradeSubject::GRADE_STAGE_SENIOR, Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR])) {
            if ($esCommonLessonData[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'] && $esCommonLessonData[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'] < 1800) {
                $attend = 1;
            }
        } else {
            if ($esLessonData[self::$studentUid]['playbackTimeIn7d'] && $esLessonData[self::$studentUid]['playbackTimeIn7d'] < 1800) {
                $attend = 1;
            }
        }

        if ($esCommonLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] >= 1800) {
            $attend = 2;
        }
        //初高和小学回放不同
        if (in_array(Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']], [Zb_Const_GradeSubject::GRADE_STAGE_SENIOR, Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR])) {
            if ($esCommonLessonData[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'] >= 1800) {
                $attend = 2;
            }
        } else {
            if ($esLessonData[self::$studentUid]['playbackTimeIn7d'] >= 1800) {
                $attend = 2;
            }
        }

        self::$student['watchStatus'] = $attend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'watchStatus', [
            'title' => '【观看状态（30min）】',
            '数据源'   => 'es.commonlu：直播：inclass_teacher_room_attend_duration，初高回放：inclass_teacher_room_total_playback_time_v1、小学回放:playback_time_in_7d',
        ]);
    }

    /**
     * 观看状态（30min）
     */
    public static function getWatchOnCourseStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields(["studentUid", "attendDuration", "playbackTimeIn7d", "playbackTotalTime"])) {
            return true;
        }
        $courseInfo   = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $strPlaybackTimeField = 'playbackTimeIn7d';
        // 高中改用累计时长
        if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $strPlaybackTimeField = 'playbackTotalTime';
        }
        $attend = 0;
        if($esLessonData[self::$studentUid]['attendDuration'] && $esLessonData[self::$studentUid]['attendDuration'] < 1800){
            $attend = 0;
        }
        if($esLessonData[self::$studentUid][$strPlaybackTimeField] && $esLessonData[self::$studentUid][$strPlaybackTimeField] < 1800){
            $attend = 0;
        }
        if($esLessonData[self::$studentUid]['attendDuration'] >= 1800){
            $attend = 1;
        }
        if($esLessonData[self::$studentUid][$strPlaybackTimeField] >= 1800){
            $attend = 1;
        }
        self::$student['watchOnCourseStatus'] = $attend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'watchOnCourseStatus', [
            'title' => '【是否观看到课（观看时长≥30min）】', //
            '数据源'   => 'es.lu.idl_assistant_lesson_student_action.attend_duration和playback_time、playback_time_in_7d',
        ]);
    }

    /**
     * 获取是否同年级同学科预约
     */
    public static function getIsSameGradeSubjectPreOrder() {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([
            "preStatus",
            "pre_status_autume_spring",
        ])) {
            return [];
        }
        $esContinueData                              = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsContinueData");
        $preStatus                                   = isset($esContinueData[self::$studentUid]['preStatus']) ? $esContinueData[self::$studentUid]['preStatus'] : 0;
        $preStatusAutumeSpring                       = isset($esContinueData[self::$studentUid]['pre_status_autume_spring']) ? $esContinueData[self::$studentUid]['pre_status_autume_spring'] : 0;
        self::$student['isSameGradeSubjectPreOrder'] = ($preStatus || $preStatusAutumeSpring) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isSameGradeSubjectPreOrder', [
            'title'  => '【是否同年级同学科预约】',
            'source' => 'es订单：字段：preStatus或者pre_status_autume_spring',
            '解释'     => 'https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=307308690'
        ]);
    }

    /**
     * 获取预约状态
     */
    public static function getTradeOrderReservationStatus() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $tradeOrderReservationData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTradeOrderReservation");
        self::$student['tradeorderreservationstatus'] = isset($tradeOrderReservationData[self::$studentUid]) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tradeorderreservationstatus', [
            'title'=>'【是否预约过】',
            'source' => 'es预约单索引idl_trade_order_reservation',
            '解释'    => '是否存在该学生的预约记录'
        ]);
    }

    /**
     * 获取首次预约时间
     */
    public static function getTradeOrderReservationFirstTime() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $tradeOrderReservationData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTradeOrderReservation");
        $firstTime = 0;
        if (isset($tradeOrderReservationData[self::$studentUid]) && is_array($tradeOrderReservationData[self::$studentUid])) {
            foreach ($tradeOrderReservationData[self::$studentUid] as $_data) {
                if ($firstTime == 0) {
                    $firstTime = $_data['createTime'];
                }
                if ($firstTime > $_data['createTime']) {
                    $firstTime = $_data['createTime'];
                }
            }
        }
        self::$student['tradeorderreservationfirsttime'] = $firstTime ? date('Y-m-d H:i:s', $firstTime) : '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tradeorderreservationfirsttime', [
            'title'=>'【最早预约时间】',
            'source' => 'es预约单索引idl_trade_order_reservation',
            '解释'    => '是否存在该学生的预约记录并找到最早的预约记录'
        ]);
    }

    /**
     * 获取是否单科用户
     */
    public static function getIsSingleSubjectStudent() {
        return [];  // 下线
    }

    /**
     * 是否单科扩科_22寒
     */
    public static function getIsSingleSubjectExtend22han() {
        return [];  // 下线
    }

    /**
     * 获取是否单科扩科
     */
    public static function getIsSingleSubjectExtend() {
        return [];  // 下线
    }

    /**
     * 当天是否有其他直播课
     */
    public static function getIsHaveOtherLiveLesson(){
        $studentLiveLessonData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getStudentLiveLessonNum",
            [AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$courseId]);
        self::$student['isHaveOtherLiveLesson'] = $studentLiveLessonData[self::$studentUid] ?? 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isHaveOtherLiveLesson', [
            'title'  => '【当天是否有其他直播课】',
            'source' => 'es：student_live_lesson_num',
            '解释'     => 'https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=319010067'
        ]);
    }

    /**
     * 获取单科扩科详情
     */
    public static function getSingleSubjectExtendDetail() {
        return [];  // 下线
    }

    /**
     * 获取单科扩科详情
     */
    public static function getSingleSubjectExtendDetail22han() {
        return [];  // 下线
    }

    /**
     * 隔季二级续报
     */
    public static function getL2rSpaceSeasonStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields(["isL2rSpaceSeason"])) {
            return true;
        }

        $continueData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");

        self::$student['l2rSpaceSeasonStatus'] = intval($continueData[self::$studentUid]['isL2rSpaceSeason']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'l2rSpaceSeasonStatus', [
            'title'  => '【隔季二级续报状态】',
            'source' => 'es订单：字段：is_l2r_space_season',
            '解释'     => '隔季二级续报状态'
        ]);
    }

    /**
     * 隔季二级续报且联报
     */
    public static function getL2rBoundSpaceSeasonStatus(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields(["isL2rBoundSpaceSeason"])) {
            return true;
        }

        $continueData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");

        self::$student['l2rBoundSpaceSeasonStatus'] = intval($continueData[self::$studentUid]['isL2rBoundSpaceSeason']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'l2rBoundSpaceSeasonStatus', [
            'title'  => '【隔季二级续报且联报状态】',
            'source' => 'es订单：字段：is_l2r_bound_space_season',
            '解释'     => '隔季二级续报且联报状态'
        ]);
    }

    /**
     * 隔季二级续报详情
     */
    public static function getL2rSpaceSeasonDetail(){
        $sourceField = 'spaceSeasonRetainDetail';
        $field = 'l2rSpaceSeason';
        $detailField = $field."Detail";
        $infoField = $field."DetailInfo";
        $subjectIdsFilterField = $field."_sids";
        $detail = self::getL2rSpaceSeasonData($sourceField,$field);
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        self::$student[$detailField] = $detail[self::$studentUid][$detailField];
        self::$student[$infoField] = $detail[self::$studentUid][$infoField];
        self::$student[$subjectIdsFilterField] = $detail[self::$studentUid][$subjectIdsFilterField];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $detailField, [
            'title'  => '【隔季二级续报详情】',
            'source' => 'es订单：字段：space_season_retain_detail格式化后的数据',
            '解释'     => '隔季二级续报详情'
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $infoField, [
            'title'  => '【隔季二级续报详情tips】',
            'source' => 'es订单：字段：space_season_retain_detail格式化后的数据',
            '解释'     => '隔季二级续报详情tips'
        ]);
    }

    /**
     * 隔季二级续报时间
     */
    public static function getL2rSpaceSeasonTime(){
        $sourceField = 'spaceSeasonRetainDetail';
        $field = 'l2rSpaceSeason';
        $timeField = $field."Time";
        $detail = self::getL2rSpaceSeasonData($sourceField,$field);
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        self::$student[$timeField] = $detail[self::$studentUid][$timeField];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $timeField, [
            'title'  => '【隔季二级续报时间】',
            'source' => 'es订单：字段：space_season_retain_detail获取有效订单中日期最新的时间',
            '解释'     => '隔季二级续报时间'
        ]);
    }

    /**
     * 隔季二级续报且联报时间
     */
    public static function getL2rBoundSpaceSeasonTime(){
        $sourceField = 'spaceSeasonBoundDetail';
        $field = 'l2rBoundSpaceSeason';
        $timeField = $field."Time";
        $detail = self::getL2rSpaceSeasonData($sourceField,$field);
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        self::$student[$timeField] = $detail[self::$studentUid][$timeField];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $timeField, [
            'title'  => '【隔季二级续报且联报时间】',
            'source' => 'es订单：字段：space_season_bound_detail获取有效订单中日期最新的时间',
            '解释'     => '隔季二级续报且联报时间'
        ]);
    }

    /**
     * 隔季二级续报且联报详情
     */
    public static function getL2rBoundSpaceSeasonDetail(){
        $sourceField = 'spaceSeasonBoundDetail';
        $field = 'l2rBoundSpaceSeason';
        $detailField = $field."Detail";
        $infoField = $field."DetailInfo";
        $subjectIdsFilterField = $field."_sids";
        $detail = self::getL2rSpaceSeasonData($sourceField,$field);
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        self::$student[$detailField] = $detail[self::$studentUid][$detailField];
        self::$student[$infoField] = $detail[self::$studentUid][$infoField];
        self::$student[$subjectIdsFilterField] = $detail[self::$studentUid][$subjectIdsFilterField];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $detailField, [
            'title'  => '【隔季二级续报且联报详情】',
            'source' => 'es订单：字段：space_season_bound_detail格式化后的数据',
            '解释'     => '隔季二级续报且联报详情'
        ]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $infoField, [
            'title'  => '【隔季二级续报且联报详情tips】',
            'source' => 'es订单：字段：space_season_bound_detail格式化后的数据',
            '解释'     => '隔季二级续报且联报详情tips'
        ]);
    }

    private static function getL2rSpaceSeasonData($sourceField,$field) {
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields([$sourceField, 'courseId'])) {
            return true;
        }

        if (isset(self::$L2rSpaceSeasonData[$field])) {
            return self::$L2rSpaceSeasonData[$field];
        }
        //获取order数据
        $continueData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");

        //获取续报详情课程ids
        $courseIds = [];
        foreach ($continueData as $cd) {
            $details = $cd[$sourceField];
            if (!empty($details)) {
                $courseIds = array_merge($courseIds, array_unique(array_column($details,'courseId')));
            }
        }

        //获取主讲老师信息
        $teacherInfos = AssistantDesk_Teacher::getTeacherNameByCourseIds($courseIds) ?: [];
        //获取课程信息
        $courseInfos = Api_Dal::getCourseLessonInfoByCourseIds($courseIds) ?: [];
        $data = [];
        foreach ($continueData as $stuId => $cd) {
            //详情
            $data[$stuId][$field.'Detail'] = [];
            $data[$stuId][$field.'Periods']= [];
            //详情tips
            $data[$stuId][$field.'DetailInfo'] = '';
            //续报时间
            $data[$stuId][$field.'Time'] = '';

            if (!empty($cd[$sourceField])) {
                foreach ($cd[$sourceField] as $k=>$detail) {
                    //过滤退款课程
                    if ($detail['status'] != 1) {
                        unset($cd[$sourceField][$k]);
                    }
                }
                //续报时间，取最新一个时间
                if (!empty($cd[$sourceField])) {
                    $arrTime = array_column($cd[$sourceField],'createTime');
                    rsort($arrTime);
                    $data[$stuId][$field.'Time'] = date("Y-m-d H:i:s",$arrTime[0]);

                    //拼接详情
                    foreach ($cd[$sourceField] as $detail) {
                        if (empty($detail)) {
                            continue;
                        }
                        if (!isset($courseInfos[$detail['courseId']])) {
                            continue;
                        }

                        $courseInfo = Zb_Const_GradeSubject::$GRADE[$courseInfos[$detail['courseId']]['mainGradeId']].AssistantDesk_Common_Keyconst::getSubject()[$courseInfos[$detail['courseId']]['mainSubjectId']];
                        $period     = Const_Season::getLearnSeasonIdNameFullV2Map()[$courseInfos[$detail['courseId']]['learnSeason']] ?? '';
                        $data[$stuId][$field.'Detail'][]    = $courseInfo;
                        $data[$stuId][$field."_sids"][] = $courseInfos[$detail['courseId']]['mainSubjectId'];
                        $data[$stuId][$field.'Periods'][]    = $period;
                        $data[$stuId][$field.'DetailInfo'] .= $courseInfos[$detail['courseId']]['courseName'] ?? '';
                        $data[$stuId][$field.'DetailInfo'] .= "</br>";
                        $data[$stuId][$field.'DetailInfo'] .= $teacherInfos[$detail['courseId']] ? implode(',', $teacherInfos[$detail['courseId']]) : "";
                        $data[$stuId][$field.'DetailInfo'] .= ' ';
                        $data[$stuId][$field.'DetailInfo'] .= $courseInfos[$detail['courseId']]['onlineFormatTimeAll'];
                        $data[$stuId][$field.'DetailInfo'] .= "</br>";
                    }
                }
            }

            //去重拼接
            $data[$stuId][$field.'Detail'] = implode(array_unique($data[$stuId][$field.'Detail']),',');
            $data[$stuId][$field.'Periods'] = implode(array_unique($data[$stuId][$field.'Periods']),',');

            $data[$stuId][$field.'_sids'] = array_values(array_unique($data[$stuId][$field.'_sids']));
        }

        self::$L2rSpaceSeasonData[$field] = $data;
        return $data;
    }

    /**
     * 获取是否领取22寒小鹿素养课
     * @throws ReflectionException
     */
    public static function getIsGet22WinterXiaoluCourse() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $userRoleData = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getUserRoleData");
        $userRoleData = $userRoleData[self::$studentUid] ?: [];

        self::$student['isGet22WinterXiaoluCourse'] = intval(in_array(Api_UserRole::ROLE_ID_2978, $userRoleData));
        AssistantDesk_Data_CommentAdd::addCommentArr(
            self::$student, 'isGet22WinterXiaoluCourse', [
                'title'  => '【是否领取22寒小鹿素养课】',
                'source' => '获取用户是否包含2978角色，/userrole/coreapi/getuserroles',
                '解释'     => '是否领取22寒小鹿素养课'
            ]
        );
    }

    /**
     * 获取LBP观看状态
     */
    public static function getIsLbpAttend() {
        if(AssistantDesk_Data_DataSource::beforeAddLuFields(["isLbpAttend"])) {
            return [];
        }
        $esLuData = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', "getStudentLessonData");
        self::$student['isLbpAttend'] = isset($esLuData[self::$studentUid]['isLbpAttend']) ? $esLuData[self::$studentUid]['isLbpAttend'] : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLbpAttend', [
            'title'=>'【录播观看状态】',
            'source' => 'es.lu.idl_assistant_lesson_student_action.is_lbp_attend',
            '解释'     => '录播观看状态'
        ]);
    }

    /**
     * 获取LBP观看完课状态
     */
    public static function getIsLbpAttendFinish() {
        if(AssistantDesk_Data_DataSource::beforeAddLuFields(["isLbpAttendFinish"])) {
            return [];
        }
        $esLuData = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', "getStudentLessonData");
        self::$student['isLbpAttendFinish'] = isset($esLuData[self::$studentUid]['isLbpAttendFinish']) ? $esLuData[self::$studentUid]['isLbpAttendFinish'] : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isLbpAttendFinish', [
            'title'=>'【录播观看完课状态】',
            'source' => 'es.lu.idl_assistant_lesson_student_action.is_lbp_attend_finish',
            '解释'     => '录播观看完课状态'
        ]);
    }

    /**
     * 获取LBP章节最后观看时间
     */
    public static function getLbpLastPlaytime() {
        if(AssistantDesk_Data_DataSource::beforeAddLuFields(["lbpLastPlaytime"])) {
            return [];
        }

        $esLuData = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', "getStudentLessonData");
        self::$student['lbpLastPlaytime'] = isset($esLuData[self::$studentUid]['lbpLastPlaytime']) ? $esLuData[self::$studentUid]['lbpLastPlaytime'] : 0;
        self::$student['lbpLastPlaytime'] = self::$student['lbpLastPlaytime'] ? date("Y-m-d H:i",self::$student['lbpLastPlaytime']) : '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpLastPlaytime', [
            'title'=>'【录播最后观看时间】',
            'source' => 'es.lu.idl_assistant_lesson_student_action.lbp_last_playtime',
            '解释'     => '录播最后观看时间'
        ]);
    }

    /**
     * 获取LBP章节观看时长
     */
    public static function getLbpAttendDuration() {
        if(AssistantDesk_Data_DataSource::beforeAddLuFields(["lbpAttendDuration"])) {
            return [];
        }

        $esLuData = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', "getStudentLessonData");
        self::$student['lbpAttendDuration'] = isset($esLuData[self::$studentUid]['lbpAttendDuration']) ? $esLuData[self::$studentUid]['lbpAttendDuration'] : 0;
        //self::$student['lbpAttendDuration'] = self::$student['lbpAttendDuration'] % 60 > 0 ? floor(self::$student['lbpAttendDuration'] / 60) .'min'.(self::$student['lbpAttendDuration'] % 60).'s' : floor(self::$student['lbpAttendDuration'] / 60) .'min';
        // 改为 int 秒数返回，前端根据 cname format
        self::$student['lbpAttendDuration'] = floor(self::$student['lbpAttendDuration']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpAttendDuration', [
            'title'=>'【录播在线观看时长】',
            'source' => 'es.lu.idl_assistant_lesson_student_action.lbp_attend_duration',
            '解释'     => '录播在线观看时长'
        ]);
    }

    /**
     * 获取累计LBP到课章节数
     */
    public static function getLbpAttendNum(){
        if(AssistantDesk_Data_DataSource::beforeAddCuFields(["lbpAttendCount"])) {
            return [];
        }
        $esCuData          = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");;

        self::$student['lbpAttendNum'] = $esCuData[self::$studentUid]['lbpAttendCount'] ? intval($esCuData[self::$studentUid]['lbpAttendCount']) : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpAttendNum', [
            'title' => '【LBP累计到课章节数】',
            '数据源'   => 'es.cu.lbp_attend_count',
        ]);
    }

    /**
     * 获取累计LBP到课章节数
     */
    public static function getLbpFinishNum(){
        if(AssistantDesk_Data_DataSource::beforeAddCuFields(["lbpFinishCount"])) {
            return [];
        }
        $esCuData          = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");;

        self::$student['lbpFinishNum'] = $esCuData[self::$studentUid]['lbpFinishCount'] ? intval($esCuData[self::$studentUid]['lbpFinishCount']) : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpFinishNum', [
            'title' => '【LBP累计完课章节数】',
            '数据源'   => 'es.cu.lbp_finish_count',
        ]);
    }

    /**
     * lbp到课章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getLbpAttendNumRatio() {
        self::getLbpAttendNum();
        if(AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData("AssistantDesk_Course", "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['lbpAttendNumRatio'] = intval(self::$student['lbpAttendNum']) . "/" . $lessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpAttendNumRatio', [
            'title' => '【LBP到课节数】',
            '数据源'   => 'dal获取到 课程下主体章节个数,cu 获取到课章节数lbp_attend_count',
        ]);
    }

    /**
     * lbp完课章节数/ 总章节数
     * @throws ReflectionException
     */
    public static function getLbpFinishNumRatio() {
        self::getLbpFinishNum();
        if(AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $courseInfo = Common_Singleton::getInstanceData("AssistantDesk_Course", "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];
        $lessonList = array_filter($lessonList, function ($v) {
            return $v['lessonType'] == Api_Dal::LESSON_TYPE_MAIN;
        });
        $lessonCnt = count($lessonList);
        self::$student['lbpFinishNumRatio'] = intval(self::$student['lbpFinishNum']) . "/" . $lessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpFinishNumRatio', [
            'title' => '【LBP完课章节数】',
            '数据源'   => 'dal获取到 课程下主体章节个数,cu 获取到课章节数lbp_finish_count',
        ]);
    }

    /**
     * LBP互动题（对/答/总）
     */
    public static function getLbpInteractionExam() {
        if (AssistantDesk_Data_DataSource::beforeAddLuFields(["mix_live_interaction_right_num", "mix_live_interaction_submit_num"]) && AssistantDesk_Data_DataSource::beforeAddLFields(["mix_interaction_total_num"])) {
            return [];
        }
        $esLessonData        = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getStudentLessonData");
        $esLesson            = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getArkLessonData", [
            [AssistantDesk_Data_CommonParams::$lessonId],
        ]);
        $studentLessonInfo   = $esLessonData[self::$studentUid];
        $intInteractTotalNum = $esLesson[AssistantDesk_Data_CommonParams::$lessonId]['mix_interaction_total_num'] ?? 0;
        if ($intInteractTotalNum == 0) {
            self::$student['lbpInteractExam'] = '-';
        } else {
            self::$student['lbpInteractExam'] = sprintf(
                self::LESSON_EXERCISE_DETAIL,
                $studentLessonInfo['mix_live_interaction_right_num'],
                $studentLessonInfo['mix_live_interaction_submit_num'],
                $intInteractTotalNum
            );
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lbpInteractExam', [
            "title"      => "LBP互动题对答总",
            "dataSource" => "参与数：lu:mix_live_interaction_submit_num,正确数： lu:mix_live_interaction_right_num,总数：ads_zbk_lesson_common:mix_interaction_total_num",
            "解释"        => "总题数为0则为-，否则展示对答总"
        ]);
    }

    /**
     * 转介绍参与意向评估
     */
    public static function getLevelTransfer(){
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields([
            "levelTransfer",
        ])) {
            return [];
        }

        $esCuData          = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsCuData");;

        self::$student['levelTransfer'] = $esCuData[self::$studentUid]['levelTransfer'] ?? '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'levelTransfer', [
            'title' => '【转介绍参与意向评估】',
            '数据源'   => 'es.cu.level_transfer',
        ]);
    }


    /**
     * 转介绍新增“绑定到期时间”字段
     * https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=332959518
     */
    public static function getExpireTime(){
        $activityRelationList = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getActivityExpireTime",
            [
                AssistantDesk_Data_CommonParams::$taskId,
                AssistantDesk_Data_CommonParams::$assistantUid,
                AssistantDesk_Data_CommonParams::$studentUids,
            ]
        );
        self::$student['expireTime'] = $activityRelationList[self::$studentUid]['expireTime'] ?? "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'expireTime', [
            'title'  => '【绑定到期时间】',
            'source' => 'es：external_idl_fudao_referral_activity_bind_relation_l2',
            '解释'     => 'https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=332959518'
        ]);
    }

    /**
     * 隔季扩科状态
     */
    public static function getSpaceExpandDetail(){
        if (\AssistantDesk_Data_DataSource::beforeAddOrderFields(["space_expand_detail"])) {
            return true;
        }

        $continueData      = Common_Singleton::getInstanceData("AssistantDesk_Data_DataQuery", "getEsContinueData");

        $details = $continueData[self::$studentUid]['space_expand_detail'] ?? [];
        $formatedDetail = [];
        foreach ($details as $detail) {
            $subject = AssistantDesk_Common_Keyconst::getSubject()[$detail['subject']] ?? '';
            if (empty($subject)) {
                continue;
            }
            $formatedDetail[] = $subject;
        }
        //格式化
        self::$student['spaceExpandDetail'] = !empty($formatedDetail) ? implode(",",array_unique($formatedDetail)) : '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'spaceExpandDetail', [
            'title'  => '【隔季扩科详情】',
            'source' => 'holo订单：字段：space_expand_detail',
            '解释'     => '隔季扩科详情'
        ]);
    }

    /**
     * 获取预约信息
     */
    public static function getTradeOrderReservationInfo($year = -1, $season = -1,$seasonNum = -1) {
        $tradeOrderReservationData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getTradeOrderReservation",[]);
        $seasonNum = (!is_array($seasonNum) && $seasonNum != -1) ? [$seasonNum] : $seasonNum;
        if (isset($tradeOrderReservationData[self::$studentUid]) && is_array($tradeOrderReservationData[self::$studentUid])) {
            foreach ($tradeOrderReservationData[self::$studentUid] as $k => $_data) {
                //过滤非有效状态
                if (!in_array($_data['reserve_status'],[1,2])) {
                    unset($tradeOrderReservationData[self::$studentUid][$k]);
                    continue;
                }
                //过滤学年、学季、学期和状态
                if (-1 != $year && $_data['year'] != $year) {
                    unset($tradeOrderReservationData[self::$studentUid][$k]);
                    continue;
                }
                if (-1 != $season && $_data['season'] != $season) {
                    unset($tradeOrderReservationData[self::$studentUid][$k]);
                    continue;
                }
                if (-1 != $seasonNum && !in_array(intval($_data['learn_season']),$seasonNum)) {
                    unset($tradeOrderReservationData[self::$studentUid][$k]);
                    continue;
                }
            }
        }
        return $tradeOrderReservationData;
    }

    /**
     * 获取预约状态
     */
    public static function getTradeOrderReservationStatusV2() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $courseInfo    = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year = $courseInfo['year'];
        $season = $courseInfo['season'];
        $seasonNum = $courseInfo['learnSeason'];
        //TODO 2022初三春1、春2预约秋1，数据预约单数据没有期次，期次临时处理为0
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING == $season && '初三' == Zb_Const_GradeSubject::$GRADE[$courseInfo['mainGradeId']]) {
            $seasonNum = 0;
        }
        //TODO 2022小学春3预约秋1，数据预约单数据中存的是春2，期次临时处理为春2
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3 == $seasonNum && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2;
        }
        //TODO 2022小学春2、春3预约秋1，数据预约单数据中存的是春2或null，期次临时处理为春2和0
        if (2022 == $year && in_array($seasonNum,[Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3])
            && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = [Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,0];
        }
        //获取当前课程学季、学期
        $tradeOrderReservationData = self::getTradeOrderReservationInfo($year,$season,$seasonNum);

        self::$student['tradeorderreservationstatus'] = isset($tradeOrderReservationData[self::$studentUid]) && !empty($tradeOrderReservationData[self::$studentUid]) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tradeorderreservationstatus', [
            'title'=>'【是否预约过】',
            'source' => 'es预约单索引idl_trade_order_reservation',
            '解释'    => '是否存在该学生的预约记录'
        ]);
    }

    /**
     * 获取首次预约时间
     */
    public static function getTradeOrderReservationFirstTimeV2() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $courseInfo    = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year = $courseInfo['year'];
        $season = $courseInfo['season'];
        $seasonNum = $courseInfo['learnSeason'];
        //TODO 2022初三春1、春2预约秋1，数据预约单数据没有期次，期次临时处理为0
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING == $season && '初三' == Zb_Const_GradeSubject::$GRADE[$courseInfo['mainGradeId']]) {
            $seasonNum = 0;
        }
        //TODO 2022小学春3预约秋1，数据预约单数据中存的是春2，期次临时处理为春2
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3 == $seasonNum && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2;
        }
        //TODO 2022小学春2、春3预约秋1，数据预约单数据中存的是春2或null，期次临时处理为春2和0
        if (2022 == $year && in_array($seasonNum,[Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3])
            && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = [Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,0];
        }
        //获取当前课程学季、学期
        $tradeOrderReservationData = self::getTradeOrderReservationInfo($year,$season,$seasonNum);

        $firstTime = 0;
        if (isset($tradeOrderReservationData[self::$studentUid]) && is_array($tradeOrderReservationData[self::$studentUid])) {
            foreach ($tradeOrderReservationData[self::$studentUid] as $_data) {
                if ($firstTime == 0) {
                    $firstTime = $_data['createTime'];
                }
                if ($firstTime > $_data['createTime']) {
                    $firstTime = $_data['createTime'];
                }
            }
        }
        self::$student['tradeorderreservationfirsttime'] = $firstTime ? date('Y-m-d H:i:s', $firstTime) : '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'tradeorderreservationfirsttime', [
            'title'=>'【最早预约时间】',
            'source' => 'es预约单索引idl_trade_order_reservation',
            '解释'    => '是否存在该学生的预约记录并找到最早的预约记录'
        ]);
    }

    /**
     * 获取预约科目
     */
    public static function getNextTermPreOrderDetailV2() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $courseInfo    = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year = $courseInfo['year'];
        $season = $courseInfo['season'];
        $seasonNum = $courseInfo['learnSeason'];
        //TODO 2022初三春1、春2预约秋1，数据预约单数据没有期次，期次临时处理为0
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING == $season && '初三' == Zb_Const_GradeSubject::$GRADE[$courseInfo['mainGradeId']]) {
            $seasonNum = 0;
        }
        //TODO 2022小学春3预约秋1，数据预约单数据中存的是春2，期次临时处理为春2
        if (2022 == $year && Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3 == $seasonNum && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2;
        }
        //TODO 2022小学春2、春3预约秋1，数据预约单数据中存的是春2或null，期次临时处理为春2和0
        if (2022 == $year && in_array($seasonNum,[Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,Zb_Const_LearnSeason::LEARN_SEASON_SPRING_3])
            && Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY == Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
            $seasonNum = [Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2,0];
        }
        //获取当前课程学季、学期
        $getStudentPreOrders = self::getTradeOrderReservationInfo($year,$season,$seasonNum);

        $nextTermPreOrderDetail = [];
        if(is_array($getStudentPreOrders[self::$studentUid])){
            foreach ($getStudentPreOrders[self::$studentUid] as $studentPreOrder){
                $nextTermPreOrderDetail[] = AssistantDesk_Common_Keyconst::getSubject()[$studentPreOrder['subject_id']];
            }
        }

        self::$student['nextTermPreOrderDetail'] = $nextTermPreOrderDetail ? implode(',',array_unique($nextTermPreOrderDetail)) : "-";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'nextTermPreOrderDetail', [
            'title'=>'【预约科目】',
            'source' => 'es预约单索引idl_trade_order_reservation',
            '解释'     => '所有预约科目'
        ]);
    }

    /**
     * 计算每一个学生的当前学季报名的学科
     */
    public static function currentSeasonSignUpSubjectInfoV2(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        //获取指定学期
        $fieldRule     = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey      = strval($fieldRule['key'] ?? '');
        $specifiedYear = intval($fieldRule['serviceConfig']['specifiedYear'] ?? 0);
        $specifiedLearnSeason = $fieldRule['serviceConfig']['specifiedLearnSeason'] ?? ''; //多个以","分割
        $subjectIds = $fieldRule['serviceConfig']['subjectIds'] ?? ''; //多个以","分割
        $subjectIdArgs = explode(",",$subjectIds);

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year           = $specifiedYear ?: $courseInfo['year'];
        $type           = $courseInfo['courseType'];
        $learnSeason    = $specifiedLearnSeason ?: $courseInfo['learnSeason'];
        $seasonNum = [];
        $season = [];
        $learnSeasonArr = explode(",", $learnSeason);
        foreach ($learnSeasonArr as $learnSeason) {
            $learnSeason = Const_Season::getLearnSeasonIdNameFullMap()[$learnSeason] ?? '';
            if ($learnSeason) {
                list($singleSeason,$singleSeasonNum) = explode("_",$learnSeason);
                $seasonNum[] = $singleSeasonNum;
                $season[]    = $singleSeason;
            }
        }

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids,$seasonNum]
        );
        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];
        $subjectList = $studentSubjectNumArr[self::$studentUid] ?? [];
        $subjectNameList = [];

        $subjectIdArr = [];
        foreach ($subjectList as $subjectId) {
            $subjectName = AssistantDesk_Common_Keyconst::getSubject()[$subjectId] ?? '';
            if (empty($subjectName)) {
                $subjectIdArr[] = $subjectId;
                continue;
            }

            if (empty($subjectIds)) {
                $subjectNameList[] = $subjectName;
                $subjectIdArr[] = $subjectId;
                continue;
            }

            if ($subjectIdArgs && in_array($subjectId,$subjectIdArgs)) {
                //只统计指定指定学科的
                $subjectNameList[] = $subjectName;
                $subjectIdArr[] = $subjectId;
            }
        }
        self::$student['currentSeasonSignUpSubjectInfo'] = implode(',', $subjectNameList);
        // 方便后续切换字段
        self::$student[$fieldKey] = implode(',', $subjectNameList);
        foreach($subjectNameList as &$v) {
            if ($v == '历史') {
                $v = '史';
            } else {
                $v = mb_substr($v, 0, 1, 'utf-8');
            }
        }
        unset($v);
        self::$student['currentSeasonSignUpSubjectInfoArr'] = $subjectNameList;
        self::$student['currentSeasonSignUpSubjectIdArr']   = array_values($subjectIdArr);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'currentSeasonSignUpSubjectInfoArr', [
            'title'  => '【计算每一个学生的指定学季报名的学科】',
            'source' => '数据库表tblAssistantCourseStudent程序计算',
        ]);
    }

    /**
     * 计算每一个学生的当前学季未报名的学科
     */
    public static function currentSeasonUnSignUpSubjectInfo(){
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        //获取指定学期
        $fieldRule     = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey      = strval($fieldRule['key'] ?? '');
        $specifiedYear = intval($fieldRule['serviceConfig']['specifiedYear'] ?? 0);
        $specifiedLearnSeason = $fieldRule['serviceConfig']['specifiedLearnSeason'] ?? ''; //多个以","分割

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year           = $specifiedYear ?: $courseInfo['year'];
        $type           = $courseInfo['courseType'];
        $learnSeason    = $specifiedLearnSeason ?: $courseInfo['learnSeason'];
        $seasonNum = [];
        $season = [];
        $learnSeasonArr = explode(",", $learnSeason);
        foreach ($learnSeasonArr as $learnSeason) {
            $learnSeason = Const_Season::getLearnSeasonIdNameFullMap()[$learnSeason] ?? '';
            if ($learnSeason) {
                list($singleSeason,$singleSeasonNum) = explode("_",$learnSeason);
                $seasonNum[] = $singleSeasonNum;
                $season[]    = $singleSeason;
            }
        }

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids,$seasonNum]
        );
        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];
        $subjectList = $studentSubjectNumArr[self::$studentUid] ?? [];

        $allSubjectList = $fieldRule["filterMap"] ?? [];
        foreach ($subjectList as $subjectId) {
            if (isset($allSubjectList[$subjectId])){
                unset($allSubjectList[$subjectId]);
            }
        }
        self::$student['currentSeasonUnSignUpSubjectInfo'] = implode(',', $allSubjectList);
        self::$student[$fieldKey] = implode(',', $allSubjectList);
        foreach($allSubjectList as &$v) {
            if ($v == '历史') {
                $v = '史';
            } else {
                $v = mb_substr($v, 0, 1, 'utf-8');
            }
        }
        unset($v);
        self::$student['currentSeasonUnSignUpSubjectInfoArr'] = $allSubjectList;
        self::$student['currentSeasonUnSignUpSubjectIdArr']   = array_keys($allSubjectList);

        $params = [AssistantDesk_Data_CommonParams::$studentUids];
        $studentCourseTimeTable = Common_Singleton::getInstanceData(AssistantDesk_Filter::class, "getStudentCourseTime", $params);
        self::$student['courseTimeTable'] = $studentCourseTimeTable[self::$studentUid] ?? [];

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'currentSeasonUnSignUpSubjectInfoArr', [
            'title'  => '【计算每一个学生的指定学季未报名的学科】',
            'source' => '数据库表tblAssistantCourseStudent程序计算',
        ]);
    }

    /**
     * 计算每一个学生的指定学期报名的学科数
     */
    public static function getSpecifiedSemesterSignUpSubjectNum(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        //获取指定学期
        $fieldRule           = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $specifiedYear = intval($fieldRule['feConfig']['specifiedYear'] ?? 0);
        $specifiedLearnSeason = intval($fieldRule['feConfig']['specifiedLearnSeason'] ?? 0);

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year           = $specifiedYear ?: $courseInfo['year'];
        $type           = $courseInfo['courseType'];
        $learnSeason    = $specifiedLearnSeason ?: $courseInfo['learnSeason'];
        $seasonNum = 0;
        $season = '';
        $learnSeason = Const_Season::getLearnSeasonIdNameFullMap()[$learnSeason] ?? '';
        if ($learnSeason) {
            list($season,$seasonNum) = explode("_",$learnSeason);
        }

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids,$seasonNum]
        );
        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];
        $cnt = $studentSubjectNumArr[self::$studentUid] ? count($studentSubjectNumArr[self::$studentUid]) : 0;
        $returnKey = "specifiedSemesterSignUpSubjectNum".$specifiedLearnSeason;
        self::$student[$returnKey] = $cnt;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【计算指定期次续报科目数】',
            'source' => '数据库表tblAssistantCourseStudent程序计算',
        ]);
    }

    /**
     * 计算每一个学生的指定学期报名的学科数
     */
    public static function getSpecifiedSemesterSignUpSubjectNumV2(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        //获取指定学期
        $fieldRule     = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey      = strval($fieldRule['key'] ?? '');
        $specifiedYear = intval($fieldRule['serviceConfig']['specifiedYear'] ?? 0);
        $specifiedLearnSeason = $fieldRule['serviceConfig']['specifiedLearnSeason'] ?? ''; //多个以","分割
        $subjectIds = $fieldRule['serviceConfig']['subjectIds'] ?? ''; //多个以","分割
        $subjectIdArgs = explode(",",$subjectIds);

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year           = $specifiedYear ?: $courseInfo['year'];
        $type           = $courseInfo['courseType'];
        $learnSeason    = $specifiedLearnSeason ?: $courseInfo['learnSeason'];
        $seasonNum = [];
        $season = [];
        $learnSeasonArr = explode(",", $learnSeason);
        foreach ($learnSeasonArr as $learnSeason) {
            $learnSeason = Const_Season::getLearnSeasonIdNameFullMap()[$learnSeason] ?? '';
            if ($learnSeason) {
                list($singleSeason,$singleSeasonNum) = explode("_",$learnSeason);
                $seasonNum[] = $singleSeasonNum;
                $season[]    = $singleSeason;
            }
        }

        $studentSubjectNumArr = Common_Singleton::getInstanceData(
            AssistantDesk_Filter::class,
            'currentSeasonSignUpSubjectNumCount',
            [$year,$season,$type,AssistantDesk_Data_CommonParams::$studentUids,$seasonNum]
        );
        $studentSubjectNumArr = is_array($studentSubjectNumArr) ? $studentSubjectNumArr : [];
        $stuSubjectArr = $studentSubjectNumArr[self::$studentUid] ?? [];
        $SubjectArr = [];
        foreach ($stuSubjectArr as $subjectId) {
            if (empty($subjectIds)) {
                $SubjectArr[] = $subjectId;
                continue;
            }
            if ($subjectIdArgs && in_array($subjectId,$subjectIdArgs)) {
                //只统计指定指定学科的
                $SubjectArr[] = $subjectId;
            }
        }
        self::$student[$fieldKey] = count($SubjectArr);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title'  => '【计算指定期次续报科目数】',
            'source' => '数据库表tblAssistantCourseStudent和tblAssistantNewCourse程序计算',
        ]);
    }

    public static function getIsBuyVolunteerCard() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $volunteerData = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', 'getStudentBuyVolunteerCardInfo', [AssistantDesk_Data_CommonParams::$studentUids]);
        self::$student['isBuyVolunteerCard'] = (isset($volunteerData[self::$studentUid]) && ($volunteerData[self::$studentUid] > 0)) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isBuyVolunteerCard', [
            'title'  => '【是否购买志愿卡】',
            'source' => 'es：zyb_zbk_bzr_ads_base.ads_zbk_trade_order_volunteer_card',
            '解释'   => '是否购买志愿卡'
        ]);
    }

    /**
     * 获取学期续报详情
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getSemesterContinueDetail() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        //计算续报学年、学季、学期
        $continueType = AssistantDesk_Season::CONTINUE_TYPE_LEARN_SEASON;
        //获取续报学年、学季、学期查询条件,学期续报无法确定下个学期，只能写死学期
        $otherConds = self::_getContinueConds($continueType,0,0,Zb_Const_LearnSeason::LEARN_SEASON_SPRING_2);
        $fields = [
            'student_uid',
            'grade_id',
            'valid_subjects'
        ];
        $continueLists = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', 'getContinueSemesterDataByStudentUidsAndOtherConds', [AssistantDesk_Data_CommonParams::$studentUids,$otherConds,$fields]);
        $continueData = $continueLists[self::$studentUid] ?? [];
        //详情
        self::$student['semesterContinueDetail'] = [];
        //详情tips，不展示
        //self::$student['continueDetailInfo'] = '';
        foreach ($continueData as $cd) {
            //拼接年级和学科
            if (!isset($cd['valid_subjects']) || !is_array($cd['valid_subjects'])) {
                continue;
            }

            //拼接年级和学科
            foreach ($cd['valid_subjects'] as $subject) {
                if (empty($subject)) {
                    continue;
                }
                self::$student['semesterContinueDetail'][] = Zb_Const_GradeSubject::$GRADE[$cd['grade_id']].AssistantDesk_Common_Keyconst::getSubject()[$subject];
            }
        }
        self::$student['semesterContinueDetail'] = implode(",",array_unique(self::$student['semesterContinueDetail']));
        self::$student['semesterContinueDetail'] = self::$student['semesterContinueDetail'] ?: '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'semesterContinueDetail', [
            'title'  => '【学期续报科目详情】',
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'   => '学期续报科目详情'
        ]);
    }

    /**
     * 获取指定学期续报详情
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getSpecifiedSemesterContinueDetail() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        //获取指定学期
        $fieldRule            = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $specifiedYear        = intval($fieldRule['feConfig']['specifiedYear'] ?? 0);
        $specifiedSeason      = intval($fieldRule['feConfig']['specifiedSeason'] ?? 0);
        $specifiedLearnSeason = intval($fieldRule['feConfig']['specifiedLearnSeason'] ?? 0);

        //计算续报学年、学季、学期
        $continueType = AssistantDesk_Season::CONTINUE_TYPE_LEARN_SEASON;
        //获取续报学年、学季、学期查询条件,学期续报无法确定下个学期，只能写死学期
        $otherConds = self::_getContinueConds($continueType,$specifiedYear,$specifiedSeason,$specifiedLearnSeason);
        $fields = [
            'student_uid',
            'grade_id',
            'valid_subjects'
        ];
        $continueLists = Common_Singleton::getInstanceData('AssistantDesk_Data_DataQuery', 'getContinueSemesterDataByStudentUidsAndOtherConds', [AssistantDesk_Data_CommonParams::$studentUids,$otherConds,$fields]);
        $continueData = $continueLists[self::$studentUid] ?? [];
        //详情
        $returnKey = "specifiedSemesterContinueDetail{$specifiedLearnSeason}";
        self::$student[$returnKey] = [];
        foreach ($continueData as $cd) {
            //拼接年级和学科
            foreach ($cd['valid_subjects'] as $subject) {
                if (empty($subject)) {
                    continue;
                }
                self::$student[$returnKey][] = Zb_Const_GradeSubject::$GRADE[$cd['grade_id']] . AssistantDesk_Common_Keyconst::getSubject()[$subject];
            }
        }
        self::$student[$returnKey] = implode(",", array_unique(self::$student[$returnKey]));
        self::$student[$returnKey] = self::$student[$returnKey] ?: '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【学期续报科目详情】',
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'     => '学期续报科目详情'
        ]);
    }

    /**
     * 获取学期续报详情
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getSemesterContinueCommonDetail() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule           = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $nextIsFirstSemester = intval($fieldRule['feConfig']['nextIsFirstSemester'] ?? 0);
        $posSeason           = intval($fieldRule['feConfig']['posSeason'] ?? 2);

        //计算续报学年、学季、学期
        //获取续报学年、学季、学期查询条件,学期续报无法确定下个学期，只能写死学期
        $courseInfo  = AssistantDesk_Course::getCourseInfo(AssistantDesk_Data_CommonParams::$courseId);
        $year        = $courseInfo['year'];
        $season      = $courseInfo['season'];
        $learnSeason = $courseInfo['learnSeason'];
        $grade       = $courseInfo['mainGradeId'];

        //计算续报学年、学季、学期
        $otherConds = [];
        list($nextLearnSeasonYear, $nextLearnSeasonSeason, $nextLearnSeasonGradeId, $semesterNextLearnSeason) = self::getYearSeasonByNextLearnSeason($year, $season, $grade, $learnSeason, $nextIsFirstSemester, $posSeason);
        // 无年级限制，跨年级可能存在问题，需要从接口方改变
        $otherConds['learn_year']   = $nextLearnSeasonYear;
        $otherConds['season']       = $nextLearnSeasonSeason;
        $otherConds['learn_season'] = intval($semesterNextLearnSeason);

        $fields        = [
            'student_uid',
            'grade_id',
            'valid_subjects'
        ];
        $continueLists = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getContinueSemesterDataByStudentUidsAndOtherConds', [AssistantDesk_Data_CommonParams::$studentUids, $otherConds, $fields]);
        $continueData  = $continueLists[self::$studentUid] ?? [];
        //详情
        $returnKey = "semesterContinueCommonDetail{$nextIsFirstSemester}{$posSeason}";
        self::$student[$returnKey] = [];
        foreach ($continueData as $cd) {
            //拼接年级和学科
            if (!isset($cd['valid_subjects']) || !is_array($cd['valid_subjects'])) {
                continue;
            }
            foreach ($cd['valid_subjects'] as $subject) {
                if (empty($subject)) {
                    continue;
                }
                self::$student[$returnKey][] = Zb_Const_GradeSubject::$GRADE[$cd['grade_id']] . AssistantDesk_Common_Keyconst::getSubject()[$subject];
            }
        }
        self::$student[$returnKey] = implode(",", array_unique(self::$student[$returnKey]));
        self::$student[$returnKey] = self::$student[$returnKey] ?: '-';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【学期续报科目详情】',
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'     => '学期续报科目详情'
        ]);
    }

    /**
     * 续报详情-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getSemesterContinueCommonDetailV1() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $learnSeason = $fieldRule['serviceConfig']['learnSeason'] ?? '';
        $gradeIds    = $fieldRule['serviceConfig']['gradeIds'] ?? '';
        $subjects    = $fieldRule['serviceConfig']['subjectIds'] ?? '';
        $subjectMapping = $fieldRule['serviceConfig']['subjectMapping'] ?? '';
        $returnKey   = $fieldRule['key'];
        $gradeIds    = explode(',', $gradeIds);
        $subjects    = $subjects ? explode(',', $subjects) : [];
        $subjectMappings = explode(",", $subjectMapping) ?: [];
        $learnSeasons= explode(',', $learnSeason);
        if (empty($year) || empty($learnSeasons)|| empty($learnSeason)) {
            return;
        }
        $subjectMap = self::extractListToMap($subjectMappings, "=");

        //计算续报学年、学季、学期
        $otherConds                 = [];
        $otherConds['learn_year']   = $year;
        $otherConds['learn_seasons']= $learnSeasons;
        $otherConds['gradeIds']     = $gradeIds;

        if (!empty($subjects)) {
            foreach ($subjects as $idx => $s) {
                if ($s <= 0) {
                    // 本学科
                    $courseInfo  = AssistantDesk_Course::getCourseInfo(AssistantDesk_Data_CommonParams::$courseId);
                    if (!empty($courseInfo) && !empty($courseInfo['mainSubjectId'])) {
                        $subjects[$idx] = $courseInfo['mainSubjectId'];
                    }
                }
                // 有映射
                if ($subjectMap[$subjects[$idx]]) {
                    $subjects[$idx] = $subjectMap[$subjects[$idx]];
                }
            }
        }

        $fields        = [
            'student_uid',
            'grade_id',
            'valid_subjects'
        ];
        $continueLists = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getContinueSemesterDataByStudentUidsAndOtherConds', [AssistantDesk_Data_CommonParams::$studentUids, $otherConds, $fields]);
        $continueData  = $continueLists[self::$studentUid] ?? [];
        //详情
        //$returnKey                 = "semesterContinueCommonDetailV1{$year}{$learnSeason}";
        $subjectIds = [];
        $gradeSubjectNames = [];
        foreach ($continueData as $cd) {
            //拼接年级和学科
            if (!isset($cd['valid_subjects']) || !is_array($cd['valid_subjects'])) {
                continue;
            }
            foreach ($cd['valid_subjects'] as $subject) {
                if (empty($subject)) {
                    continue;
                }
                if ($subjects && !in_array($subject, $subjects)) {
                    continue;
                }
                $subjectIds[] = $subject;
                $gradeSubjectNames[] = Zb_Const_GradeSubject::$GRADE[$cd['grade_id']] . AssistantDesk_Common_Keyconst::getSubject()[$subject];
            }
        }
        self::$student[$returnKey . "_num"] = count(array_unique($gradeSubjectNames));
        self::$student[$returnKey] = empty($gradeSubjectNames) ? '-' : implode(",", array_unique($gradeSubjectNames));
        self::$student[$returnKey . "_ids"] = $subjectIds; // 筛选
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年某学期某年级续报科目详情】',
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'     => '某学期续报科目详情，配置year，learnSeason、gradeIds和subjectIds下的续报详情字段valid_subjects'
        ]);
    }

    /**
     * 是否报名
     * @return bool|void
     * @throws ReflectionException
     */
    public static function getIsRegisterOrder() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $learnSeason = $fieldRule['serviceConfig']['learnSeason'] ?? '';
        $gradeIds    = $fieldRule['serviceConfig']['gradeIds'] ?? '';
        $subjects    = $fieldRule['serviceConfig']['subjectIds'] ?? '';
        $subjectMapping = $fieldRule['serviceConfig']['subjectMapping'] ?? '';
        $returnKey   = $fieldRule['key'];
        $gradeIds    = explode(',', $gradeIds);
        $subjects    = $subjects ? explode(',', $subjects) : [];
        $subjectMappings = explode(",", $subjectMapping) ?: [];
        $learnSeasons= explode(',', $learnSeason);
        if (empty($year) || empty($learnSeasons)|| empty($learnSeason)) {
            return false;
        }
        $subjectMap = self::extractListToMap($subjectMappings, "=");

        //计算续报学年、学季、学期
        $otherConds                 = [];
        $otherConds['learn_year']   = $year;
        $otherConds['learn_seasons']= $learnSeasons;
        $otherConds['gradeIds']     = $gradeIds;

        if (!empty($subjects)) {
            foreach ($subjects as $idx => $s) {
                if ($s <= 0) {
                    // 本学科
                    $courseInfo  = AssistantDesk_Course::getCourseInfo(AssistantDesk_Data_CommonParams::$courseId);
                    if (!empty($courseInfo) && !empty($courseInfo['mainSubjectId'])) {
                        $subjects[$idx] = $courseInfo['mainSubjectId'];
                    }
                }
                // 有映射
                if ($subjectMap[$subjects[$idx]]) {
                    $subjects[$idx] = $subjectMap[$subjects[$idx]];
                }
            }
        }

        $fields        = [
            'student_uid',
            'grade_id',
            'valid_subjects'
        ];
        $continueLists = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getContinueSemesterDataByStudentUidsAndOtherConds', [AssistantDesk_Data_CommonParams::$studentUids, $otherConds, $fields]);
        $continueData  = $continueLists[self::$studentUid] ?? [];
        //详情
        $subjectIds = [];
        foreach ($continueData as $cd) {
            //拼接年级和学科
            if (!isset($cd['valid_subjects']) || !is_array($cd['valid_subjects'])) {
                continue;
            }
            foreach ($cd['valid_subjects'] as $subject) {
                if (empty($subject)) {
                    continue;
                }
                if ($subjects && !in_array($subject, $subjects)) {
                    continue;
                }
                $subjectIds[] = $subject;
            }
        }
        self::$student[$returnKey] = empty($subjectIds) ? 0 : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年某学期某年级是否续报】',
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'     => '某学期续报科目详情，配置year，learnSeason、gradeIds和subjectIds下的续报详情字段valid_subjects'
        ]);
        return self::$student[$returnKey];
    }

    private static function extractListToMap($list, $delimiter) {
        $dataMap = [];
        if (empty($list)) {
            return $dataMap;
        }
        foreach ($list as $mapping) {
            $kv = explode($delimiter, $mapping);
            if (count($kv) != 2) {
                continue;
            }
            $dataMap[$kv[0]] = $kv[1];
        }
        return $dataMap;
    }

    /**
     * 指定学年学部学季是否预约-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1IsOrder() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeason = intval($fieldRule['serviceConfig']['learnSeason'] ?? 0);
        $subjectIds  = strval($fieldRule['serviceConfig']['subjectIds'] ?? '');
        $subjectIds  = $subjectIds ? explode(",", $subjectIds) : [];
        $returnKey   = $fieldRule['key'];
        if (empty($year) || empty($learnSeason) || empty($department)) {
            return;
        }


        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriod', [AssistantDesk_Data_CommonParams::$studentUids, $year, $department, $learnSeason]);

        $preOrder                  = $preOrders[self::$studentUid] ?? [];
        if (empty($preOrder) || empty($preOrder['reserve_grade_subject'])) {
            $preOrder = [];
        } else if (!empty($subjectIds)) {
            $preOrder = self::getMatchReserveValidSubjects($subjectIds, array($preOrder['reserve_grade_subject']));
        }

        self::$student[$returnKey] = empty($preOrder) ? 0 : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期是否预约】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学科匹配 reserve_grade_subject（预约学科），valid_grade_subject（续报学科）
     * @param $subjectIds [1,2]
     * @param $gradeSubjectsStrList ["1-1","2-2"...]
     * @return array
     */
    private static function getMatchReserveValidSubjects($subjectIds, $gradeSubjectsStrList) {
        if (empty($subjectIds) || empty($gradeSubjectsStrList)) {
            return [];
        }

        if (!is_array($subjectIds) && !empty($subjectIds)) {
            $subjectIds = array($subjectIds);
        }
        if (!is_array($gradeSubjectsStrList) && !empty($gradeSubjectsStrList)) {
            $gradeSubjectsStrList = array($gradeSubjectsStrList);
        }

        $allSubjectIds = [];
        foreach ($gradeSubjectsStrList as $gradeSubjectsStr) {
            $rvGradeSubjects = explode(",", $gradeSubjectsStr);
            $rvSubjectIds = [];
            foreach ($rvGradeSubjects as $gradeSubject) {
                $gs = explode("-", $gradeSubject);
                if (empty($gs)) {
                    continue;
                }
                $rvSubjectIds[$gs[1]] = $gs[1];
            }
            $rvSubjectIds = array_keys($rvSubjectIds);

            $allSubjectIds = array_merge($allSubjectIds, $rvSubjectIds);
        }

        $matchSubjects = [];
        foreach ($subjectIds as $subjectId) {
            if ($subjectId <= 0) {
                // 本学科
                $courseInfo  = AssistantDesk_Course::getCourseInfo(AssistantDesk_Data_CommonParams::$courseId);
                if (!empty($courseInfo)
                    && !empty($courseInfo['mainSubjectId'])
                    && in_array($courseInfo['mainSubjectId'], $allSubjectIds)) {
                    $matchSubjects[$courseInfo['mainSubjectId']] = $courseInfo['mainSubjectId'];
                }
            } else {
                if (in_array($subjectId, $allSubjectIds)) {
                    $matchSubjects[$subjectId] = $subjectId;
                }
            }
        }

        return array_keys($matchSubjects);
    }


    public static function suyangUser() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $studentUserTagList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsUONData',
            [AssistantDesk_Data_CommonParams::$studentUids]);

        $studentUserTag = $studentUserTagList[self::$studentUid] ?? [];
        self::$student["suyangUser"]=0;//新生
        if (!empty($studentUserTag)){
            self::$student["suyangUser"] = $studentUserTag['is_suyang_user']?1:0;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "suyangUser", [
            'title'  => '【素养学生】',
            'source' => 'es：adl_dim_student_user_info_v1.is_suyang_user',
            '解释'     => '素养学生，1:是,0:否'
        ]);
    }

    public static function subjectUser() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        $studentUserTagList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsUONData',
            [AssistantDesk_Data_CommonParams::$studentUids]);

        $studentUserTag = $studentUserTagList[self::$studentUid] ?? [];
        self::$student["subjectUser"]=0;//新生
        if (!empty($studentUserTag)){
            self::$student["subjectUser"] = $studentUserTag['is_subject_user']?1:0;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "subjectUser", [
            'title'  => '【正课学生】',
            'source' => 'es：adl_dim_student_user_info_v1.is_subject_user',
            '解释'     => '正课学生，1:是,0:否'
        ]);
    }

    /**
     * 指定学年学部学季是否预约-支持自定义配置[一次访问多学期]
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV2IsOrder() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule    = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year         = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department   = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons = explode(',', strval($fieldRule['serviceConfig']['learnSeasons']));
        $subjectIds  = strval($fieldRule['serviceConfig']['subjectIds'] ?? '');
        $subjectIds  = $subjectIds ? explode(",", $subjectIds) : [];
        $returnKey    = $fieldRule['key'];
        if (empty($year) || empty($learnSeasons) || empty($department)) {
            return;
        }

        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriods', [$year, $department, $learnSeasons, AssistantDesk_Data_CommonParams::$studentUids]);

        $preOrder                  = $preOrders[self::$studentUid] ?? [];
        $reserveGradeSubjects = array_column($preOrder, 'reserve_grade_subject');
        if (empty($preOrder) || empty($reserveGradeSubjects)) {
            $preOrder = [];
        } else if (!empty($subjectIds)) {
            $preOrder = self::getMatchReserveValidSubjects($subjectIds, $reserveGradeSubjects);
        }

        self::$student[$returnKey] = empty($preOrder) ? 0 : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期是否预约】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季多期次是否预约-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1IsOrderByBatch() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $returnKey   = $fieldRule['key'];

        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons= $fieldRule['serviceConfig']['learnSeason'] ?? '';
        $subjectIds  = strval($fieldRule['serviceConfig']['subjectIds'] ?? '');
        $subjectIds  = $subjectIds ? explode(",", $subjectIds) : [];
        $preOrders  = [];
        $learnSeasons= $learnSeasons ? explode(',', $learnSeasons) : [];
        foreach ($learnSeasons as $learnSeason) {
            $preOrder   = self::getPreOrderV1Info($year, $department, intval($learnSeason));
            if (empty($preOrder) || empty($preOrder['reserve_grade_subject'])) {
                continue;
            }
            if (!empty($subjectIds)) {
                $preOrder = self::getMatchReserveValidSubjects($subjectIds, array($preOrder['reserve_grade_subject']));
                if (!empty($preOrder)) {
                    $preOrders[]    = $preOrder;
                }
            } else {
                $preOrders[]    = $preOrder;
            }
        }

        self::$student[$returnKey] = empty($preOrders) ? 0 : 1;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、多学期是否预约】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 隔季二级续报且联报时间
     */
    public static function getL2rBoundSpaceSeasonPeriods(){
        $sourceField = 'spaceSeasonBoundDetail';
        $field = 'l2rBoundSpaceSeason';
        $timeField = $field."Periods";
        $detail = self::getL2rSpaceSeasonData($sourceField,$field);
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }

        self::$student[$timeField] = $detail[self::$studentUid][$timeField];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $timeField, [
            'title'  => '【隔季二级续报且联报学期】',
            'source' => 'es订单：字段：space_season_bound_detail获取有效订单中课程所属学期',
            '解释'     => '隔季二级续报且联报学期'
        ]);
    }

    /**
     * 获取预约单信息
     * @param $year
     * @param $department
     * @param $learnSeason
     * @return array|mixed
     * @throws ReflectionException
     */
    private static function getPreOrderV1Info($year, $department, $learnSeason) {
        if (empty($year) || empty($learnSeason) || empty($department)) {
            return [];
        }

        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriod', [AssistantDesk_Data_CommonParams::$studentUids, $year, $department, $learnSeason]);
        return $preOrders[self::$studentUid] ?? [];
    }

    /**
     * 指定学年学部学季预约科目-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1OrderSubject() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeason = intval($fieldRule['serviceConfig']['learnSeason'] ?? 0);
        $returnKey   = $fieldRule['key'];
        if (empty($year) || empty($learnSeason) || empty($department)) {
            return;
        }

        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriod', [AssistantDesk_Data_CommonParams::$studentUids, $year, $department, $learnSeason]);

        $preOrder = $preOrders[self::$studentUid] ?? [];
        $preOrder = empty($preOrder['reserve_grade_subject']) ? '' : $preOrder['reserve_grade_subject'];

        $gradeSubjects      = explode(',', $preOrder);
        $returnGradeSubject = [];
        foreach ($gradeSubjects as $gradeSubject) {
            list($grade, $subject) = explode('-', $gradeSubject);
            if (!empty($grade) && !empty($subject)) {
                $gradeName            = Zb_Const_GradeSubject::$GRADE[$grade];
                $subjectName          = AssistantDesk_Common_Keyconst::getSubject()[$subject];
                $returnGradeSubject[] = $gradeName . '-' . $subjectName;
            }
        }

        self::$student[$returnKey] = implode(',', $returnGradeSubject);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期预约科目】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季预约科目-支持自定义配置【一次获取多学期】
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV2OrderSubject() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule    = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year         = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department   = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons = explode(',', $fieldRule['serviceConfig']['learnSeasons']);
        $returnKey    = $fieldRule['key'];
        if (empty($year) || empty($learnSeasons) || empty($department)) {
            return;
        }

        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriods', [$year, $department, $learnSeasons, AssistantDesk_Data_CommonParams::$studentUids]);

        $preOrder = $preOrders[self::$studentUid] ?? [];
        $preOrder = empty($preOrder) ? '' : implode(',', array_column($preOrder, 'reserve_grade_subject'));

        $gradeSubjects      = explode(',', $preOrder);
        $gradeSubjects      = array_unique($gradeSubjects);
        $returnGradeSubject = [];
        $subjectIds = [];
        foreach ($gradeSubjects as $gradeSubject) {
            list($grade, $subject) = explode('-', $gradeSubject);
            if (!empty($grade) && !empty($subject)) {
                $gradeName            = Zb_Const_GradeSubject::$GRADE[$grade];
                $subjectName          = AssistantDesk_Common_Keyconst::getSubject()[$subject];
                $subjectIds[$subject] = $subject;
                $returnGradeSubject[] = $gradeName . '-' . $subjectName;
            }
        }

        self::$student[$returnKey . "_ids"] = array_keys($subjectIds); // 筛选
        self::$student[$returnKey] = implode(',', $returnGradeSubject);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期预约科目】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季多期次预约科目-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1OrderSubjectByBatch() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons= $fieldRule['serviceConfig']['learnSeason'] ?? '';
        $returnKey   = $fieldRule['key'];
        $returnGradeSubject = [];
        $learnSeasons= $learnSeasons ? explode(',', $learnSeasons) : [];
        foreach ($learnSeasons as $learnSeason) {
            $preOrder   = self::getPreOrderV1Info($year, $department, intval($learnSeason));
            $preOrder   = empty($preOrder['reserve_grade_subject']) ? '' : $preOrder['reserve_grade_subject'];
            $gradeSubjects      = explode(',', $preOrder);
            foreach ($gradeSubjects as $gradeSubject) {
                list($grade, $subject) = explode('-', $gradeSubject);
                if (!empty($grade) && !empty($subject)) {
                    $gradeName            = Zb_Const_GradeSubject::$GRADE[$grade];
                    $subjectName          = AssistantDesk_Common_Keyconst::getSubject()[$subject];
                    $returnGradeSubject[$gradeName . '-' . $subjectName] = $gradeName . '-' . $subjectName;
                }
            }
        }

        self::$student[$returnKey] = implode(',', array_keys($returnGradeSubject));
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、多学期预约科目】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季预约时间-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1IsTime() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeason = intval($fieldRule['serviceConfig']['learnSeason'] ?? 0);
        $returnKey   = $fieldRule['key'];
        if (empty($year) || empty($learnSeason) || empty($department)) {
            return;
        }

        $preOrders = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriod', [AssistantDesk_Data_CommonParams::$studentUids, $year, $department, $learnSeason]);

        $preOrder                  = $preOrders[self::$studentUid] ?? [];
        self::$student[$returnKey] = empty($preOrder['earliest_reserve_time']) ? '' : date("Y-m-d H:i:s", $preOrder['earliest_reserve_time']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期最早预约时间】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季预约时间-支持自定义配置【一次获取多学期】
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV2IsTime() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule    = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year         = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department   = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons = explode(',', $fieldRule['serviceConfig']['learnSeasons']);
        $returnKey    = $fieldRule['key'];
        if (empty($year) || empty($learnSeasons) || empty($department)) {
            return;
        }

        $preOrders                 = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getListByStudentIdsYearDepartmentPeriods', [$year, $department, $learnSeasons, AssistantDesk_Data_CommonParams::$studentUids]);
        self::$student[$returnKey] = '';
        $preOrder                  = $preOrders[self::$studentUid] ?? [];
        $preOrder                  = Tools_Array::sortByMultiCols($preOrder, array(
            'earliest_reserve_time' => SORT_ASC,
        ));
        self::$student[$returnKey] = empty($preOrder) ? '' : date("Y-m-d H:i:s", $preOrder[0]['earliest_reserve_time']);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、学期最早预约时间】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 指定学年学部学季多期次预约时间-支持自定义配置
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getPreOrderV1IsTimeByBatch() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule   = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $year        = intval($fieldRule['serviceConfig']['year'] ?? 0);
        $department  = intval($fieldRule['serviceConfig']['department'] ?? 0);
        $learnSeasons= $fieldRule['serviceConfig']['learnSeason'] ?? '';
        $returnKey   = $fieldRule['key'];
        $firstFirstTime = 0;
        $learnSeasons= $learnSeasons ? explode(',', $learnSeasons) : [];
        foreach ($learnSeasons as $learnSeason) {
            $preOrder   = self::getPreOrderV1Info($year, $department, intval($learnSeason));
            if (!empty($preOrder['earliest_reserve_time'])) {
                $firstTime  = $preOrder['earliest_reserve_time'];
                if ((0 == $firstFirstTime) || ($firstTime <= $firstFirstTime)) {
                    $firstFirstTime   = $firstTime;
                }
            }
        }
        self::$student[$returnKey] = empty($firstFirstTime) ? '' : date("Y-m-d H:i:s", $firstFirstTime);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title'  => '【某学年、学部、多学期最早预约时间】',
            'source' => 'es：dataware_trade_student_season_department_agg',
            '解释'     => '预约学科：reserve_grade_subject，首次预约时间：earliest_reserve_time'
        ]);
    }

    /**
     * 根据当前学年，学季，年级，学季期次，下一个学季是否为第一学季 计算下一学季的学年，学季，年级，学季期次
     * 1、可计算下一季 下一季也需要传入下一期为第一期
     * 2、也可计算下一期，下一期需要传入下一期是否为第一期
     * 3、也可以计算隔季，传入$posSeason 为
     * @param $currentYear int 当前学年
     * @param $currentSeason int  当前学季
     * @param $currentGrade int  当前年级
     * @param $currentLearnSeason int  当前学季期次
     * @param $nextIsFirstSemester int|bool 下一个学季是否是第一学期
     * @param $posSeason int 下一个学季的间隔，如果隔一个学季则posSeason 为2，默认为1
     * @return array
     */
    public static function getYearSeasonByNextLearnSeason($currentYear, $currentSeason, $currentGrade, $currentLearnSeason, $nextIsFirstSemester = 1, $posSeason = 1) {
        static $nextMap = [];
        $key = $currentYear-$currentSeason-$currentGrade-$currentLearnSeason-$nextIsFirstSemester-$posSeason;
        if (isset($nextMap[$key])) {
            return $nextMap[$key];
        }
        $nextLearnSemesterSeason = $currentSeason;
        $semesterNextLearnSeason = $currentLearnSeason;
        $nextLearnSemesterGradeid = $currentGrade;
        $nextLearnSemesterYear    = $currentYear;
        if ($nextIsFirstSemester) {
            $posSeason = $posSeason % 4;
            $passSeasonList = [];
            for($i = 1; $i <= $posSeason; $i++) {
                $nextLearnSemesterSeason = AssistantDesk_Season::$renewSeasonMap[$nextLearnSemesterSeason];
                $passSeasonList[] = $nextLearnSemesterSeason;
            }
            // 第一个学期 升学季
            if (in_array(Zb_Const_LearnSeason::LEARN_SEASON_WINTER, $passSeasonList)) {
                // 寒季为每个学年的第一个学季
                // 升学年
                $nextLearnSemesterYear = $currentYear + 1;
            }
            if (in_array(Zb_Const_LearnSeason::LEARN_SEASON_AUTUMN, $passSeasonList)) {
                // 秋季为每个年级的第一个学季
                // 升年级
                $nextLearnSemesterGradeid = Const_Common::$gradeUpper[$currentGrade];
            }
            // 学季期次为 下一个学季第一期
            $semesterNextLearnSeason = $nextLearnSemesterSeason . '1';
        } else {
            // 下一个学期=学期+1
            $semesterNextLearnSeason++;
        }

        $nextMap[$key] = [$nextLearnSemesterYear, $nextLearnSemesterSeason, $nextLearnSemesterGradeid, intval($semesterNextLearnSeason)];
        return $nextMap[$key];
    }
    /**
     * * 获取续报查询条件
     *
     * @param int $continueType 续报类型：1-学季，2-隔季，3-学期，4-隔期
     * @param int $continueYear
     * @param int $continueSeason
     * @param int $continueLearnSeason
     *
     * @return array
     * @throws ReflectionException
     */
    private static function _getContinueConds($continueType = 0, $continueYear = 0, $continueSeason = 0, $continueLearnSeason = 0)
    {
        //获取当前课程学季、学期
        $courseInfo    = Common_Singleton::getInstanceData(AssistantDesk_Course::class,"getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $year = $courseInfo['year'];
        $season = $courseInfo['season'];

        //计算续报学年、学季、学期
        $cond = [];
        if (in_array($continueType,[AssistantDesk_Season::CONTINUE_TYPE_LEARN_SEASON,AssistantDesk_Season::CONTINUE_TYPE_SPACE_LEARN_SEASON])) {
            $cond['learn_year'] = $continueYear ?: $year;
            $cond['season'] = $continueSeason ?: $season;
            $cond['learn_season'] = $continueLearnSeason;
        }
        //todo 隔季续报条件待确定
        if (in_array($continueType,[AssistantDesk_Season::CONTINUE_TYPE_SEASON,AssistantDesk_Season::CONTINUE_TYPE_SPACE_SEASON])) {
            $cond['learn_year'] = $continueYear ?: $year;
            $cond['season'] = $continueSeason ?: $season;
        }

        return $cond;
    }

    public static function getUserRole() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $userRoles = Common_Singleton::getInstanceData(Api_UserRole::class, 'getUserRoleIdsByBatch', [AssistantDesk_Data_CommonParams::$studentUids]);
        $userRole  = $userRoles[self::$studentUid] ?? [];
        $roleConfig = Common_Singleton::getInstanceData(Util_Config_Get::class, 'getJsonCfg', ['user_role_config', []]);
        if (!empty($roleConfig)) {
            foreach ($roleConfig as $_key => $_val) {
                $_arrRole = $_val['value'] ?? [];
                if (empty($_arrRole) || !is_array($_arrRole)) {
                    //角色配置里的角色为空，则代表任意角色都满足条件
                    $satisfy = true;
                } else if (empty($userRole) || !is_array($userRole)) {
                    $satisfy = false;
                } else {
                    $satisfy = array_intersect($userRole, $_arrRole) ? true : false;
                }
                self::$student['userrole_' . $_key] = $satisfy ? 1 : 0;
                AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'userrole_' . $_key, [
                    'title'  => "【{$_val['name']}】",
                    'source' => 'es：adl_trade_student_period_grade_v1',
                    '解释'     => "接口获取角色列表：/userrole/coreapi/batchgetuserroles，当前字段角色id为：" . implode(',', $_arrRole),
                ]);

            }
        }
    }
    public static function getUserRoleByConf() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $roleIdsStr = strval($fieldRule['serviceConfig']['roleIds'] ?? 0);
        $notRoleIds = strval($fieldRule['serviceConfig']['notRoleIds'] ?? 0);
        $fieldKey = strval($fieldRule['key'] ?? '');
        $roleIdsArr = [];
        $notRoleIdsArr = [];
        $roleIdsStr && $roleIdsArr = explode(',', $roleIdsStr);
        $notRoleIds && $notRoleIdsArr = explode(',', $notRoleIds);
        $userRoles = Common_Singleton::getInstanceData(Api_UserRole::class, 'getUserRoleIdsByBatch', [AssistantDesk_Data_CommonParams::$studentUids]);
        $userRole  = $userRoles[self::$studentUid] ?? [];
        $satisfy = array_intersect($userRole, $roleIdsArr);
        $satisfyNot = array_intersect($userRole, $notRoleIdsArr);
        self::$student[$fieldKey] = ($satisfy && !$satisfyNot) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title'  => "【" . $fieldRule['feConfig']['label'] ?? '' . "】",
            'source' => 'es：adl_trade_student_period_grade_v1',
            '解释'     => "接口获取角色列表：/userrole/coreapi/batchgetuserroles，当前字段角色id为：" . $roleIdsStr,
        ]);
    }

    /**
     * 根据角色id，展示不同的角色定义
     *
     * @return bool
     * @throws ReflectionException
     */
    public static function getUserRoleIdByConf() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $roleIdsStr = strval($fieldRule['serviceConfig']['roleIds'] ?? 0);
        $roleIdsArr = [];
        $roleIdsStr && $roleIdsArr = explode(',', $roleIdsStr);
        $roleDefault = intval($fieldRule['serviceConfig']['roleDefault'] ?? 0);
        $fieldKey = strval($fieldRule['key'] ?? '');
        $userRoles = Common_Singleton::getInstanceData(Api_UserRole::class, 'getUserRoleIdsByBatch', [AssistantDesk_Data_CommonParams::$studentUids]);
        $userRole  = $userRoles[self::$studentUid] ?? [];
        $role = $roleDefault;
        foreach ($roleIdsArr as $roleId) {
            if (is_array($userRole) && in_array($roleId,$userRole)) {
                $role = $roleId;
            }
        }
        self::$student[$fieldKey] = $role;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title'  => "【" . $fieldRule['feConfig']['label'] ?? '' . "】",
            'source' => '/userrole/coreapi/batchgetuserroles',
            '解释'     => "接口获取角色列表：/userrole/coreapi/batchgetuserroles，当前字段角色id为：" . $fieldRule['serviceConfig']['roleIds'],
        ]);
    }

    /**
     * 直播到课状态(跟课详情反馈）
     */
    public static function getAttendStatusByDuration(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["inclass_teacher_room_attend_duration"])) {
            return true;
        }
        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $attend = 0;
        if($esLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] && $esLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] < 1800){
            $attend = 1;
        }
        if($esLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] && $esLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'] >= 1800){
            $attend = 2;
        }
        self::$student['attendStatusByDuration'] = $attend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'attendStatusByDuration', [
            'title' => '【直播到课状态】',
            '数据源'   => 'es.common_lu.dataware_idl_common_lesson_student_v1.inclass_teacher_room_attend_duration',
            '解释' => '2-已到课超30分钟，1-已参课不足30分钟，0-未到课(指听课时长是0分钟)',
        ]);
    }

    /**
     * 获取最近一次看回放结束时间
     */
    public static function getLastPlaybackTimepoint(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['last_playback_timepoint'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        $lastPlaybackTimepoint = $esCuData[self::$studentUid]['last_playback_timepoint'];
        self::$student['lastPlaybackTimepoint'] = $lastPlaybackTimepoint ? date('Y-m-d H:i:s', $lastPlaybackTimepoint) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lastPlaybackTimepoint', [
            'title' => '【获取最近一次看回放结束时间】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段last_playback_timepoint',
        ]);
    }

    /**
     * 获取核心课章节直播5分钟到课累计次数
     */
    public static function getAttendMainLesson5MinutesNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['attend_5minute_main_lesson_num'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        self::$student['attendMainLesson5MinutesNum'] = intval($esCuData[self::$studentUid]['attend_5minute_main_lesson_num']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'AttendMainLesson5MinutesNum', [
            'title' => '【获取核心课章节直播5分钟到课累计次数】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, attend_5minute_main_lesson_num',
        ]);
    }

    /**
     * 获取核心章节5分钟到课章节数(直播+回放)
     */
    public static function getAttendOrContentMainLesson5MinutesNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['attend_or_content_view_5minute_main_lesson_num'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        self::$student['attendOrContentMainLesson5MinutesNum'] = intval($esCuData[self::$studentUid]['attend_or_content_view_5minute_main_lesson_num']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'AttendOrContentMainLesson5MinutesNum', [
            'title' => '【获取核心课章节直播5分钟到课累计次数】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, attend_or_content_view_5minute_main_lesson_num',
        ]);
    }

    /**
     * 获取核心章节观看完课章节数(直播+回放)
     */
    public static function getAttendOrContentMainLessonNum(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['attend_or_content_view_three_four_main_lesson_num'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        self::$student['attendOrContentMainLessonNum'] = intval($esCuData[self::$studentUid]['attend_or_content_view_three_four_main_lesson_num']);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'AttendOrContentMainLessonNum', [
            'title' => '【获取核心课章节直播5分钟到课累计次数】',
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, attend_or_content_view_three_four_main_lesson_num',
        ]);
    }

    /**
     * 最近一次看回放时长
     */
    public static function getLastPlaybackDuration(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['last_playback_duration'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $lastPlaybackDuration = intval($esCuData[self::$studentUid]['last_playback_duration']);
        self::$student['lastPlaybackDuration'] = floor($lastPlaybackDuration/60) . '分钟' . $lastPlaybackDuration%60 . '秒';
        self::$student['lastPlaybackDurationFilter'] = $lastPlaybackDuration;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lastPlaybackDuration', [
            'title' => '【最近一次看回放时长】',
            '数据源'   => 'ES CU 维度数据：dataware_idl_common_course_student, 字段last_playback_duration',
        ]);
    }

    /**
     * 最近一次看回放章节
     */
    public static function getLastPlaybackLessonId(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['last_playback_lesson_id'])) {
            return true;
        }
        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");;
        $lessonId = intval($esCuData[self::$studentUid]['last_playback_lesson_id']);
        $lesson   = Common_Singleton::getInstanceData(Api_Dal::class, "getLessonBaseByLessonIds", [ $lessonId, ['lessonName'] ]);

        $lessonInfo = $lesson['lessonName'] . '(' . $lessonId . ')';
        self::$student['lastPlaybackLessonId'] = $lessonInfo; // 改

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lastPlaybackLessonId', [
            'title' => '【最近一次看回放章节】', // 改
            '数据源'   => 'ES CU 维度数据：idl_course_student_assistant, 字段last_playback_lesson_id',
        ]);
    }

    /**
     * 首讲是否完课
     */
    public static function getFirstEndLecture(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['view_finish_total_playback_three_five_lesson_num_v1'])) {
            return true;
        }
        $esCuData     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");;
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $returnKey = $fieldRule['key'];
        self::$student[$returnKey] = $esCuData[self::$studentUid]['view_finish_total_playback_three_five_lesson_num_v1'] > 0 ? 1: 0; // 改

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $returnKey, [
            'title' => '首讲是否完课', // 改
            '数据源'   => 'ES CU 维度数据：dataware_idl_common_course_student, 字段view_finish_total_playback_three_five_lesson_num_v1 > 0 表示首讲完课',
        ]);
        return self::$student[$returnKey];
    }

    public static function getIsInclassTeacherRoomContentView5Mintue() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["is_inclass_teacher_room_content_view_5minute"])) {
            return [];
        }
        $esLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);
        self::$student['isInclassTeacherRoomContentView5Mintue'] = $esLessonData[self::$studentUid]['is_inclass_teacher_room_content_view_5minute'] ? '是' : '否';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isInclassTeacherRoomContentView5Mintue', [
            'title' => '【录播lbp是否内容观看到课_5min】', // 改
            '数据源'   => 'es.common_lu.dataware_idl_common_lesson_student_v1.is_inclass_teacher_room_content_view_5minute',
        ]);
    }

    const DEER_DEVICE_TYPE_PC = [
        1 => "Windows",
        2 => "Mac",
    ];
    const DEER_DEVICE_TYPE_PAD = [
        3 => "IPad",
        4 => "AndroidPad",
    ];
    const DEER_DEVICE_TYPE_PHONE = [
        5 => "IPhone",
        6 => "Android",
        7 => "其他",
    ];

    /**
     * 小鹿编程硬件绑定状态
     * 0 未绑定、1 已绑定
     * https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=464603917
     * @return bool|mixed|string
     * @throws ReflectionException
     */
    public static function getDeerProgrammingHardwareBindStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddUONFields(['student_uid', 'deer_programming_hardware_bind_status'])) {
            return true;
        }

        $studentDeviceList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsUONData", [AssistantDesk_Data_CommonParams::$studentUids]);

        self::$student['deerProgrammingHardwareBindStatus'] = $studentDeviceList[self::$studentUid]['deer_programming_hardware_bind_status'] ?? '-1';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'deerProgrammingHardwareBindStatus', [
            'title' => '【硬件绑定状态	】',
            '数据源'   => 'es：adl_dim_student_user_info，字段取值：deer_programming_hardware_bind_status',
        ]);
    }

    /**
     * 小鹿编程代码运行次数tid维度列表
     * 0 未绑定、1 已绑定
     * https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=464603917
     * @return bool|mixed|string
     * @throws ReflectionException
     */
    public static function getDeerLessonInteractDetails() {
        if (\AssistantDesk_Data_DataSource::beforeAddLiveInteractQuestionFields(['interact_id', 'code_upload_success_cnt', 'code_load_success_cnt'])) {
            return true;
        }

        $deerLessonInteractList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLiveInteractQuestionData", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]);
        $studentLessonInteractList = empty($deerLessonInteractList[self::$studentUid]) ? [] : $deerLessonInteractList[self::$studentUid];

        $executeCnt = 0;

        $deerLessonInteractDetails = [];
        foreach ($studentLessonInteractList as $studentLessonInteract) {
            $deerLessonInteractDetails[] = [
                'interactId' => $studentLessonInteract['interact_id'],
                'executeCnt' => $studentLessonInteract['code_load_success_cnt'],
                'uploadCnt' => $studentLessonInteract['code_upload_success_cnt'],
            ];
            $executeCnt += $studentLessonInteract['code_load_success_cnt'];
        }

        array_multisort(array_column($deerLessonInteractDetails, 'interactId'), SORT_ASC, $deerLessonInteractDetails);

        foreach($deerLessonInteractDetails as $key => $deerLessonInteractDetail) {
            $deerLessonInteractDetails[$key]['numberId'] = $key+1;
        }

        self::$student['deerLessonInteractExecuteCnt'] = $executeCnt;
        self::$student['deerLessonInteractDetails'] = $deerLessonInteractDetails;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'deerLessonInteractDetails', [
            'title' => '【代码运行次数	】',
            '数据源'   => 'es：adl_deer_livestream_interact_question_l1，字段取值：interact_id, code_load_success_cnt, code_upload_success_cnt',
        ]);
    }

    /**
     * 获取用户指法练习学习表现
     * 分子：用户指法练习通过章节数
     * 分母：用户指法练习获取星星数
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getDeerFingeringFinishCnt() {
        if (\AssistantDesk_Data_DataSource::beforeAddUONFields(['student_uid'])) {
            return true;
        }

        $studentList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsUONData", [AssistantDesk_Data_CommonParams::$studentUids]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'deerFingeringFinishCnt', [
            'title' => '【完成二级目录数/完成星星数（指法练习）】',
            '数据源'   => 'es：adl_dim_student_user_info_v1，deer_fingering_l2_directory_finish_cnt（完成二级目录数）/deer_fingering_star_finish_cnt（完成星星数）',
        ]);

        $deerFingeringL2DirectoryFinishCnt = empty($studentList[self::$studentUid]) ? "0" : $studentList[self::$studentUid]['deer_fingering_l2_directory_finish_cnt'];
        $deerFingeringStarFinishCnt = empty($studentList[self::$studentUid]) ? "0" : $studentList[self::$studentUid]['deer_fingering_star_finish_cnt'];
        $deerFingeringFinishCnt = $deerFingeringL2DirectoryFinishCnt . '/' . $deerFingeringStarFinishCnt;
        Bd_Log::notice("getDeerFingeringFinishCnt:studentUid[" . self::$studentUid . "]:deerFingeringFinishCnt[{$deerFingeringFinishCnt}]");
        self::$student['deerFingeringFinishCnt'] = $deerFingeringFinishCnt;
        return $deerFingeringFinishCnt;
    }

    /**
     * 用户指法练习通过章节数
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getDeerFingeringDirFinishCnt() {
        if (\AssistantDesk_Data_DataSource::beforeAddUONFields(['student_uid'])) {
            return true;
        }

        $studentList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsUONData", [AssistantDesk_Data_CommonParams::$studentUids]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'deerFingeringDirFinishCnt', [
            'title' => '【完成二级目录数（指法练习）】',
            '数据源'   => 'es：adl_dim_student_user_info_v1，deer_fingering_l2_directory_finish_cnt完成二级目录数',
        ]);

        $deerFingeringDirFinishCnt = empty($studentList[self::$studentUid]) ? "0" : $studentList[self::$studentUid]['deer_fingering_l2_directory_finish_cnt'];
        Bd_Log::notice("getDeerFingeringDirFinishCnt:studentUid[" . self::$studentUid . "]:deerFingeringDirFinishCnt[{$deerFingeringDirFinishCnt}]");
        self::$student['deerFingeringDirFinishCnt'] = $deerFingeringDirFinishCnt;
        return $deerFingeringDirFinishCnt;
    }

    /**
     * 0=连接失败，1=连接成功，2=重新连接，(99=未发起连接；默认处理)
     * 小鹿用户硬件设备连接状态
     * @return bool|mixed|string
     * @throws ReflectionException
     */
    public static function getDeerBluetoothConnectStatus() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'bluetooth_connect_status'])) {
            return true;
        }

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'deerBluetoothConnectStatus', [
            'title' => '【硬件连接状态（小鹿）】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，bluetooth_connect_status 蓝牙连接状态',
        ]);

        $deerBluetoothConnectStatus = "99";
        if (isset($commonLuDataList[self::$studentUid]) && isset($commonLuDataList[self::$studentUid]['bluetooth_connect_status'])) {
            $deerBluetoothConnectStatus = $commonLuDataList[self::$studentUid]['bluetooth_connect_status'];
        }
        Bd_Log::notice("getDeerBluetoothConnectStatus:studentUid[" . self::$studentUid . "]:deerBluetoothConnectStatus[{$deerBluetoothConnectStatus}]");
        self::$student['deerBluetoothConnectStatus'] = $deerBluetoothConnectStatus;
        return $deerBluetoothConnectStatus;
    }

    /**
     * ai到课进度
     */
    public static function getContentView10sLessonNum() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['content_view_10s_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $contentView10sLessonNum = intval($commonCuData[self::$studentUid]['content_view_10s_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;


        self::$student['contentViewLessonPercent'] = '0%';
        self::$student['contentViewLessonPercentFilter'] = 0;
        if ($unLockLessonNum != 0) {
            $contentViewLessonPercent = round($contentView10sLessonNum  * 100  / $unLockLessonNum, 2);
            self::$student['contentViewLessonPercent'] = $contentViewLessonPercent . '%';
            self::$student['contentViewLessonPercentFilter'] = $contentViewLessonPercent;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai到课进度】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，content_view_10s_lesson_num / unlock_lesson_num ',
        ]);
    }

    /**
     * ai到课解锁情况
     */
    public static function getContentViewSituation() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['content_view_10s_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $contentView10sLessonNum = intval($commonCuData[self::$studentUid]['content_view_10s_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;
        $contentViewSituation = $contentView10sLessonNum . '|' . $unLockLessonNum;
        self::$student['contentViewSituation'] = $contentViewSituation;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai到课解锁情况】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，content_view_10s_lesson_num / unlock_lesson_num ',
        ]);
    }

    /**
     * AI完课进度
     */
    public static function getContentViewFinishLessonPersent() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['content_view_finish_85percent_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $contentViewFinishLessonNum = intval($commonCuData[self::$studentUid]['content_view_finish_85percent_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;

        self::$student['contentViewFinishPersent'] = '0%';
        self::$student['contentViewFinishPersentFilter'] = 0;
        if ($unLockLessonNum != 0) {
            $contentViewFinishPersent = round($contentViewFinishLessonNum * 100 / $unLockLessonNum, 2);
            self::$student['contentViewFinishPersent'] = $contentViewFinishPersent . '%';
            self::$student['contentViewFinishPersentFilter'] = $contentViewFinishPersent;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai课完课进度】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，content_view_finish_85percent_lesson_num	 / unlock_lesson_num ',
        ]);
    }

    /**
     * ai课完课解锁情况
     */
    public static function getContentViewFinishSituation() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['content_view_finish_85percent_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $contentViewFinishLessonNum = intval($commonCuData[self::$studentUid]['content_view_finish_85percent_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;
        self::$student['contenViewFinishSituation'] = $contentViewFinishLessonNum . '|' . $unLockLessonNum;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai课完课解锁情况】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，content_view_finish_85percent_lesson_num | unlock_lesson_num ',
        ]);
    }

    /**
     * ai课整体完课进度
     */
    public static function getOverallFinishLessonPersent() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['overall_finish_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $overallFinishLessonNum = intval($commonCuData[self::$studentUid]['overall_finish_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;

        self::$student['overallFinishLessonPersent'] = '0%';
        self::$student['overallFinishLessonPersentFilter'] = 0;
        if ($unLockLessonNum != 0) {
            $overallFinishLessonPersent = round($overallFinishLessonNum * 100 / $unLockLessonNum, 2);
            self::$student['overallFinishLessonPersent'] = $overallFinishLessonPersent . '%';
            self::$student['overallFinishLessonPersentFilter'] = $overallFinishLessonPersent;
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai课整体完课进度】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，overall_finish_lesson_num | unlock_lesson_num ',
        ]);
    }

    /**
     * ai课整体完课解锁情况
     */
    public static function getOverallFinishSituation() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['overall_finish_lesson_num', 'unlock_lesson_num'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData", [AssistantDesk_Data_CommonParams::$lessonId]);
        $overallFinishLessonNum = intval($commonCuData[self::$studentUid]['overall_finish_lesson_num']) ?? 0;
        $unLockLessonNum = intval($commonCuData[self::$studentUid]['unlock_lesson_num']) ?? 0;
        self::$student['overallFinishSituation'] = $overallFinishLessonNum . '|' . $unLockLessonNum;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'contentViewLessonPercent', [
            'title' => '【ai课整体完课解锁情况】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，overall_finish_lesson_num | unlock_lesson_num ',
        ]);
    }

    /**
     * 章节报告
     */
    public static function getAiIsSubmitLessonWork() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid", "is_submit_lesson_work"])) {
            return true;
        }
        $commonStudentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $studentReportUrl = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getReportUrlData", [AssistantDesk_Data_CommonParams::$lessonId]);
        self::$student['isSubmitLessonWork'] = $commonStudentLessonData[self::$studentUid]['is_submit_lesson_work'] ?? 0;
        self::$student['lessonReportUrl'] = $commonStudentLessonData[self::$studentUid]['is_submit_lesson_work'] ?
            Api_Su::getShortUrl($studentReportUrl[self::$studentUid]['lessonReportUrl']) : ''; // 章节报告提供后需要修改

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'lessonReportUrl', [
            'title' => '【章节作品是否提交】',
            '数据源'   => 'es：dataware_idl_common_lesson_student_v1，is_submit_lesson_work 和接口：/writereport/api/url',
        ]);
    }

    /**
     * 是否生成阶段报告
     */
    public static function getAiIsSubmitCourseWork() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['is_generate_course_report'])) {
            return true;
        }
        $commonCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");
        $studentReportUrl = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getReportUrlData", [AssistantDesk_Data_CommonParams::$courseId]);
        $isSubmitCourseReport = $commonCuData[self::$studentUid]['is_generate_course_report'];
        self::$student['isSubmitCourseReport'] = $isSubmitCourseReport ?? 0;
        self::$student['courseReportUrl'] = $isSubmitCourseReport ? Api_Su::getShortUrl($studentReportUrl[self::$studentUid]['unitReportUrl']) : '';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'courseReportUrl', [
            'title' => '【阶段作品是否提交】',
            '数据源'   => 'es：dataware_idl_common_course_student_v1，is_generate_course_report 和接口：/writereport/api/url',
        ]);
    }

    /**
     * ai课是否整体完课
     */
    public static function getIsOverallFinish() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid", "is_submit_lesson_work", "is_inclass_teacher_room_content_view_finish_85percent"])) {
            return true;
        }
        $commonStudentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);
        $isSubmitLessonWork = $commonStudentLessonData[self::$studentUid]['is_submit_lesson_work'] ?? 0;
        $isFinish = $commonStudentLessonData[self::$studentUid]['is_inclass_teacher_room_content_view_finish_85percent'] ?? 0;
        self::$student['isOverallFinish'] = 0;
        if ($isSubmitLessonWork && $isFinish) {
            self::$student['isOverallFinish'] = 1;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isOverallFinish', [
            'title' => '【ai课是否整体完课】',
            '数据源'   => 'es：dataware_idl_common_lesson_student_v1，is_submit_lesson_work && is_inclass_teacher_room_content_view_finish_85percent',
        ]);
    }

    /**
     * ai课观看时长
     */
    public static function getPlayContentTime() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid", "inclass_teacher_room_total_playback_content_time"])) {
            return true;
        }
        $commonStudentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);
        $playContentTime = $commonStudentLessonData[self::$studentUid]['inclass_teacher_room_total_playback_content_time'] ?? 0;
        self::$student['playContentTime'] = $playContentTime % 60 > 0 ? floor($playContentTime / 60) .'min'.($playContentTime % 60).'s' : floor($playContentTime / 60) .'min';
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'playContentTime', [
            'title' => '【ai课观看时长】',
            '数据源'   => 'es：dataware_idl_common_lesson_student_v1，inclass_teacher_room_total_playback_content_time',
        ]);
    }

    /**
     * AI课是否已解锁
     */
    public static function getIsAILessonUnLock() {
        $dasLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);
        $dasInfo = $dasLessonData[self::$studentUid][AssistantDesk_Data_CommonParams::$lessonId];
        $stratTime = $dasInfo['startTime'];
        self::$student['isAILessonUnLock'] = $stratTime <= time() ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isAILessonUnLock', [
            'title' => '【AI章节是否已解锁】',
            '数据源'   => 'es：das，startTime',
        ]);
    }

    /*
     * AI课课前学生观看时长
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getAIInteractBeforeClassAttendDuration() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'beforeclass_video_content_time'])) {
            return true;
        }

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'aiInteractBeforeClassAttendDuration', [
            'title' => '【AI课课前学生观看时长】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，beforeclass_video_content_time AI课课前学生观看时长',
        ]);

        $aiInteractBeforeClassAttendDurationValue = empty($commonLuDataList[self::$studentUid])
            ? 0
            : (isset($commonLuDataList[self::$studentUid]['beforeclass_video_content_time'])
                ? $commonLuDataList[self::$studentUid]['beforeclass_video_content_time']
                : 0);
        $aiInteractBeforeClassAttendDuration = empty($commonLuDataList[self::$studentUid])
            ? "-"
            : (isset($commonLuDataList[self::$studentUid]['beforeclass_video_content_time'])
                ? AssistantDesk_Tools::formatDurationTime($commonLuDataList[self::$studentUid]['beforeclass_video_content_time'])
                : "-");
        Bd_Log::notice("getAIInteractBeforeClassAttendDuration:studentUid[" . self::$studentUid . "]:aiInteractBeforeClassAttendDuration[{$aiInteractBeforeClassAttendDuration}]");
        self::$student['aiInteractBeforeClassAttendDuration'] = $aiInteractBeforeClassAttendDuration;
        self::$student['aiInteractBeforeClassAttendDurationValue'] = $aiInteractBeforeClassAttendDurationValue;
        return $aiInteractBeforeClassAttendDuration;
    }

    /**
     * AI课课中学生观看时长
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getAIInteractInClassAttendDuration() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'inclass_teacher_room_total_playback_content_time'])) {
            return true;
        }

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'aiInteractInClassAttendDuration', [
            'title' => '【AI课课中学生观看时长】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，inclass_teacher_room_total_playback_content_time AI课课中学生观看时长',
        ]);

        $aiInteractInClassAttendDurationValue = empty($commonLuDataList[self::$studentUid])
            ? 0
            : (isset($commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_content_time'])
                ? $commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_content_time']
                : 0);
        $aiInteractInClassAttendDuration = empty($commonLuDataList[self::$studentUid])
            ? "-"
            : (isset($commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_content_time'])
                ? AssistantDesk_Tools::formatDurationTime($commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_content_time'])
                : "-");
        Bd_Log::notice("getAIInteractInClassAttendDuration:studentUid[" . self::$studentUid . "]:aiInteractInClassAttendDuration[{$aiInteractInClassAttendDuration}]");
        self::$student['aiInteractInClassAttendDuration'] = $aiInteractInClassAttendDuration;
        self::$student['aiInteractInClassAttendDurationValue'] = $aiInteractInClassAttendDurationValue;
        return $aiInteractInClassAttendDuration;
    }

    /**
     * AI课课后学生观看时长
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getAIInteractAfterClassAttendDuration() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'afterclass_video_content_time'])) {
            return true;
        }

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'aiInteractAfterClassAttendDuration', [
            'title' => '【AI课课后学生观看时长】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，afterclass_video_content_time AI课课后学生观看时长',
        ]);

        $aiInteractAfterClassAttendDurationValue = empty($commonLuDataList[self::$studentUid])
            ? 0
            : (isset($commonLuDataList[self::$studentUid]['afterclass_video_content_time'])
                ? $commonLuDataList[self::$studentUid]['afterclass_video_content_time']
                : 0);
        $aiInteractAfterClassAttendDuration = empty($commonLuDataList[self::$studentUid])
            ? "-"
            : (isset($commonLuDataList[self::$studentUid]['afterclass_video_content_time'])
                ? AssistantDesk_Tools::formatDurationTime($commonLuDataList[self::$studentUid]['afterclass_video_content_time'])
                : "-");
        Bd_Log::notice("getAIInteractAfterClassAttendDuration:studentUid[" . self::$studentUid . "]:aiInteractAfterClassAttendDuration[{$aiInteractAfterClassAttendDuration}]");
        self::$student['aiInteractAfterClassAttendDuration'] = $aiInteractAfterClassAttendDuration;
        self::$student['aiInteractAfterClassAttendDurationValue'] = $aiInteractAfterClassAttendDurationValue;
        return $aiInteractAfterClassAttendDuration;
    }

    /**
     * 计划录播累计到课数
     * @throws ReflectionException
     */
    public static function getGjkPlanLuboAttendNum() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['student_uid', 'inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num', 'mandatory_lbp_lesson_num'])) {
            return true;
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        $aiLessonNum = intval($esCuData[self::$studentUid]['inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num']);

        $mustLessonCnt = intval($esCuData[self::$studentUid]['mandatory_lbp_lesson_num']);

        self::$student['gjkPlanLuboAttendNum'] = $aiLessonNum . '/' . $mustLessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'gjkPlanLuboAttendNum', [
            'title' => '【计划录播累计到课数】',
            '数据源'   => 'es：公共数仓cu，分子：inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num，分母：mandatory_lbp_lesson_num',
        ]);
    }

    /**
     * 计划录播累计到课百分比
     * @throws ReflectionException
     */
    public static function getGjkPlanLuboAttendNumPercent() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['student_uid', 'inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num', 'mandatory_lbp_lesson_num'])) {
            return true;
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        $aiLessonNum = intval($esCuData[self::$studentUid]['inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num']);

        $mustLessonCnt = intval($esCuData[self::$studentUid]['mandatory_lbp_lesson_num']);

        if ($mustLessonCnt == 0) {
            self::$student['gjkPlanLuboAttendNumPercent'] = 0;
        } else {
            self::$student['gjkPlanLuboAttendNumPercent'] = round($aiLessonNum / $mustLessonCnt, 2) * 100;
        }
        self::$student['gjkPlanLuboAttendNumPercentDisplay'] = self::$student['gjkPlanLuboAttendNumPercent'] . '%';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'gjkPlanLuboAttendNumPercentDisplay', [
            'title' => '【计划录播累计到课数百分比】',
            '数据源'   => 'es：公共数仓cu，分子：inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num，分母：mandatory_lbp_lesson_num',
        ]);
    }

    /**
     * 计划录播累计完课数
     * @throws ReflectionException
     */
    public static function getGjkPlanLuboCompleteNum() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['student_uid', 'inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num', 'mandatory_lbp_lesson_num'])) {
            return true;
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        $aiLessonNum = intval($esCuData[self::$studentUid]['inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num']);

        $mustLessonCnt = intval($esCuData[self::$studentUid]['mandatory_lbp_lesson_num']);


        self::$student['gjkPlanLuboCompleteNum'] = $aiLessonNum . '/' . $mustLessonCnt;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'gjkPlanLuboCompleteNum', [
            'title' => '【计划录播累计完课数】',
            '数据源'   => 'es：公共数仓cu，分子：inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num， 分母：mandatory_lbp_lesson_num',
        ]);
    }


    /**
     * 计划录播累计完课数百分比
     * @throws ReflectionException
     */
    public static function getGjkPlanLuboCompleteNumPercent() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonCuFields(['student_uid', 'inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num', 'mandatory_lbp_lesson_num'])) {
            return true;
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCommonCuData");

        $aiLessonNum = intval($esCuData[self::$studentUid]['inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num']);

        $mustLessonCnt = intval($esCuData[self::$studentUid]['mandatory_lbp_lesson_num']);

        if ($mustLessonCnt == 0) {
            self::$student['gjkPlanLuboCompleteNumPercent'] = 0;

        } else {
            self::$student['gjkPlanLuboCompleteNumPercent'] = round($aiLessonNum / $mustLessonCnt, 2) * 100;

        }
        self::$student['gjkPlanLuboCompleteNumPercentDisplay'] = self::$student['gjkPlanLuboCompleteNumPercent'] . '%';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'gjkPlanLuboCompleteNumPercentDisplay', [
            'title' => '【计划录播累计完课数百分比】',
            '数据源'   => 'es：公共数仓cu，分子：inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num， 分母：mandatory_lbp_lesson_num',
        ]);
    }

    /**
     * 高价课必看字段
     * @throws ReflectionException
     */
    public static function getMustWatchData() {

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getMustWatchData");

        self::$student['isMandatory'] = $esCuData[self::$studentUid]['isMandatory'] ?? 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isMandatory', [
            'title' => '【高价课必看字段】',
            '数据源'   => '学习计划接口：/learnplan/backend/lessonlearningplanlist，字段isMandatory',
        ]);

    }

    /**
     * 课堂报告链接
     */
    public static function getJxLessonReport() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["studentUid", "is_generate_lesson_report"])) {
            return true;
        }
        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData", [AssistantDesk_Data_CommonParams::$lessonId]);
        $lessonReport = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getJxReportUrlData", [AssistantDesk_Data_CommonParams::$lessonId]);
        self::$student['isGenerateLessonReport'] = $commonLuDataList[self::$studentUid]['is_generate_lesson_report'] ?? 0;
        self::$student['isGenerateLessonReportUrl'] = self::$student['isGenerateLessonReport'] ? Api_Su::getShortUrl($lessonReport[self::$studentUid]["url"]) : "";
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'is_generate_lesson_report', [
            'title' => '【课堂报告链接】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，is_generate_lesson_report 课堂报告链接',
        ]);
    }

    /**
     * ycl考级字段
     */
    public static function getYCLField() {
        if (\AssistantDesk_Data_DataSource::beforeAddCuFields(["latest_exam_time", "latest_time_interval"])) {
            return [];
        }

        $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCuData");
        $latestExamTime  = intval($esCuData[self::$studentUid]['latest_exam_time']) ?? 0;
        $latestTimeInterval = $esCuData[self::$studentUid]['latest_time_interval'] ?? 0;
        if (!$latestExamTime || !$latestTimeInterval) {
            self::$student['ycl_examination_time'] = '-';
        } else {
            self::$student['ycl_examination_time'] = date('Y-m-d', $latestExamTime) . ' ' . $latestTimeInterval;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'ycl_examination_time', [
            'title'  => '【ycl考级时间】',
            "source' => 'es.cu.latest_exam_time:{$latestExamTime} + latest_time_interval:{$latestTimeInterval}",
            '解释'     => 'es.cu.latest_exam_time + latest_time_interval 拼接',
        ]);
    }

    /**
     * 获取第N章节到课状态
     * @throws ReflectionException
     */
    public static function getLessonNumberAttend() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(["is_inclass_teacher_room_attend_5minute"])) {
            return true;
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        if (empty($fieldRule['serviceConfig'])) {
            return;
        }

        $courseInfo = Common_Singleton::getInstanceData(AssistantDesk_Course::class, "getCourseInfo", [AssistantDesk_Data_CommonParams::$courseId]);
        $lessonList = $courseInfo['lessonList'];

        if($fieldRule['serviceConfig']['lessonType'] > 0) {
            foreach($lessonList as $key => $lessonInfo) {
                if ($lessonInfo['lessonType'] != $fieldRule['serviceConfig']['lessonType']) {
                    unset($lessonList[$key]);
                }
            }
        }

        if($fieldRule['serviceConfig']['playType'] > 0) {
            foreach($lessonList as $key => $lessonInfo) {
                if ($lessonInfo['playType'] != $fieldRule['serviceConfig']['playType']) {
                    unset($lessonList[$key]);
                }
            }
        }

        $column = $fieldRule['serviceConfig']['column'];
        if(mb_strlen($fieldRule['serviceConfig']['column']) > 0) {
            $column = 'is_inclass_teacher_room_attend_5minute';
        }

        $number = $fieldRule['serviceConfig']['number']-1;
        if($number < 0) {
            $number = 0;
        } else if ($number >= count($lessonList)) {
            $number = count($lessonList)-1;
        }

        //按上课时间升序
        array_multisort(array_column($lessonList, 'startTime'), SORT_ASC, $lessonList);
        if(!isset($lessonList[$number])) {
            return;
        }


        $selectLessonId = $lessonList[$number]['lessonId'];

        Bd_Log::notice("getLessonNumberAttend, selectLessonId:{$selectLessonId}");

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonDataByLessonId", [$selectLessonId]);
        self::$student[$fieldRule['key']] = $commonLuDataList[self::$studentUid]['is_inclass_teacher_room_attend_5minute'] ?? 0;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldRule['key'], [
            'title' => '【第N章节是否到课】',
            '数据源'   => '表es.dataware_idl_common_lesson_student 字段:isInclassTeacherRoomAttend5Minute',
        ]);
    }

    /*
     * 魔方投放城市字段
     */
    public static function getMofangCity() {
        if (\AssistantDesk_Data_DataSource::beforeAddDeerToufangFields(["student_uid", "province", "city", "county", "county_code"])) {
            return [];
        }

        $esDeerToufangData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDeerToufangData");
        $toufangInfo = $esDeerToufangData[self::$studentUid] ?? [];
        $province = $toufangInfo['province'] ?? "";
        $city = $toufangInfo['city'] ?? "";
        $county = $toufangInfo['county'] ?? "";
        self::$student['toufangArea'] = "";
        if ($province || $city || $county) {
            self::$student['toufangArea'] = $province . "/" . $city . "/" . $county;
        }
        self::$student['county_code'] = $toufangInfo['county_code'];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'external_idl_deer_cube_form_report', [
            'title'  => '【小鹿投放城市字段】',
            "source' => 'es.external_idl_deer_cube_form_report，province，city，county，county_code字段 ",
            '解释'     => 'es.external_idl_deer_cube_form_report',
        ]);
    }
    /**
     * 是否迟到
     * 定义delayTime：first_enter_time - 开课时间 > delayTime算迟到
     * @return bool 0=否，1=是
     * @throws ReflectionException
     */
    public static function getStudentAttendDelayed() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'lesson_id', 'first_enter_time', 'inclass_teacher_room_attend_duration'])) {
            return true;
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');
        $delayTime = intval($fieldRule['serviceConfig']['delayTime'] ?? 300); // default 5 min

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title' => '【是否迟到】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，first_enter_time-开课时间>delayTime && inclass_teacher_room_attend_duration>0',
        ]);

        $firstEnterTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['first_enter_time'];
        $attendTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['inclass_teacher_room_attend_duration'];

        // 获取章节开始时间
        $lessonId = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['lesson_id'];
        $lessonInfo = Common_Singleton::getInstanceData(Api_Dal::class, "getLessonBaseByLessonIds", [$lessonId]);
        if (empty($lessonInfo) || empty($lessonInfo['startTime'])) {
            Bd_Log::warning("getStudentAttendDelayed:studentUid[".self::$studentUid."],lessonId[$lessonId]data invalid:".json_encode($lessonInfo));
            return false;
        }

        $delayed = 0;
        if ($attendTime > 0
            && $firstEnterTime > 0
            && ($firstEnterTime - $lessonInfo['startTime'] > $delayTime)) {
            $delayed = 1;
        }

        Bd_Log::notice("getStudentAttendDelayed:studentUid[".self::$studentUid."]:first_enter_time[{$firstEnterTime}],inclass_teacher_room_attend_duration[{$attendTime}]");
        self::$student[$fieldKey] = $delayed;
        return $delayed;
    }

    /**
     * 内容观看时长 = 直播到课时长 + 新回放观看时长
     * @return bool|string
     * @throws ReflectionException
     */
    public static function getInclassDuration() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'inclass_teacher_room_attend_duration', 'inclass_teacher_room_total_playback_time_v1'])) {
            return true;
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title' => '【内容观看时长】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，inclass_teacher_room_attend_duration + inclass_teacher_room_total_playback_time_v1',
        ]);

        $attendTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['inclass_teacher_room_attend_duration'];
        $newPlaybackTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'];

        //$inclassDuration = AssistantDesk_Tools::formatDurationTime(($attendTime + $newPlaybackTime));
        // 改为 int 秒数返回，前端根据 cname format
        $inclassDuration = floor(($attendTime + $newPlaybackTime));

        $inclassDurationFilter = $attendTime + $newPlaybackTime;

        Bd_Log::notice("getInclassDuration:studentUid[".self::$studentUid."]:inclass_teacher_room_attend_duration[{$attendTime}],inclass_teacher_room_total_playback_time_v1[{$newPlaybackTime}]");
        self::$student[$fieldKey] = $inclassDuration;
        self::$student[$fieldKey."filter"] = $inclassDurationFilter;
        return $inclassDuration;
    }

    /**
     * 内容观看完课状态；直播到课时长 or 新回放观看时长 任意一个大于章节总时长 * 设置的比例
     * @return bool|int 0=未完成，1=已完成
     * @throws ReflectionException
     */
    public static function getInclassHasFinish() {
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields(['student_uid', 'inclass_teacher_room_attend_duration', 'inclass_teacher_room_total_playback_time_v1'])) {
            return true;
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');
        $rateFinish = floatval($fieldRule['serviceConfig']['rateFinish'] ?? 0.75); // default 0.75

        $commonLuDataList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title' => '【内容观看完课状态】',
            '数据源'   => 'es：dataware_idl_common_lesson_student，inclass_teacher_room_attend_duration or inclass_teacher_room_total_playback_time_v1任意大于指定比例',
        ]);

        $attendTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['inclass_teacher_room_attend_duration'];
        $newPlaybackTime = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['inclass_teacher_room_total_playback_time_v1'];

        // 获取章节开始时间
        $lessonId = empty($commonLuDataList[self::$studentUid]) ? 0 : $commonLuDataList[self::$studentUid]['lesson_id'];
        $lessonInfo = Common_Singleton::getInstanceData(Api_Dal::class, "getLessonBaseByLessonIds", [$lessonId]);
        if (empty($lessonInfo) || empty($lessonInfo['startTime']) || empty($lessonInfo['stopTime'])) {
            Bd_Log::warning("getInclassHasFinish:studentUid[".self::$studentUid."],lessonId[$lessonId]data invalid:".json_encode($lessonInfo));
            return false;
        }

        $finished = 0;
        $finishTime = ($lessonInfo['stopTime'] - $lessonInfo['startTime']) * $rateFinish;
        if ($finishTime < $attendTime || $finishTime < $newPlaybackTime) {
            $finished = 1;
        }

        Bd_Log::notice("getInclassHasFinish:studentUid[".self::$studentUid."]:inclass_teacher_room_attend_duration[{$attendTime}],inclass_teacher_room_total_playback_time_v1[{$newPlaybackTime}]");
        self::$student[$fieldKey] = $finished;
        return $finished;
    }

    public static function getStageNumTestList() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        // 第几次阶段测报告
        $num = intval($fieldRule['serviceConfig']['num'] ?? 1);
        self::$student['stageReportNum' . $num] = $num;
        $stageData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getInterImReportList", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$studentUids, $num]);
        $stageData = $stageData[self::$studentUid];
        self::$student['isReadStageReport' . $num] = 0;
        if (!isset($stageData['is_read'])) {
            self::$student['isReadStageReport' . $num] = 0;
        } else {
            self::$student['isReadStageReport' . $num] = $stageData['is_read'] + 1;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "第 $num 次阶段报告", [
            'title' => "第 $num 次阶段报告",
            '数据源'   => "第 $num 次阶段报告，/dataproxy/cu/interim-report-list",
        ]);
    }

    /**
     * 直播到课时长，
     * 课前：https://dataring.zuoyebang.cc/#/datadictionary/data-dictionary/indicators/detail?id=2977&type=ATOMIC&operate=show
     * 课中
     * 课后：https://dataring.zuoyebang.cc/#/datadictionary/data-dictionary/indicators/detail?id=2974&type=ATOMIC&operate=show
     * @return bool|int|mixed
     * @throws ReflectionException
     */
    public static function getRoomAttendDuration(){
        if (\AssistantDesk_Data_DataSource::beforeAddCommonLuFields([
            "beforeclass_assistant_room_total_playback_time",
            "inclass_teacher_room_attend_duration",
            "afterclass_assistant_room_attend_duration"
        ])) {
            return true;
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');
        $beforeClass = intval($fieldRule['serviceConfig']['beforeClass'] ?? 0); // 课前
        $inClass = intval($fieldRule['serviceConfig']['inClass'] ?? 1); // 课中
        $afterClass = intval($fieldRule['serviceConfig']['afterClass'] ?? 0); // 课后

        $esLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getCommonStudentLessonData");
        $beforeClassDuration = $esLessonData[self::$studentUid]['beforeclass_assistant_room_total_playback_time'];
        $inClassDuration = $esLessonData[self::$studentUid]['inclass_teacher_room_attend_duration'];
        $afterClassDuration = $esLessonData[self::$studentUid]['afterclass_assistant_room_attend_duration'];

        $duration = 0;
        $allNull = true;
        if ($beforeClass > 0 && !is_null($beforeClassDuration)) {
            $duration += $beforeClassDuration;
            $allNull = false;
        }
        if ($inClass > 0 && !is_null($inClassDuration)) {
            $duration += $inClassDuration;
            $allNull = false;
        }
        if ($afterClass > 0 && !is_null($afterClassDuration)) {
            $duration += $afterClassDuration;
            $allNull = false;
        }

        self::$student[$fieldKey] = $allNull ? "-" : AssistantDesk_Tools::formatDurationTime($duration);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title' => '【到课时长】',
            '数据源'   => 'es.common_lu.dataware_idl_common_lesson_student_v1.[beforeclass_assistant_room_total_playback_time,inclass_teacher_room_attend_duration,afterclass_assistant_room_attend_duration]',
            '解释' => '到课时长（课前、课中、课后）',
        ]);
        return $duration;
    }


    public static function getDeerLayerTag() {
        if (\AssistantDesk_Data_DataSource::beforeAddLeadsExtFields([])) {
            return true;
        }
        $leadsEsExtData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsExtendLeadsData", [array_keys(AssistantDesk_Data_CommonParams::$leadsIdMapStudentUid)]);

        $currentData = $leadsEsExtData[self::$leadsId];
        self::$student["fallLevelId"] = intval($currentData['fall_level_id']) ? intval($currentData['fall_level_id']) : '-';
        self::$student["beforeLevelId"] = intval($currentData['before_level_id']) ? intval($currentData['before_level_id']) : '-';
        self::$student["afterLevelId"] = intval($currentData['after_level_id']) ? intval($currentData['after_level_id']) : '-';
        self::$student["deerLevelStr"] = '分层：' . self::$student["fallLevelId"] . ' | ' . self::$student["beforeLevelId"] . ' | ' . self::$student["afterLevelId"];
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, '小鹿分层数据', [
            'title' => '【小鹿分层】',
            '数据源'   => 'getEsExtendLeadsData， fall_level_id，before_level_id，after_level_id',
            '解释' => '小鹿分层（掉落时、课前、课后）',
        ]);
    }

    public static function getIsEduProbePdfCreated() {
        if (AssistantDesk_Data_DataSource::beforeAddULearningReportFields(['student_uid', 'course_id'])) {
            return true;
        }
        $studentListData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsULearningReport");
        $currentData = $studentListData[self::$studentUid];
        if (empty($currentData) || empty($currentData['course_id'])) {
            // 未生成
            $pdfStatus = Api_DataProxy::EDU_PROBE_PDF_STATUS_NOT_CREATED;
        } else {
            // 已生成
            $pdfStatus = Api_DataProxy::EDU_PROBE_PDF_STATUS_CREATED;
        }
        self::$student["isEduProbePdfCreated"] = $pdfStatus;
        return self::$student["isEduProbePdfCreated"];
    }

    public static function getLearningReport() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return true;
        }
        $studentListData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsExternalLearningReport", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$studentUids]);
        $currentData = $studentListData[self::$studentUid];
        if (isset($currentData)) {
            self::$student["eduProbePdfStauts"] = intval($currentData['edu_probe_pdf_stauts']) ?? 0;
            // 有记录但status=0，说明生成中
            if (self::$student["eduProbePdfStauts"] == \Api_DataProxy::EDU_PROBE_PDF_STATUS_NOT_CREATED) {
                self::$student["eduProbePdfStauts"] = \Api_DataProxy::EDU_PROBE_PDF_STATUS_CREATING;
            }
        } else {
            // 未生成
            self::$student["eduProbePdfStauts"] = \Api_DataProxy::EDU_PROBE_PDF_STATUS_NOT_CREATED;
        }

        self::$student["eduProbePdf"] = $currentData['edu_probe_pdf_obj_name'] ? Hkzb_Util_FuDao::getFileUrl($currentData['edu_probe_pdf_obj_name'], 86400, 'zyb-charge', 'bos') : "";
        self::$student["eduProbePdfName"] = $currentData['edu_probe_pdf_file_name'];
        self::$student["eduProbePdfDate"] = intval($currentData['edu_probe_pdf_time']) ? date('Y-m-d H:i', $currentData['edu_probe_pdf_time']) : '';

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, '学情测评', [
            'title' => '【学情测评】',
            '数据源'   => 'external_idl_course_student_learning_report， edu_probe_pdf_stauts，edu_probe_pdf_name，edu_probe_pdf_time',
            '解释' => '学情测评 调查问卷',
        ]);

        self::$student["fnProbeReportLink"] = Common_Singleton::getInstanceData(\Api_EduProbe::class, 'getProbeUrl', [AssistantDesk_Data_CommonParams::$courseId]);
        self::$student["encodeCourseId"] = \Hk_Util_IdCrypt::encodeAQid(AssistantDesk_Data_CommonParams::$courseId);
        self::$student["encodeStudentId"] = \Hk_Util_IdCrypt::encodeAQid(self::$studentUid);
    }

    public static function getCorrectLessonReport() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $dasLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getDasLessonData");
        $studentHomeworkInfo = $dasLessonData[self::$studentUid][AssistantDesk_Data_CommonParams::$lessonId];
        $studentReportUrl = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getReportUrlData", [AssistantDesk_Data_CommonParams::$lessonId]);

        self::$student['isCorrectLessonReport'] = 0;
        self::$student['correctReportUrl'] = "";
        if ($studentHomeworkInfo['homeworkRecorrect'] == AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_CORRECTED) {
            self::$student['isCorrectLessonReport'] = 1;
            self::$student['correctReportUrl']     = $studentReportUrl[self::$studentUid]['correctReportUrl'] . '&videoPlayType=2';
            self::$student['correctReportUrlReal'] = $studentReportUrl[self::$studentUid]['correctReportUrl'];
        }

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, '是否生成章节报告', [
            'title' => '【是否生成章节报告】',
            '数据源'   => 'das, homeworkRecorrect',
            '解释' => '是否生成章节报告',
        ]);
    }

    public static function getEvaluateCommitInfo()
    {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $params             = [
            AssistantDesk_Data_CommonParams::$studentUids, AssistantDesk_Data_CommonParams::$courseId, self::EVALUATE_SOURCE_TYPE_COURSE,
            AssistantDesk_Data_CommonParams::$assistantUid, self::EVALUATE_DIMENSION_TYPE_FD, AssistantDesk_Data_CommonParams::$taskId
        ];
        $evaluateCommitData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEvaluateCommitData", $params);

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, '填写状态', [
            'title'  => '【是否提交调查问卷以及提交时间】',
            '数据源' => 'es：external_idl_course_student_weekly_v1，字段取值：commit_status, commit_time, submit_id',
            '解释'   => '填写状态',
        ]);

        Bd_Log::notice(sprintf("getEvaluateCommitInfo, params: %s, ret: %s", json_encode($params), json_encode($evaluateCommitData)));
        $evaluateCommitInfo                          = $evaluateCommitData[self::$studentUid] ?? [];
        self::$student["evaluateCommitStatus"]       = $evaluateCommitInfo["commitStatus"] ?? 0;
        self::$student["evaluateCommitStatusFilter"] = $evaluateCommitInfo["commitStatus"] ?? 0;
        self::$student["evaluateCommitId"]           = $evaluateCommitInfo["submitId"] ?? 0;
        self::$student["evaluateCommitTime"]         = !empty($evaluateCommitInfo["commitTime"]) ? date("Y-m-d H:i:s", $evaluateCommitInfo["commitTime"]) : "";

        return $evaluateCommitData;
    }

    /**
     * 获取课程学生是否入群
     * @return array
     * @throws ReflectionException
     */
    public static function getGroupBindStudentCourse() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');

        $groupInStudents = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getGroupBindStudentCourse", [
            AssistantDesk_Data_CommonParams::$assistantUid,
            AssistantDesk_Data_CommonParams::$courseId,
        ]);

        // match student
        self::$student[$fieldKey] = in_array(self::$studentUid, $groupInStudents) ? 1 : 0;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, $fieldKey, [
            'title' => '【是否入群】（课程）',
            '数据源'   => 'db获取绑定关系, 获取鲲鹏入群人数',
            '解释' => '【是否入群】（课程）',
        ]);
        return self::$student[$fieldKey];
    }

    /**
     * 根据sendtype获取当前章节是否已发送场景化群发
     */
    public static function getLessonWxSendHistoryBySendType(){
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $fieldKey = strval($fieldRule['key'] ?? '');
        $sendType = intval($fieldRule['serviceConfig']['beforeClass'] ?? 0); // sendType
        $isLessonDimension = boolval($fieldRule['serviceConfig']['inClass'] ?? true); // 是否章节维度
        $sendHistoryUids      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getWxSendHistoryMap", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
            $sendType,
            AssistantDesk_Data_CommonParams::$lessonId,
            $isLessonDimension, // 是否是章节维度
        ]);
        $hasSend = isset($sendHistoryUids[self::$studentUid]) ? 1 : 0;
        if ($sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT) {
            $stuFeedbackDetail = Common_Singleton::getInstanceData(AssistantDesk_KpMessageService_SpecialSendTypeHandle::class, "stuFeedbackDetail", [
                AssistantDesk_Data_CommonParams::$courseId,
                AssistantDesk_Data_CommonParams::$lessonId,
                $sendType,
                AssistantDesk_Data_CommonParams::$assistantUid,
            ]);
            // 如果批改系统反馈过作文报告图片也算反馈过
            isset($stuFeedbackDetail[self::$studentUid]) && $hasSend = 1;
        }
        if ($sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER) {
            // 小鹿工作台也可以单独发送
            $studentUids = Api_DeerCorrect::getFeedUids(AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$assistantUid, 2000);
            $hasSend = (in_array(self::$studentUid, $studentUids) || $hasSend) ? 1 : 0;
        }

        // 将发送记录拼接到全局学生列表数据
        self::$student[$fieldKey]   = $hasSend;

        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'getLessonWxSendHistoryBySendType', [
            'title' => '【根据sendtype获取当前章节是否已发送场景化群发】章节维度, sendType:' . $sendType,
            '数据源'   => 'mysql.tblWxMessageSendRecord ，作文报告反馈：查询批改详情',
        ]);
    }

    /**
     * 直播课解锁章节数，以及已发（解锁的定义：直播章节是否开课）
     */
    public static  function getUnLockLessonNum() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }

        $fieldRule = AssistantDesk_Data_StudentListFormat::getFieldRule();
        $sendType = intval($fieldRule['serviceConfig']['beforeClass'] ?? 0); // sendType

        // 获取已解锁章节信息，获取学生已发送的章节 [$studentUid => [$lessonId]]
        list($sendHistoryUidLessonCnts, $unlockLessons)     = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLessonSendHistoryByCourseId", [
            AssistantDesk_Data_CommonParams::$courseId,
            AssistantDesk_Data_CommonParams::$assistantUid,
            $sendType
        ]);

        self::$student['deerProgrammingSendCnt'] = "0/0";
        self::$student['deerProgrammingSendDetail'] = [];

        if (empty($unlockLessons)) {
            return;
        }

        // 是否提交
        $unlockLessonIds = array_column($unlockLessons, 'lessonId') ?? [];
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getBcReportStatusByLessonStudentUids", [
            $unlockLessonIds,
        ]);

        $submitLessonIds = $sendHistoryUidLessonCnts[self::$studentUid] ?? [];
        $studentSubmitData = $studentLessonData[self::$studentUid] ?? [];

        // 已发送/已解锁
        self::$student['deerProgrammingSendCnt'] = count($submitLessonIds) . '/' . count($unlockLessonIds);
        $deerProgrammingLessonStatusData = [];
        // 拼接浮窗内容
        foreach ($unlockLessons as $unlockLesson) {
            $deerProgrammingLessonStatusData[] = [
                'idx' => $unlockLesson['idx'], //序号
                'lessonName' => $unlockLesson['lessonName'],
                'isSubmit' => in_array($studentSubmitData[$unlockLesson['lessonId']]['bc_report_status'], [2,3]) ? // 2,3为已生成报告状态，
                    ($submitLessonIds[$unlockLesson['lessonId']] ? 2 : 1) :
                    0,
            ];
        }
        self::$student['deerProgrammingSendDetail'] = $deerProgrammingLessonStatusData;
    }


    private static function getStudentThirdOrderInfo(): array
    {
        $skuMap = Common_Singleton::getInstanceData(Api_Dak::class, "getCourseSkuIdMapByCourseIds", [[AssistantDesk_Data_CommonParams::$courseId]]);
        $skuId = $skuMap[AssistantDesk_Data_CommonParams::$courseId];
        $userOrder = Common_Singleton::getInstanceData(Api_StudentLogisticsInfo::class, "getOneOpenSearch", [AssistantDesk_Data_CommonParams::$studentUids, [$skuId]]);

        $orderIdList = [];
        foreach ($userOrder as $orderId) {
            array_push($orderIdList, $orderId);
        }
        return [
            "userOrder" => $userOrder,
            "orderInfo" => Common_Singleton::getInstanceData(Api_StudentLogisticsInfo::class, "getThirdOrderSearch", [$orderIdList])
        ];
    }

    public static function getStudentCourseAddressInfo()
    {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $apiRes = self::getStudentThirdOrderInfo();
        $userOrder = $apiRes["userOrder"];
        $orderInfo = $apiRes["orderInfo"];
        if ($orderInfo == false) {
            return [];
        }
        $orderId = strval($userOrder[self::$studentUid]);
        # $orderId = "2105683890";
        if (isset($orderInfo[$orderId]["receiverInfo"]) and $orderInfo[$orderId]["receiverInfo"] != null) {
            $addressInfo = $orderInfo[$orderId]["receiverInfo"];
            self::$student["studentCourseAddressInfo"] = $addressInfo["province"] . "/" . $addressInfo["city"] . "/" . $addressInfo["area"];
        };
        AssistantDesk_Util_Log::debug("getStudentCourseAddressInfo:".self::$student["studentCourseAddressInfo"]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "studentCourseAddressInfo", [
            'title' => '三方订单物流地址信息',
            '数据源'   => 'moat服务端外物流接口查询',
            '解释' => '',
        ]);

    }

    public static function getThirdOrderStoreInfo()
    {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $apiRes = self::getStudentThirdOrderInfo();
        $userOrder = $apiRes["userOrder"];
        $orderInfo = $apiRes["orderInfo"];
        if ($orderInfo == false) {
            return;
        }
        $orderId = strval($userOrder[self::$studentUid]);
        # $orderId = "2105683890";
        $skuData = [];
        if (isset($orderInfo[$orderId]["shopName"]) && $orderInfo[$orderId]["shopName"] != null) {
            $skuData[] = $orderInfo[$orderId]["sourcePlatFormName"] . "/" . $orderInfo[$orderId]["shopName"];
        }
        if (!empty($orderInfo[$orderId]["placeOrderTime"])) {
            $skuData[] = $orderInfo[$orderId]["placeOrderTime"];
        }
        self::$student["thirdOrderStoreInfo"] = implode("<br>", $skuData);
        AssistantDesk_Util_Log::debug("getThirdOrderStoreInfo:".self::$student["thirdOrderStoreInfo"]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "thirdOrderStoreInfo", [
            'title' => '三方订单物流店铺信息',
            '数据源'   => 'moat服务端外物流接口查询',
            '解释' => '',
        ]);
    }

    public static function getStudentCourseThirdOrderInfo()
    {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $apiRes = self::getStudentThirdOrderInfo();
        $userOrder = $apiRes["userOrder"];
        $orderInfo = $apiRes["orderInfo"];
        if ($orderInfo == false) {
            return;
        }
        $orderId = strval($userOrder[self::$studentUid]);
        # $orderId = "2105683890";
        if (isset($orderInfo[$orderId]["skuList"]) and $orderInfo[$orderId]["skuList"] != null) {
            $skuList = $orderInfo[$orderId]["skuList"];
            $skuData = [];
            foreach ($skuList as $sku) {
                array_push($skuData, $sku["skuName"] . "/" . "¥" . $sku["priceFen"]/100);
            }
            if (!empty($orderInfo[$orderId]["referrerName"])) {
                $skuData[] = $orderInfo[$orderId]["referrerName"];
            }
            self::$student["studentCourseThirdOrderInfo"] = implode("<br>", $skuData); // 列表展示
            if (mb_strlen(self::$student["studentCourseThirdOrderInfo"]) >= 50) { // 大于50个字符串 截断
                self::$student["studentCourseThirdOrderInfo"] = mb_substr(self::$student["studentCourseThirdOrderInfo"], 0, 50) . "...";
            }
            self::$student["studentCourseThirdOrderInfoInfo"] = implode("<br>",$skuData); // 悬浮展示
            self::$student["studentCourseThirdOrderInfoFileInfo"] = implode("；",$skuData); // 导出内容
        };
        AssistantDesk_Util_Log::debug("getStudentCourseThirdOrderInfo:".self::$student["studentCourseThirdOrderInfoInfo"]);
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "studentCourseThirdOrderInfoInfo", [
            'title' => '三方订单物流订单信息',
            '数据源'   => 'moat服务端外物流接口查询',
            '解释' => '',
        ]);
    }

    /**
     * 对应章节阶段测报告是否已经阅读
     */
    public static function getIsLessonStageTestReportRead() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $num = Common_Singleton::getInstanceData(AssistantDesk_FieldToData::class, "getLessonInterim", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$lessonId]);
        self::$student['isLessonReadStageReport'] = 0;
        if ($num === false) {
            return;
        }
        $stageData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getInterImReportList", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$studentUids, $num]);
        $stageData = $stageData[self::$studentUid];
        if (!isset($stageData['is_read'])) {
            self::$student['isLessonReadStageReport'] = 0;
        } else {
            self::$student['isLessonReadStageReport'] = $stageData['is_read'] + 1;
        }
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "第 $num 次阶段报告", [
            'title' => "第 $num 次阶段报告",
            '数据源'   => "第 $num 次阶段报告，/dataproxy/cu/interim-report-list",
        ]);
    }

    /*
     * 需求wiki：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=494967760
     */
    public static function getExamAnalysisExamNum() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        $studentExamAnalysisExam = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getExamAnalysisExamNum", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$assistantUid, AssistantDesk_Data_CommonParams::$studentUids]);
        $studentData = $studentExamAnalysisExam[self::$studentUid] ?? [];

        $totalExamNum = 0;
        $finishNum = 0;
        if (!empty($studentData)) {
            $totalExamNum = $studentData['totalExam'] ?? 0;
            $finishNum = $studentData['finishNum'] ?? 0;
        }
        $encodeStaffUId = Hk_Util_IdCrypt::encodeAQid(AssistantDesk_Data_CommonParams::$assistantUid);
        $encodeCourseId = Hk_Util_IdCrypt::encodeAQid(AssistantDesk_Data_CommonParams::$courseId);
        $encodeStudentUid = Hk_Util_IdCrypt::encodeAQid(self::$studentUid);

        $url = 'https://exam-analysis.zuoyebang.cc/static/hy/exam-analysis-mis/#/exam-list?';
        $url .= "staffUid={$encodeStaffUId}&studentId={$encodeStudentUid}&courseId={$encodeCourseId}";

        self::$student['studentExamAnalysis'] = $finishNum . ' / ' . $totalExamNum;
        self::$student['studentExamAnalysisUrl'] = $url;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "studentCourseThirdOrderInfoInfo", [
            'title' => '领航试卷分析',
            '数据源'   => 'https://yapi.zuoyebang.cc/project/10341/interface/api/819072',
            '解释' => '领航学生试卷数/报告数',
        ]);
    }
    /*
     * 是否提交编程作业
     */
    public static function getIsSubmitBcHomework() {
        if (\AssistantDesk_Data_DataSource::beforeAddLuFields([
            "deer_programming_homework_status",
        ])) {
            return [];
        }

        $studentLessonData      = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getStudentLessonData");
        $isSubmitBcHomework = 0;
        if (empty($studentLessonData[self::$studentUid]['deer_programming_homework_status'])) {
            $isSubmitBcHomework = 0;
        } else if (in_array($studentLessonData[self::$studentUid]['deer_programming_homework_status'], ['marked', 'un-correct', 'assigned', 'correct', 'un-assigned'])) {
            $isSubmitBcHomework = 1;
        }

        self::$student['isSubmitBcHomework'] = $isSubmitBcHomework;
    }

    /**
     * 回放状态
     */
    public static function getPlaybackAttendStatus() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        list($stuTimeLines, $isPlaybackMap) = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAttendTimelineForStudents", [AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]);
        $isPlayback = $isPlaybackMap[self::$studentUid] ?? 0;
        self::$student['isPlaybackAttend'] = $isPlayback;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "isPlaybackAttend", [
            'title' => '是否回放在线',
            '数据源'   => '长连接打点',
            '解释' => '通过长链接判断是否在线，可通过工具查询长链接打点',
        ]);
    }

    /**
     * 听课时长
     */
    public static function getClassLearningDuration() {
        if (\AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        list($stuTimeLines, $isPlayback) = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getAttendTimelineForStudents", [AssistantDesk_Data_CommonParams::$lessonId, AssistantDesk_Data_CommonParams::$studentUids]);
        $timelines = $stuTimeLines[self::$studentUid] ?? [];
        if (empty($timelines)) {
            self::$student['classLearningDuration'] = 0;
            return;
        }
        $len = count($timelines);
        $duration = 0;
        for ($i = 0; $i < $len-1; $i++) {
            // 当前时间段时在线，并且下个时间段是离线
            if ($timelines[$i]['status'] == Api_Longservice_LongService::TimeLineStatusOnline && $timelines[$i+1]['status'] == Api_Longservice_LongService::TimeLineStatusOffline) {
                $duration += $timelines[$i+1]['startTime'] - $timelines[$i]['startTime'];
            }
        }
        if ($timelines[count($timelines)-1]['status'] == Api_Longservice_LongService::TimeLineStatusOnline) {
            $duration += time() - $timelines[count($timelines)-1]['startTime'];
        }
        self::$student['classLearningDuration'] = intval(floor($duration / 60));
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "classLearningDuration", [
            'title' => '是否回放在线',
            '数据源'   => '长连接打点',
            '解释' => '通过长链接判断是否在线，可通过工具查询长链接打点',
        ]);
    }
    
    /*
     * 摸底测完成状态
     */
    public static function getPlacementFinishStatusFudao()
    {
        self::$student['placementTestFinishStatusFudaoStatus'] = AssistantDesk_Config::PLACEMENT_TEST_UNKNOWN;
        self::$student['placementTestFinishStatusFudao'] = [
            'status' => AssistantDesk_Config::PLACEMENT_TEST_UNKNOWN, // 未知
            'examId' => 0,
        ];
        $courseLessonInfo = Common_Singleton::getInstanceData(Api_Dal::class, 'getCourseLessonInfoByCourseIds', [AssistantDesk_Data_CommonParams::$courseId]);
        if (false === $courseLessonInfo) {
            Bd_Log::warning('getPlacementFinishStatusFudao dal章节信息获取失败');
            return;
        }
        $cpuId = $courseLessonInfo['cpuId'];
        if ($cpuId > 0) {
            $cpuExamBind = Common_Singleton::getInstanceData(AssistantDesk_ExamBind::class, 'cpuBindExams',[[$cpuId], Api_Exam::BIND_TYPE_SURVEY]);
            $examId = 0;
            if (isset($cpuExamBind[$cpuId]) && is_array($cpuExamBind[$cpuId])) {
                $examIds = array_keys($cpuExamBind[$cpuId]);
                $examId = intval(end($examIds));
            }
            if ($examId) {
                $esCuData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsCUIsTestComplete", [AssistantDesk_Data_CommonParams::$courseId, AssistantDesk_Data_CommonParams::$studentUids]);
                if ($esCuData[self::$studentUid]['is_test_complete'] === 1 || $esCuData[self::$studentUid]['is_test_complete'] === '1') {
                    self::$student['placementTestFinishStatusFudaoStatus'] = AssistantDesk_Config::PLACEMENT_TEST_FINISH;
                    self::$student['placementTestFinishStatusFudao'] = [
                        'status' => AssistantDesk_Config::PLACEMENT_TEST_FINISH, // 已完成
                        'examId' => $examId,
                    ];
                } else {
                    // 兜底措施 null/0都为未完成
                    $url = Common_Singleton::getInstanceData(Api_Examcore::class, 'getClientExamUrl', [$examId, AssistantDesk_Data_CommonParams::$courseId]);
                    if (!empty($url)) {
                        $shortUrl = Common_Singleton::getInstanceData(Api_Su::class, 'getShortUrl', [$url]);
                    }
                    self::$student['placementTestFinishStatusFudaoStatus'] = AssistantDesk_Config::PLACEMENT_TEST_UN_FINISH;
                    self::$student['placementTestFinishStatusFudao'] = [
                        'status' => AssistantDesk_Config::PLACEMENT_TEST_UN_FINISH, // 未完成
                        'examId' => $examId,
                        'shortUrl' => $shortUrl,
                    ];
                }
                AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, 'isTestComplete', [
                    'title' => '【是否完成摸底测】',
                    'source' => 'es：cu.is_test_complete',
                ]);
            } else {
                // 未绑定课程
                self::$student['placementTestFinishStatusFudao'] = [
                    'status' => AssistantDesk_Config::PLACEMENT_TEST_NO_NEED_TEST, // 免测
                    'examId' => $examId,
                ];
            }
        } else {
            // 未绑定课程
            self::$student['placementTestFinishStatusFudao'] = [
                'status' => AssistantDesk_Config::PLACEMENT_TEST_UNKNOWN, // 未知
                'examId' => 0,
            ];
            Bd_Log::warning('课程cpuId信息获取失败，当前课程可能未绑定摸底测');
            return;
        }
    }

    public static function getAiQuestionsBeforeClass() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        self::$student["aiChapterInteractiveQuestionsBeforeClass"] = "[]";
        self::$student["aiChapterInteractiveQuestionsBeforeClassAnswerNumber"] = 0;
        $arrFields = ["beforeclassPuzzleAnswerDetail", "beforeclassPuzzleParticipateCnt"];
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsLuListByCourseIdLessonIdsAssistantUid',[AssistantDesk_Data_CommonParams::$courseId, [AssistantDesk_Data_CommonParams::$lessonId], AssistantDesk_Data_CommonParams::$assistantUid, $arrFields]);
        self::$student["aiChapterInteractiveQuestionsBeforeClass"] = $studentLessonData[self::$studentUid]['beforeclassPuzzleAnswerDetail'] ?? "[]";
        self::$student["aiChapterInteractiveQuestionsBeforeClassAnswerNumber"] = $studentLessonData[self::$studentUid]['beforeclassPuzzleParticipateCnt'] ?? 0;
        AssistantDesk_Util_Log::debug("getAiChapterInteractiveQuestionsBeforeClass:". json_encode($studentLessonData[self::$studentUid]));
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "aiChapterInteractiveQuestionsBeforeClass", [
            'title' => '课前AI章节互动题',
            '数据源'   => 'es:idl_assistant_lesson_student_action；holo:ads_zbk_idl_assistant_lesson_student_action_v1',
            '解释' => '小鹿编程课前题完成情况',
        ]);
    }

    public static function getAiQuestionsInClass() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        self::$student["aiChapterInteractiveQuestionsInClass"] = "[]";
        self::$student["aiChapterInteractiveQuestionsInClassAnswerNumber"] = 0;
        $arrFields = ["inclassPuzzleAnswerDetail", "incalssPuzzleParticipateCnt"];
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsLuListByCourseIdLessonIdsAssistantUid',[AssistantDesk_Data_CommonParams::$courseId, [AssistantDesk_Data_CommonParams::$lessonId], AssistantDesk_Data_CommonParams::$assistantUid, $arrFields]);
        self::$student["aiChapterInteractiveQuestionsInClass"] = $studentLessonData[self::$studentUid]['inclassPuzzleAnswerDetail'] ?? "[]";
        self::$student["aiChapterInteractiveQuestionsInClassAnswerNumber"] = $studentLessonData[self::$studentUid]['incalssPuzzleParticipateCnt'] ?? 0;
        AssistantDesk_Util_Log::debug("getAiChapterInteractiveQuestionsInClass:". json_encode($studentLessonData[self::$studentUid]));
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "getAiChapterInteractiveQuestionsInClass", [
            'title' => '课中AI章节互动题',
            '数据源'   => 'es:idl_assistant_lesson_student_action；holo:ads_zbk_idl_assistant_lesson_student_action_v1',
            '解释' => '小鹿编程课中题完成情况',
        ]);
    }

    public static function getAiQuestionsAfterClass() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        self::$student["aiChapterInteractiveQuestionsAfterClass"] = "[]";
        self::$student["aiChapterInteractiveQuestionsAfterClassAnswerNumber"] = 0;
        $arrFields = ["afterclassPuzzleAnswerDetail", "afterPuzzleParticipateCnt"];
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsLuListByCourseIdLessonIdsAssistantUid',[AssistantDesk_Data_CommonParams::$courseId, [AssistantDesk_Data_CommonParams::$lessonId], AssistantDesk_Data_CommonParams::$assistantUid, $arrFields]);
        self::$student["aiChapterInteractiveQuestionsAfterClass"] = $studentLessonData[self::$studentUid]['afterclassPuzzleAnswerDetail'] ?? "[]";
        self::$student["aiChapterInteractiveQuestionsAfterClassAnswerNumber"] = $studentLessonData[self::$studentUid]['afterPuzzleParticipateCnt'] ?? 0;
        AssistantDesk_Util_Log::debug("getAiChapterInteractiveQuestionsAfterClass:". json_encode($studentLessonData[self::$studentUid]));
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "aiChapterInteractiveQuestionsAfterClass", [
            'title' => '课后AI章节互动题',
            '数据源'   => 'es:idl_assistant_lesson_student_action；holo:ads_zbk_idl_assistant_lesson_student_action_v1',
            '解释' => '小鹿编程课后题完成情况',
        ]);
    }

    /*
     * 是否完成AI拼搭
     */
    public static function getIsFinishAiPatchWork() {
        if (AssistantDesk_Data_DataSource::isBeforeStatus()) {
            return [];
        }
        self::$student["isFinishAiPatchWorkStatus"] = 2;
        $arrFields = ["ideFinishStatus"];
        $studentLessonData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, 'getEsLuListByCourseIdLessonIdsAssistantUid',[AssistantDesk_Data_CommonParams::$courseId, [AssistantDesk_Data_CommonParams::$lessonId], AssistantDesk_Data_CommonParams::$assistantUid, $arrFields]);
        self::$student['isFinishAiPatchWorkStatus'] = $studentLessonData[self::$studentUid]['ideFinishStatus'] ?? 2;
        AssistantDesk_Data_CommentAdd::addCommentArr(self::$student, "", [
            'title' => '是否完成拼搭',
            '数据源'   => 'es:idl_assistant_lesson_student_action；holo:ads_zbk_idl_assistant_lesson_student_action_v1',
            '解释' => '小鹿编程学员完成拼搭题状态',
        ]);
    }
}
