<?php

/*
 * 方舟学生列表请求参数存储，用于流量回放
 */

class AssistantDesk_Data_ArkQueryParamData
{
    use AssistantDesk_Ark_Singleton;

    const CONFIG_ARK_STUDENT_LIST_DATA_DIFF = 'assistantdesk_ark_student_list_data_diff_control_config';
    public static $oneDayRecordParamCountRedisKey = "assistantdesk::limit::%s::%s";

    public static function insertArkQueryParams($arrInput, $handleName, $res)
    {
        // diff api 接口跳过
        if ($arrInput['forDiff'] == 1) {
            return;
        }

        // 判断灰度
        $diffConfig = Util_Config_Get::getJsonCfg(self::CONFIG_ARK_STUDENT_LIST_DATA_DIFF, []);
        Bd_Log::notice("CONFIG_ARK_STUDENT_LIST_DATA_DIFF: " . json_encode($diffConfig));
        if (empty($diffConfig)) {
            Bd_Log::warning("insertArkQueryParams error, 获取配置失败!");
            return;
        }

        if ($diffConfig[$handleName . '_0']){
            self::doInsertArkQueryParams($diffConfig, $arrInput, $handleName.'_0', $res);
        }

        if ($diffConfig[$handleName . '_1']){
            self::doInsertArkQueryParams($diffConfig, $arrInput, $handleName.'_1', $res);
        }
    }

    public static function doInsertArkQueryParams($diffConfig,$arrInput, $handleName, $res){
        $nowTime  = date('Ymd');
        $storeReq = false;
        $storeResp = false;
        $diffType = 0;

        if ($diffConfig[$handleName]['disable']) {
            return;
        }
        $nowHour = date('H');

        // 时间范围判断
        if (($diffConfig[$handleName]['startTime'] < $nowTime && $diffConfig[$handleName]['endTime'] > $nowTime) && ($diffConfig[$handleName]['oneDayStartTime'] < $nowHour && $diffConfig[$handleName]['oneDayEndTime'] > $nowHour)) {
            $storeReq = $diffConfig[$handleName]['storeReq'];
            $storeResp = $diffConfig[$handleName]['storeResp'];
        }


        if (!$storeResp && !$storeReq) {
            return;
        }

        $collectFrequency = $diffConfig[$handleName]['collectFrequency']?$diffConfig[$handleName]['collectFrequency']:100;

        // 随机数判断，随机选一些请求
        $randomNumber = mt_rand(1, 100);
        Bd_Log::warning('doInsertArkQueryParams mt_rand' . $randomNumber);
        if ($randomNumber > $collectFrequency){
            return;
        };

        // 判断今日录制的请求数是否到达配置
        $redisCli = Common_Redis::getInstance(Common_Redis::ZYB_CODIS_ZHIBO);
        $redisValue = $redisCli->get(sprintf(self::$oneDayRecordParamCountRedisKey, $handleName, date('Y-m-d'))) ?? 0;
        if ($redisValue > $diffConfig[$handleName]['oneDayRecordMaxNum']) {
            return;
        }


        $arrParams = [
            'handlerName' => $handleName,
            'params' => json_encode($arrInput),
            'status' => 0,
            'createTime' => time(),
            'updateTime' => time(),
        ];

        // $storeResp 决定两种回放模式
        // ture：记录返回值，只记录结束的课程请求，后续回放时，重新请求此接口，diff 对比
        // false：只记录请求，后续回放时用参数重新请求，再与新接口 diff
        if ($storeResp) {
            $arrParams['oldData'] = json_encode($res);
            $arrParams['diffType'] = 1;
            $diffType = 1;
        }

        $arrInput['timestamp'] = 0;
        $paramsFingerprint = md5(json_encode($arrInput));

        $arrConds = [
            "fingerprint" => $paramsFingerprint,
            "diffType"=>$diffType,
        ];
        $records = (new Service_Data_Ark_ArkStudentDataQuery())->getList($arrConds);
        if (empty($records)) {
            $arrParams['fingerprint'] = $paramsFingerprint;
            $ret = (new Service_Data_Ark_ArkStudentDataQuery())->insert($arrParams);
            if (!$ret) {
                Bd_Log::warning('insertArkQueryParams err' . json_encode([$arrParams]));
                return;
            }

            // 记录录制的请求数量到 redis
            $key = sprintf(self::$oneDayRecordParamCountRedisKey, $handleName, date('Y-m-d'));
            $val = $redisCli->incrby($key, 1);
            if ($val == 1) {
                $redisCli->expire($key, 60 * 60 * 24);
            }
        }
    }
}

