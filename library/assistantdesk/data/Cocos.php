<?php

/**
 * Class AssistantDesk_Data_Cocos
 */
class AssistantDesk_Data_Cocos {

    public static function getCocosQuestions($lessonId) {
        $teacherInfo = Api_Dat::getTeacherByLessonIds([$lessonId]);
        if (empty($teacherInfo)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '获取主讲信息失败');

        }
        $teacherUid = $teacherInfo[$lessonId]['teacherUid'];
        $snapInfos  = Api_Achilles::getCocosSnapId($lessonId, $teacherUid);
        if (empty($snapInfos)) {
            return [];
        }
        $snapIds        = Tools_Array::getArrayColumn(array_values($snapInfos), 'snapId');
        $snapIds        = array_unique($snapIds);
        $cocosQuestions = Api_Artcw::getVersions($snapIds);
        if (empty($cocosQuestions)) {
            return [];
        }
        //返回tid和题目信息
        $ret = [];
        foreach ($snapInfos as $k => $v) {
            $ret[$k] = empty($cocosQuestions[$v['snapId']]) ? [] : $cocosQuestions[$v['snapId']];
        }
        return $ret;

    }

}
