<?php
/**
 * 方舟相关数据表单例类
 * AssistantDesk_Ark_AssistantSingleton
 * User: liuxian<PERSON>@zuoyebang.com
 * Date: 2020/12/08
 * Time: 21:30
 */

trait AssistantDesk_Ark_Singleton
{
    private $_db;
    private $_objDsArkTemplate;
    private $_objDsArkConfigRules;
    private $_objDsArkTemplateCourseBind;
    private $_objDsArkTemplateServiceName;
    private $_objDsArkTemplateServiceField;
    private $_objDsArkTemplateServiceTarget;
    private $_objDsArkTemplateServiceDataPanel;
    private $_objDsArkTemplateServiceFeature;
    private $_objDsArkTemplateServiceRelation;
    private $_objDsArkTemplateServiceOverview;
    private $_objDsArkTemplateContractBind;
    private $_objDsArkArkStudentDataQuery;

    private function getObjDb()
    {
        if ($this->_db) {
            return $this->_db;
        }

        return $this->_db = AssistantDesk_Db::getDbObj(AssistantDesk_Db::ZYB_FUDAO);
    }

    private function getObjDsArkTemplate()
    {
        if ($this->_objDsArkTemplate) {
            return $this->_objDsArkTemplate;
        }

        return $this->_objDsArkTemplate = new Service_Data_Ark_ArkTemplate();
    }

    private function getObjDsArkConfigRules()
    {
        if ($this->_objDsArkConfigRules) {
            return $this->_objDsArkConfigRules;
        }

        return $this->_objDsArkConfigRules = new Service_Data_Ark_ArkConfigRules();
    }


    private function getObjDsArkTemplateCourseBind()
    {
        if ($this->_objDsArkTemplateCourseBind) {
            return $this->_objDsArkTemplateCourseBind;
        }

        return $this->_objDsArkTemplateCourseBind = new Service_Data_Ark_ArkTemplateCourseBind();
    }

    private function getObjDsArkTemplateServiceName()
    {
        if ($this->_objDsArkTemplateServiceName) {
            return $this->_objDsArkTemplateServiceName;
        }

        return $this->_objDsArkTemplateServiceName = new Service_Data_Ark_ArkTemplateServiceName();
    }

    private function getObjDsArkTemplateServiceField()
    {
        if ($this->_objDsArkTemplateServiceField) {
            return $this->_objDsArkTemplateServiceField;
        }

        return $this->_objDsArkTemplateServiceField = new Service_Data_Ark_ArkTemplateServiceField();
    }

    private function getObjDsArkTemplateServiceRelation()
    {
        if ($this->_objDsArkTemplateServiceRelation) {
            return $this->_objDsArkTemplateServiceRelation;
        }

        return $this->_objDsArkTemplateServiceRelation = new Service_Data_Ark_ArkTemplateServiceRelation();
    }

    private function getObjDsArkTemplateServiceOverview()
    {
        if ($this->_objDsArkTemplateServiceOverview) {
            return $this->_objDsArkTemplateServiceOverview;
        }

        return $this->_objDsArkTemplateServiceOverview = new Service_Data_Ark_ArkTemplateServiceOverview();
    }

    private function getObjDsArkTemplateServiceFeature()
    {
        if ($this->_objDsArkTemplateServiceFeature) {
            return $this->_objDsArkTemplateServiceFeature;
        }

        return $this->_objDsArkTemplateServiceFeature = new Service_Data_Ark_ArkTemplateServiceFeature();
    }

    private function getObjDsArkTemplateServiceTarget()
    {
        if ($this->_objDsArkTemplateServiceTarget) {
            return $this->_objDsArkTemplateServiceTarget;
        }

        return $this->_objDsArkTemplateServiceTarget = new Service_Data_Ark_ArkTemplateServiceTarget();
    }

    private function getObjDsArkTemplateServiceDataPanel()
    {
        if ($this->_objDsArkTemplateServiceDataPanel) {
            return $this->_objDsArkTemplateServiceDataPanel;
        }

        return $this->_objDsArkTemplateServiceDataPanel = new Service_Data_Ark_ArkTemplateServiceDataPanel();
    }

    private function getObjDsArkTemplateContractBind()
    {
        if ($this->_objDsArkTemplateContractBind) {
            return $this->_objDsArkTemplateContractBind;
        }

        return $this->_objDsArkTemplateContractBind = new Service_Data_Ark_ArkTemplateContractBind();
    }

    private function getObjDsArkArkStudentDataQuery()
    {
        if ($this->_objDsArkArkStudentDataQuery) {
            return $this->_objDsArkArkStudentDataQuery;
        }

        return $this->_objDsArkArkStudentDataQuery = new Service_Data_Ark_ArkStudentDataQuery();
    }
}