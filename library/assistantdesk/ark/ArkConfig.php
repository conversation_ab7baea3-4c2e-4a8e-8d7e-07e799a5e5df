<?php
/**
 * Created by PhpStorm.
 * User: liuxia<PERSON><PERSON>@zuoyebang.com
 * Date: 2020/1/08
 * Time: 2:25 PM
 * 方舟组件化配置
 */
class AssistantDesk_Ark_ArkConfig {
    //用于动态非美枚举的Map，程序中动态指定
    const DYNAMIC_MAP           = 'dynamicMap';

    const CONFIG_TYPE_COURSE = 0; //课程维度
    const CONFIG_TYPE_LESSON = 1; //章节维度
    const CONFIG_TYPE_SEND_COURSE = 2; //赠课维度
    const CONFIG_TYPE_CONTRACT = 3; //合约维度
    const CONFIG_TYPE_CONTRACT_COURSE = 4; // 合约课程维度
    CONST CONFIG_TYPE_CONTRACT_LESSON = 5; // 合约章节维度

    const CONFIG_TYPE_ASSISTANT = 10; //课程维度


    const TOGETHER_TYPE_0 = 0; // 两边都展示
    const TOGETHER_TYPE_1 = 1; // 仅在老版本展示
    const TOGETHER_TYPE_2 = 2; // 仅在新版本（通用化）展示
    public static $configTypeMap = [
        self::CONFIG_TYPE_COURSE            => '课程维度',
        self::CONFIG_TYPE_LESSON            => '章节维度',
        self::CONFIG_TYPE_CONTRACT          => '合约维度',
        self::CONFIG_TYPE_CONTRACT_COURSE   => '合约课程维度',
        self::CONFIG_TYPE_CONTRACT_LESSON   => '合约章节维度',
    ];
    public static $togetherTypeMap = [
//        self::TOGETHER_TYPE_0   => '两边都展示',
//        self::TOGETHER_TYPE_1   => '仅在老版本展示',
        self::TOGETHER_TYPE_2   => '仅在新版本（通用化）展示',
    ];


    const RAL_TYPE_POST  = 'POST';
    const RAL_TYPE_GET   = 'GET';
    const RAL_TYPE_EMPTY = ' ';
    public static $ralType = [
        self::RAL_TYPE_EMPTY => '空',
        self::RAL_TYPE_POST  => 'post',
        self::RAL_TYPE_GET   => 'get',
    ];

    const RAL_CONVERTER_JSON  = 'json';
    const RAL_CONVERTER_FORM  = 'form';
    const RAL_CONVERTER_EMPTY = ' ';
    public static $ralConverter = [
        self::RAL_CONVERTER_EMPTY => '空',
        self::RAL_CONVERTER_FORM  => 'form',
        self::RAL_CONVERTER_JSON  => 'json',
    ];

    const PARTITION_TYPE_MOD   = '2';
    const PARTITION_TYPE_MUL   = '1';
    const PARTITION_TYPE_EMPTY = ' ';
    public static $partitionType = [
        self::PARTITION_TYPE_EMPTY => '空',
        self::PARTITION_TYPE_MOD   => '取模分表',
        self::PARTITION_TYPE_MUL   => '固定大小分表',
    ];

    public static $configTypeMapLPC = [
        self::CONFIG_TYPE_COURSE      => '课程维度',
        self::CONFIG_TYPE_LESSON      => '章节维度',
        self::CONFIG_TYPE_SEND_COURSE => '赠课维度',
        self::CONFIG_TYPE_CONTRACT_COURSE => '合约课程维度',
        self::CONFIG_TYPE_CONTRACT_LESSON => '合约章节维度',
    ];

    /**
     * 方舟配置业务方
     */
    const ARK_APP_TASK_LIST           = 1; //任务列表
    const ARK_APP_COURSE_DATA         = 2; //班级数据
    const ARK_APP_DETAIL_TAG          = 3; //维系详情标签
    const ARK_APP_DETAIL_CARD         = 4; //维系详情课程卡片
    const ARK_APP_DETAIL_COLUMN       = 5; //维系详情字段列
    const ARK_APP_DETAIL_DELAMINATION = 6; //维系详情分层

    public static $appMap    = [
        self::ARK_APP_TASK_LIST           => '任务列表',
        self::ARK_APP_COURSE_DATA         => '学员表现',
        self::ARK_APP_DETAIL_TAG          => '维系详情标签',
        self::ARK_APP_DETAIL_CARD         => '维系详情课程卡片',
        self::ARK_APP_DETAIL_COLUMN       => '维系详情字段列',
        self::ARK_APP_DETAIL_DELAMINATION => '维系详情分层',

    ];
    public static $appMapLpc = [
        self::ARK_APP_TASK_LIST   => '任务列表',
    ];


    public static $configTypeNoCourse = [
        self::CONFIG_TYPE_ASSISTANT      => '资产维度',
    ];

    public static $appMapNoCourse = [
        self::ARK_APP_TASK_LIST   => '无课例子列表（服务中）',
    ];

    const ARK_DB_SOURCE_TYPE_RAL   = 1;
    const ARK_DB_SOURCE_TYPE_MYSQL = 2;
    public static $dbSourceTypeMap = [
        self::ARK_DB_SOURCE_TYPE_RAL   => 'ral调用（api）',
        self::ARK_DB_SOURCE_TYPE_MYSQL => 'mysql',
    ];
    const ARK_DB_SOURCE_DIMENSION_LEADS = 1;
    const ARK_DB_SOURCE_DIMENSION_STUDENT = 2;
    const ARK_DB_SOURCE_DIMENSION_CONST = 3;
    public static $dbSourceDimensionMap = [
        self::ARK_DB_SOURCE_DIMENSION_LEADS   => 'leadsid 维度',
        self::ARK_DB_SOURCE_DIMENSION_STUDENT => '学生id维度',
    ];

    const ARK_FIELD_GROUP_1 = 1;
    const ARK_FIELD_GROUP_2 = 2;
    const ARK_FIELD_GROUP_3 = 3;
    const ARK_FIELD_GROUP_4 = 4;
    const ARK_FIELD_GROUP_5 = 5;
    const ARK_FIELD_GROUP_6 = 6;
    const ARK_FIELD_GROUP_7 = 7;

    public static $arkFieldGroupMap = [
        self::ARK_FIELD_GROUP_1 => '人员信息类',
        self::ARK_FIELD_GROUP_2 => '任务状态类',
        self::ARK_FIELD_GROUP_3 => '触达状态类',
        self::ARK_FIELD_GROUP_4 => '结果状态类',
        self::ARK_FIELD_GROUP_5 => '辅助信息类',
        self::ARK_FIELD_GROUP_6 => '操作按钮类',
        self::ARK_FIELD_GROUP_7 => 'AI标签类',
    ];

    const ARK_NEW_FIELD_TYPE_0  = 1000;
    const ARK_NEW_FIELD_TYPE_1  = 1001;
    const ARK_NEW_FIELD_TYPE_2  = 1002;
    const ARK_NEW_FIELD_TYPE_3  = 1003;
    const ARK_NEW_FIELD_TYPE_4  = 1004;
    const ARK_NEW_FIELD_TYPE_5  = 1005;
    const ARK_NEW_FIELD_TYPE_6  = 1006;
    const ARK_NEW_FIELD_TYPE_7  = 1007;
    const ARK_NEW_FIELD_TYPE_8  = 1008;
    const ARK_NEW_FIELD_TYPE_9  = 1009;
    const ARK_NEW_FIELD_TYPE_10 = 1010;
    const ARK_NEW_FIELD_TYPE_11 = 1011;
    const ARK_NEW_FIELD_TYPE_12 = 1012;
    const ARK_NEW_FIELD_TYPE_13 = 1013;

    public static $arkNewFieldTypeMap = [
        self::ARK_NEW_FIELD_TYPE_0  => '学员信息',
        self::ARK_NEW_FIELD_TYPE_1  => '基础信息',
        self::ARK_NEW_FIELD_TYPE_2  => '报名信息',
        self::ARK_NEW_FIELD_TYPE_3  => '备注信息',
        self::ARK_NEW_FIELD_TYPE_4  => '学员分层',
        self::ARK_NEW_FIELD_TYPE_5  => '家访信息',
        self::ARK_NEW_FIELD_TYPE_6  => '学习表现',
        self::ARK_NEW_FIELD_TYPE_7  => '沟通活跃度',
        self::ARK_NEW_FIELD_TYPE_8  => '日常维护',
        self::ARK_NEW_FIELD_TYPE_9  => '续报信息',
        self::ARK_NEW_FIELD_TYPE_10 => '其他信息',
        self::ARK_NEW_FIELD_TYPE_11 => '操作按钮',
        self::ARK_NEW_FIELD_TYPE_12 => 'AI标签',
    ];
    public static $arkNewFieldTypeMapLpc = [
        self::ARK_NEW_FIELD_TYPE_0  => '学员信息',
        self::ARK_NEW_FIELD_TYPE_1  => '基础信息',
        self::ARK_NEW_FIELD_TYPE_2  => '报名信息',
        self::ARK_NEW_FIELD_TYPE_3  => '备注信息',
        self::ARK_NEW_FIELD_TYPE_4  => '学员分层',
        self::ARK_NEW_FIELD_TYPE_5  => '家访信息',
        self::ARK_NEW_FIELD_TYPE_6  => '学习表现',
        self::ARK_NEW_FIELD_TYPE_7  => '沟通活跃度',
        self::ARK_NEW_FIELD_TYPE_8  => '日常维护',
        self::ARK_NEW_FIELD_TYPE_9  => '续报信息',
        self::ARK_NEW_FIELD_TYPE_10 => '其他信息',
        self::ARK_NEW_FIELD_TYPE_11 => '操作按钮',
    ];

    const ARK_PERFORMANCE_UNKNOWN = 0;
    const ARK_PERFORMANCE_LOW = 1;
    const  ARK_PERFORMANCE_MID = 2;
    const ARK_PERFORMANCE_HIGH = 3;

    public static $arkPerformanceMap = [
        self::ARK_PERFORMANCE_LOW   => '低性能(有数据聚合或调用多次接口)',
        self::ARK_PERFORMANCE_MID   => '一般性能(单次批量接口调用)',
        self::ARK_PERFORMANCE_HIGH  => '高性能(es直取数据)',
    ];

    public static $arkNewFieldTypeMapNoCourse = [
        self::ARK_NEW_FIELD_TYPE_0  => '学员信息',
        self::ARK_NEW_FIELD_TYPE_1  => '基础信息',
        self::ARK_NEW_FIELD_TYPE_2  => '报名信息',
        self::ARK_NEW_FIELD_TYPE_3  => '备注信息',
        self::ARK_NEW_FIELD_TYPE_4  => '学员分层',
        self::ARK_NEW_FIELD_TYPE_5  => '家访信息',
        self::ARK_NEW_FIELD_TYPE_6  => '学习表现',
        self::ARK_NEW_FIELD_TYPE_7  => '沟通活跃度',
        self::ARK_NEW_FIELD_TYPE_8  => '日常维护',
        self::ARK_NEW_FIELD_TYPE_10 => '其他信息',
        self::ARK_NEW_FIELD_TYPE_11 => '操作按钮',
        self::ARK_NEW_FIELD_TYPE_12 => '所属课程',
    ];

    const ARK_TASK_GROUP_1 = 1;
    const ARK_TASK_GROUP_2 = 2;
    const ARK_TASK_GROUP_3 = 3;
    const ARK_TASK_GROUP_4 = 4;


    public static $arkTaskGroupMap = [
        self::ARK_TASK_GROUP_1 => '任务状态类',
        self::ARK_TASK_GROUP_2 => '触达状态类',
        self::ARK_TASK_GROUP_3 => '结果状态类',
    ];

    public static $arkDataPanelGroupMap = [
        self::ARK_TASK_GROUP_4 => '任务概览类',
        self::ARK_TASK_GROUP_1 => '任务状态类',
        self::ARK_TASK_GROUP_2 => '触达状态类',
        self::ARK_TASK_GROUP_3 => '结果状态类',
    ];

    const COURSE_DATA_SERVICE_ID_COURSE = 1000;
    const COURSE_DATA_SERVICE_ID_LESSON = 1001;

    /**
     * 方舟配置规则
     */
    const ARK_MODULE_TARGET   = 1; //任务指标
    const ARK_MODULE_OVERVIEW = 2; //任务概览
    const ARK_MODULE_FEATURES = 3; //功能工具
    const ARK_MODULE_FIELDS   = 4; //学生列表字段列
    const ARK_MODULE_LPC_FIELDS   = 5; //学生列表字段列 lpc
    const ARK_MODULE_DEER_PROGRAM_FIELDS   = 10; //学生列表字段列 小鹿编程

    const ARK_MODULE_NO_COURSE_FIELDS   = 20; //字段列(私海)

    const ARK_MODULE_DATA_PANEL   = 100; //数据面板，抽象历史的任务指标、任务概览

    const ARK_MODULE_TARGET_DB_SOURCE   = 1000; //指标概览数据源
    const ARK_MODULE_FIELDS_DB_SOURCE   = 1001; //字段列数据源
    const ARK_MODULE_EXPORT_DB_SOURCE   = 1002; //导出数据源

    public static $arkModuleMapBusinessLine = [
        self::ARK_MODULE_TARGET_DB_SOURCE    => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_FIELDS_DB_SOURCE    => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_EXPORT_DB_SOURCE    => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_TARGET              => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_DATA_PANEL          => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_OVERVIEW            => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_FEATURES            => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_FIELDS              => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT,
        self::ARK_MODULE_LPC_FIELDS          => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC,
        self::ARK_MODULE_NO_COURSE_FIELDS    => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_NO_COURSE,
        self::ARK_MODULE_DEER_PROGRAM_FIELDS => Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_DEER_PROGRAM,
    ];

    public static $arkBusinessLineMapFieldType = [
        Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT          => AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS,
        Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC                => AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS,
        Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_DEER_PROGRAM       => AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS,
    ];
    public static $arkModuleMap = [
        self::ARK_MODULE_TARGET     => 'taskTarget',
        self::ARK_MODULE_OVERVIEW   => 'overviewConfig',
        self::ARK_MODULE_FEATURES   => 'featureConfig',
        self::ARK_MODULE_FIELDS     => 'fieldConfig',
        self::ARK_MODULE_LPC_FIELDS => 'fieldConfig',
        self::ARK_MODULE_DEER_PROGRAM_FIELDS => 'fieldConfig',
    ];

    public static $arkModuleNameMap = [
        self::ARK_MODULE_DATA_PANEL            => '数据面板',
        self::ARK_MODULE_TARGET                => '任务指标',
        self::ARK_MODULE_OVERVIEW              => '任务概览',
        self::ARK_MODULE_FEATURES              => '功能工具',
        self::ARK_MODULE_FIELDS                => '字段列',
        self::ARK_MODULE_LPC_FIELDS            => '字段列(LPC)',
        self::ARK_MODULE_NO_COURSE_FIELDS      => '字段列(无课例子)',
        self::ARK_MODULE_DEER_PROGRAM_FIELDS   => '字段列(小鹿编程)',
    ];

    /**
     * 方舟指标数据源方法map
     */
    const ARK_TARGET_DATA_SOURCE_CA               = 1;
    const ARK_TARGET_DATA_SOURCE_CAL              = 2;
    const ARK_TARGET_DATA_SOURCE_CONTINUE         = 3;
    const ARK_TARGET_DATA_SOURCE_CA_RECRUIT       = 4;
    const ARK_TARGET_DATA_SOURCE_ACTIVITY_RECRUIT = 5;
    const ARK_TARGET_DATA_SOURCE_2021CHUN_YATIKE  = 6;
    const ARK_TARGET_DATA_SOURCE_FENXIAO          = 7;
    const ARK_TARGET_DATA_SOURCE_CA_REFUND        = 8;
    const ARK_TARGET_DATA_SOURCE_FENXIAO_21SHU = 9;
    const ARK_TARGET_DATA_SOURCE_LPC_CA    = 10;
    const ARK_TARGET_DATA_SOURCE_LPC_CAL   = 11;
    const ARK_TARGET_DATA_SOURCE_LPC_TRANS = 12;
    const ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_LA  = 13;
    const ARK_TARGET_DATA_SOURCE_CONTRACT_A = 14;
    const ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_CA  = 15;
    const ARK_TARGET_DATA_SOURCE_CONTRACT_LA = 16;
    const ARK_TARGET_DATA_SOURCE_CONTRACT_CA = 17;



    public static $arkTargetDataSourceMap = [
        self::ARK_TARGET_DATA_SOURCE_CA               => 'AssistantDesk_AssistantData::getCaSopData',
        self::ARK_TARGET_DATA_SOURCE_CAL              => 'AssistantDesk_AssistantData::getCalSopData',
        self::ARK_TARGET_DATA_SOURCE_CONTINUE         => 'AssistantDesk_AssistantData::getContinueData',
        self::ARK_TARGET_DATA_SOURCE_CA_RECRUIT       => 'AssistantDesk_AssistantData::getCaRecruitSopData',
        self::ARK_TARGET_DATA_SOURCE_ACTIVITY_RECRUIT => 'AssistantDesk_AssistantData::getActivityRecruitAggData',
        self::ARK_TARGET_DATA_SOURCE_2021CHUN_YATIKE  => 'AssistantDesk_AssistantData::get2021ChunYatikeData',
        self::ARK_TARGET_DATA_SOURCE_FENXIAO          => 'AssistantDesk_AssistantData::getFenXiaoAggData',
        self::ARK_TARGET_DATA_SOURCE_FENXIAO_21SHU    => 'AssistantDesk_AssistantData::getFenXiaoAggData21Shu',
        self::ARK_TARGET_DATA_SOURCE_CA_REFUND        => 'AssistantDesk_AssistantData::getCaRefundSopData',
        self::ARK_TARGET_DATA_SOURCE_LPC_CA           => 'AssistantDesk_AssistantData::getLpcCaData',
        self::ARK_TARGET_DATA_SOURCE_LPC_CAL          => 'AssistantDesk_AssistantData::getLpcCalData',
        self::ARK_TARGET_DATA_SOURCE_LPC_TRANS        => 'AssistantDesk_AssistantData::getLpcTransData',
        self::ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_LA  => 'AssistantDesk_AssistantData::getLpcBusinessLaData',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_A       => 'AssistantDesk_AssistantData::getContractAData',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_LA       => 'AssistantDesk_AssistantData::getContractLAData',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_CA       => 'AssistantDesk_AssistantData::getContractCAData',
        self::ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_CA  => 'AssistantDesk_AssistantData::getLpcBusinessCaData'
    ];

    public static $arkTargetDataSourceNameMap = [
        self::ARK_TARGET_DATA_SOURCE_CA               => 'CA维度数据',
        self::ARK_TARGET_DATA_SOURCE_CAL              => 'CAL维度数据',
        self::ARK_TARGET_DATA_SOURCE_CONTINUE         => '续报数据数据',
        self::ARK_TARGET_DATA_SOURCE_CA_RECRUIT       => '专题课招募数据',
        self::ARK_TARGET_DATA_SOURCE_ACTIVITY_RECRUIT => '扩科招募数据',
        self::ARK_TARGET_DATA_SOURCE_2021CHUN_YATIKE  => '2021春押题课数据',
        self::ARK_TARGET_DATA_SOURCE_FENXIAO          => '分销数据',
        self::ARK_TARGET_DATA_SOURCE_FENXIAO_21SHU    => '分销数据21暑',
        self::ARK_TARGET_DATA_SOURCE_CA_REFUND        => '退费数据',
        self::ARK_TARGET_DATA_SOURCE_LPC_CA           => 'LPC CA数据',
        self::ARK_TARGET_DATA_SOURCE_LPC_CAL          => 'LPC CAL数据',
        self::ARK_TARGET_DATA_SOURCE_LPC_TRANS        => 'LPC 转化数据',
        self::ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_LA  => 'LPC 资产维度LA数据',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_A       => '合约辅导维度数据',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_LA       => '合约章节辅导维度数据',
        self::ARK_TARGET_DATA_SOURCE_CONTRACT_CA       => '合约课程辅导维度数据',
        self::ARK_TARGET_DATA_SOURCE_LPC_BUSINESS_CA  => 'LPC 资产维度CA数据'
    ];


    //用户类型map
    public static $userType = [
        'N1' => 'N1',
        'N2' => 'N2',
        'O1' => 'O1',
        'O2' => 'O2',
        'O3' => 'O3',
        'O4' => 'O4',
        'Z'  => 'Z',
        'R'  => 'R',
        '999'  => '不限',
        '0'   => 'N0',
        '1'   => 'C1',
    ];

    //插班生类型map
    public static $transferType = [
        '0'   =>    '否',
        '1'   =>    '是',
        '10'  =>    '不限',
    ];

    //是否首页展示map
    public static $homepageType = [
        0 =>    '否',
        1 =>    '是'
    ];

    //计算指标时需要过滤的学生类型
    public static $filterUserType = ['T','R'];

    //章节计算逻辑常量
    const LESSON_LIST_TYPE_ALL      = 1;
    const LESSON_LIST_TYPE_PREVIEW  = 2;
    const LESSON_LIST_TYPE_HOMEWORK = 3;
    const LESSON_LIST_TYPE_INCLASS_TEST = 4;
    const LESSON_LIST_TYPE_STAGE_TEST = 5;

    //章节逻辑类型对应测试类型
    public static $lessonListExamTypeMap = [
        self::LESSON_LIST_TYPE_HOMEWORK      => Api_Exam::BIND_TYPE_HOMEWORK,
        self::LESSON_LIST_TYPE_INCLASS_TEST  => Api_Exam::BIND_TYPE_TEST_IN_CLASS,
        self::LESSON_LIST_TYPE_STAGE_TEST    => Api_Exam::BIND_TYPE_STAGE,
    ];
    const DB_KEY_ID_PREFIX = 's_';


    public static function getKeyById($id) {
        return self::DB_KEY_ID_PREFIX . $id;
    }

    /**
     * @param $key
     * @return int|null 不是数据配置的id返回null
     */
    public static function getIdByKey($key) {
        $id = null;
        if (substr($key, 0, 2) == self::DB_KEY_ID_PREFIX) {
            $id = substr($key, 2);
            if (is_numeric($id)) {
                return intval($id);
            } else {
                return null;
            }
        }

        return null;
    }

    public static function getAllRalName() {
        $path = Bd_Conf_Ral::confPath."/services/$service.conf";

    }

    public static function getAllMysqlName() {
        self::$_conf = Bd_Conf::getConf("/db/cluster/$clusterName", null, "mysql");

    }
    public static function isDynamicDataSourceKey($key) {
        return self::getIdByKey($key) !== null;
    }

    public static $serviceDimension = [
        AssistantDesk_Config::TYPE_COURSE   =>  'course',
        AssistantDesk_Config::TYPE_LESSON   =>  'lesson',
        AssistantDesk_Config::TYPE_SEND_COURSE   =>  'sendCourse',
        AssistantDesk_Config::TYPE_CONTRACT      =>  'contract',
        AssistantDesk_Config::TYPE_CONTRACT_COURSE      =>  'contractCourse',
        AssistantDesk_Config::TYPE_CONTRACT_LESSON      =>  'contractLesson',
    ];

    //模板课程绑定状态Map
    const TMPL_COURSE_NOT_BIND  = 1;
    const TMPL_COURSE_BIND      = 2;
    const TMPL_COURSE_UNTYING   = 3;
    public static $tmplCourseStatusMap = [
        self::TMPL_COURSE_NOT_BIND  => '未绑定',
        self::TMPL_COURSE_BIND      => '已绑定',
        self::TMPL_COURSE_UNTYING   => '已解绑',
    ];

    //方舟可显示课程类型
    public static $courseTypeMap = [
        Zb_Const_Course::TYPE_PRIVATE_LONG  => '班课',
        Zb_Const_Course::TYPE_PRIVATE       => '专题课',
    ];
}
