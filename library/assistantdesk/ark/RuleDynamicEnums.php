<?php

/**
 * Class AssistantDesk_Ark_RuleDynamicEnums
 * User: <EMAIL>
 * Date: 2021/04/06
 * Time: 11:53
 */
class AssistantDesk_Ark_RuleDynamicEnums {

    private static $singleton;

    private $businessLine;
    private $enum_values;

    public static function singleton($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT) {
        if (!isset(self::$singleton[$businessLine])) {
            self::$singleton[$businessLine] = new AssistantDesk_Ark_RuleDynamicEnums($businessLine);
        }
        return self::$singleton[$businessLine];
    }

    private function __construct($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT) {
        $this->businessLine = $businessLine;
        $sourceField = $sourceTarget = [];
        $sourceTargetList = (new Service_Data_Ark_ArkConfigRules())->getRulesGroupByModuleId(AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET_DB_SOURCE);
        $sourceTargetList = $sourceTargetList[AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET_DB_SOURCE];
        $sourceFieldList = (new Service_Data_Ark_ArkConfigRules())->getRulesGroupByModuleId(AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS_DB_SOURCE);
        $sourceFieldList = $sourceFieldList[AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS_DB_SOURCE];
        foreach($sourceFieldList as $field) {
            $sourceType = AssistantDesk_Ark_ArkConfig::$dbSourceTypeMap[$field['rule']['dbSourceType']];
            $sourceField[AssistantDesk_Ark_ArkConfig::getKeyById($field['id'])] = "{$field['name']}({$field['remark']}) {$sourceType}";
        }
        foreach($sourceTargetList as $field) {
            $sourceType = AssistantDesk_Ark_ArkConfig::$dbSourceTypeMap[$field['rule']['dbSourceType']];
            $sourceTarget[AssistantDesk_Ark_ArkConfig::getKeyById($field['id'])] = "{$field['name']}({$field['remark']}) {$sourceType}";
        }
        $partitionKeyMap = [];
        $partitionKeyTargetMap = [];
        foreach(AssistantDesk_Ark_Collection::getTargetVariableMap() as $key => $v) {
            $partitionKeyTargetMap[$key] = $key;
        }
        foreach(AssistantDesk_Data_DataSource::getVariableSingleMap() as $key => $v) {
            $partitionKeyMap[$key] = $key;
        }
        $map = [
            Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT => [
                'dimension'          => AssistantDesk_Ark_ArkConfig::$dbSourceDimensionMap,
                'dbSourceType'       => AssistantDesk_Ark_ArkConfig::$dbSourceTypeMap,
                'apps'               => AssistantDesk_Ark_ArkConfig::$appMap,
                'performance'        => AssistantDesk_Ark_ArkConfig::$arkPerformanceMap,
                'source'             => [' ' => '空'] + \AssistantDesk_Data_DataSource::getCodeDataSourceTypeMap() + $sourceField,
                'module'             => AssistantDesk_Ark_ArkConfig::$arkModuleMap,
                'type'               => AssistantDesk_Ark_ArkConfig::$configTypeMap,
                'taskGroup'          => AssistantDesk_Ark_ArkConfig::$arkTaskGroupMap,
                'group'              => AssistantDesk_Ark_ArkConfig::$arkDataPanelGroupMap,
                'dataSource'         => AssistantDesk_Ark_ArkConfig::$arkTargetDataSourceNameMap + $sourceTarget,
                'fieldType'          => AssistantDesk_Ark_ArkConfig::$arkFieldGroupMap,
                'newFieldType'       => AssistantDesk_Ark_ArkConfig::$arkNewFieldTypeMap,
                'togetherType'       => AssistantDesk_Ark_ArkConfig::$togetherTypeMap,
                'partitionKey'       => [' ' => '空'] + $partitionKeyMap,
                'partitionKeyTarget' => [' ' => '空'] + $partitionKeyTargetMap,
                'partitionType'      => AssistantDesk_Ark_ArkConfig::$partitionType,
                'ralMethod'          => AssistantDesk_Ark_ArkConfig::$ralType,
                'ralConverter'       => AssistantDesk_Ark_ArkConfig::$ralConverter,
            ],
            Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_LPC=> [
                'type'          => AssistantDesk_Ark_ArkConfig::$configTypeMapLPC,
                'apps'          => AssistantDesk_Ark_ArkConfig::$appMapLpc,
                'performance'   => AssistantDesk_Ark_ArkConfig::$arkPerformanceMap,
                'source'        => [' '=>'空'] + $sourceField,
                'newFieldType'  => AssistantDesk_Ark_ArkConfig::$arkNewFieldTypeMapLpc,
                'togetherType'  => AssistantDesk_Ark_ArkConfig::$togetherTypeMap,
            ],
            Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_NO_COURSE=> [
                'type'          => AssistantDesk_Ark_ArkConfig::$configTypeNoCourse,
                'apps'          => AssistantDesk_Ark_ArkConfig::$appMapNoCourse,
                'performance'   => AssistantDesk_Ark_ArkConfig::$arkPerformanceMap,
                'newFieldType'  => AssistantDesk_Ark_ArkConfig::$arkNewFieldTypeMapNoCourse,
            ],
            Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_DEER_PROGRAM=> [
                'type'          => AssistantDesk_Ark_ArkConfig::$configTypeMapLPC,
                'apps'          => AssistantDesk_Ark_ArkConfig::$appMapLpc,
                'newFieldType'  => AssistantDesk_Ark_ArkConfig::$arkNewFieldTypeMapLpc,
            ],
        ];
        $this->enum_values = $map[$businessLine] ?? [];
    }

    function __get($name) {
        return $this->enum_values[$name] ?? [];
    }

    public static function values($businessLine = Service_Data_Ark_ArkConfigRules::BUSINESS_LINE_ASSISTANT) {
        return self::singleton($businessLine)->enum_values;
    }
}