<?php
/**
 * Class AssistantDesk_Ark_RuleParser
 * User: liuxian<PERSON>@zuoyebang.com
 * Date: 2021/01/07
 * Time: 19:45
 */
class AssistantDesk_Ark_RuleParser{
    /**
     * 规则属性数据类型
     */
    const RULE_ATTR_VALUE_TYPE_STRING       =  'String';
    const RULE_ATTR_VALUE_TYPE_NUMBER       =  'Number';
    const RULE_ATTR_VALUE_TYPE_OBJECT       =  'Object';
    const RULE_ATTR_VALUE_TYPE_BOOL         =  'Boolean';
    const RULE_ATTR_VALUE_TYPE_SELECT       =  'Select';
    const RULE_ATTR_VALUE_TYPE_CHECKBOX     =  'Checkbox';
    const RULE_ATTR_VALUE_TYPE_JSON         =  'Json';


    public static function getModuleOriRuleMap() {
        return [
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL => [
                'name'  =>  '数据面板',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,
                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '数据key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'name',
                        'nickName'              => '名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '数据维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'group',
                        'nickName'              => '指标类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '数据对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'dataSource',
                        'nickName'              => '数据源',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'performance',
                        'nickName'              => '规则性能级别',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'dataPanelConfig',
                        'nickName'              => '数据面板配置',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_JSON,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'feConfig',
                        'nickName'              => '前端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'filterUserType',
                        'nickName'              => '是否支持过滤学生类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'filterTransferStatus',
                        'nickName'              => '是否支持过滤插班生类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'isDataFilter',
                        'nickName'              => '是否支持数据范围过滤',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                ],
                'sort'  => 1,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET   => [
                'name'  =>  '任务指标',
                'attr'  => [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,
                    ],
                    [
                        'attrName'              => 'taskKey',
                        'nickName'              => '指标key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                        'attrName'              => 'taskName',
                        'nickName'              => '指标名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'taskGroup',
                        'nickName'              => '指标类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '指标维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'dataSource',
                        'nickName'              => '指标数据源',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'numeratorExpression',
                        'nickName'              => '分子表达式',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'denominatorExpression',
                        'nickName'              => '分母表达式',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'filterUserType',
                        'nickName'              => '是否支持过滤学生类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'filterTransferStatus',
                        'nickName'              => '是否支持过滤插班生类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                ],
                'sort'  => 20,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS_DB_SOURCE   => [
                'name'  =>  '字段列数据源',
                'attr'  => [
                    [
                        'attrName'              => 'dbSourceType',
                        'nickName'              => '数据源类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'dimension',
                        'nickName'              => '数据列表维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'studentKey',
                        'nickName'              => '数据列表维度对应key（学生id或leadsid 对应的key）',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'dbQuery',
                        'nickName'              => '查询条件 key 为需要传入的条件key，value变量格式：{{xx}},目前支持的变量有：' . implode(',', array_keys(AssistantDesk_Data_DataSource::getVariableMap())),
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'db',
                        'nickName'              => '数据库名 或ral 服务名',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'path',
                        'nickName'              => '数据库表名 或ral 的path 路径',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'returnListPath',
                        'nickName'              => 'ral 返回数据 返回数据key 如 data.list',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],

                    [
                        'attrName'              => 'isPartition',
                        'nickName'              => 'mysql是否分库',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'partitionKey',
                        'nickName'              => 'mysql分库变量',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'partitionType',
                        'nickName'              => 'mysql 分表类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'partitionNum',
                        'nickName'              => 'mysql 分表值',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                        'attrName'              => 'ralMethod',
                        'nickName'              => 'ral 接口提交方式',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'ralConverter',
                        'nickName'              => 'ral 接口提交类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                ],
                'sort'  => 201,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_EXPORT_DB_SOURCE   => [
                //逐步废弃，不在支持此类型的规则
                'name'  =>  '导出数据源(废弃，后续不支持)',
                'attr'  => [
                    [
                        'attrName'              => 'dimension',
                        'nickName'              => '数据列表维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'studentKey',
                        'nickName'              => '数据列表维度对应key（学生id或leadsid 对应的key）',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'dbQuery',
                        'nickName'              => '查询条件 key 为需要传入的条件key，value变量格式：{{xx}},目前支持的变量有：' . implode(',', array_keys(AssistantDesk_Data_DataSource::getVariableMap())),
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'db',
                        'nickName'              => 'ral 服务名',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'path',
                        'nickName'              => 'ral 的path 路径',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'returnListPath',
                        'nickName'              => 'ral 返回学生数据key 如 data.list',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'returnTitleMapPath',
                        'nickName'              => 'ral 返回标题映射数据key 如 data.titleMap',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'ralMethod',
                        'nickName'              => 'ral 接口提交方式',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'ralConverter',
                        'nickName'              => 'ral 接口提交类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                ],
                'sort'  => 9200,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET_DB_SOURCE   => [
                'name'  =>  '概览或指标数据源',
                'attr'  => [
                    [
                        'attrName'              => 'dbSourceType',
                        'nickName'              => '数据源类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'dbQuery',
                        'nickName'              => '查询条件 key 为需要传入的条件key，value变量格式：{{xx}},目前支持的变量有：' . implode(',', array_keys(AssistantDesk_Ark_Collection::getTargetVariableMap())),
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'db',
                        'nickName'              => '数据库名 或ral 服务名',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'path',
                        'nickName'              => '数据库表名 或ral 的path 路径',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'returnListPath',
                        'nickName'              => 'ral 返回数据 返回数据key 如 data.list',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],

                    [
                        'attrName'              => 'isPartition',
                        'nickName'              => 'mysql是否分库',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'partitionKeyTarget',
                        'nickName'              => 'mysql分库变量',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'partitionType',
                        'nickName'              => 'mysql 分表类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'partitionNum',
                        'nickName'              => 'mysql 分表值',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                        'attrName'              => 'ralMethod',
                        'nickName'              => 'ral 接口提交方式',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'ralConverter',
                        'nickName'              => 'ral 接口提交类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                ],
                'sort'  => 200,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW => [
                'name'  =>  '任务概览',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,
                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '概览key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'name',
                        'nickName'              => '概览名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '概览维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '概览对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'dataSource',
                        'nickName'              => '数据源',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'overviewText',
                        'nickName'              => '概览文案',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'feConfig',
                        'nickName'              => '前端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                ],
                'sort'  => 30,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES => [
                'name'  =>  '功能工具',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '使用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,
                    ],
                    [
                        'attrName'              => 'componentKey',
                        'nickName'              => '功能工具key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'componentName',
                        'nickName'              => '功能工具名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'toolName',
                        'nickName'              => '后台配置名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '功能工具维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '功能工具对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'isRadio',
                        'nickName'              => '是否单选',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'options',
                        'nickName'              => '可配置的选项',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                ],
                'sort'  => 40,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS   => [
                'name'  =>  '字段列',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,

                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '字段列key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,

                    ],
                    [
                        'attrName'              => 'performance',
                        'nickName'              => '规则性能级别',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'oriName',
                        'nickName'              => '后台字段名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'customName',
                        'nickName'              => '后台默认名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '字段列维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'info',
                        'nickName'              => '后台hover提示',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'isExportExcle',
                        'nickName'              => '是否支持导出',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'togetherType',
                        'nickName'              => '通用化字段归属',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'exportFieldMap',
                        'nickName'              => '导出字段映射',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'dataSourceInfo',
                        'nickName'              => '数据源信息:sourceType(数据源):必填(cu),
                    field(从数据源获取的字段),
                    returnField(返回给前端的字段),
                    valueType(返回类型):非必填(int,str,bool,double,float)
                    ',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'source',
                        'nickName'              => '配置的数据源',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '字段列对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'isSelect',
                        'nickName'              => '后台是否默认选择',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'isDisabled',
                        'nickName'              => '后台是否不可更改',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'fieldType',
                        'nickName'              => '旧字段分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'newFieldType',
                        'nickName'              => '字段分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'secondGroup',
                        'nickName'              => '字段二级分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMap',
                        'nickName'              => '筛选map',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'filterMapFunc',
                        'nickName'              => '动态筛选map:优先级比filterMap高，会覆盖filterMap的配置',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMapMulti',
                        'nickName'              => '多筛选:字段名=>展示名称,枚举值格式:a=1&b=2&c=3,是否多选,关联的前端组件,筛选规则类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'sort',
                        'nickName'              => '字段排序',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                    'attrName'              => 'serviceConfig',
                    'nickName'              => '后端配置对象',
                    'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'fusingRule',
                        'nickName'              => '熔断配置:timeoutWarning : 报警的超时时间，只报警不参与降级 单位：ms，timeoutFusing : 触发降级计数器的超时时间 单位：ms，fusingDuration : 熔断的时长 单位：s，duration : 持续时间 单位：s，durationTimes : 持续次数',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'feConfig',
                        'nickName'              => '前端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],

                ],
                'sort'  => 100,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS   => [
                'name'  =>  '多字段列',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,

                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '字段列key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,

                    ],
                    [
                        'attrName'              => 'performance',
                        'nickName'              => '规则性能级别',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'oriName',
                        'nickName'              => '后台字段名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'customName',
                        'nickName'              => '后台默认名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '字段列维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'info',
                        'nickName'              => '后台hover提示',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'isExportExcle',
                        'nickName'              => '是否支持导出',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'togetherType',
                        'nickName'              => '通用化字段归属',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'exportFieldMap',
                        'nickName'              => '导出字段映射',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'dataSourceInfo',
                        'nickName'              => '数据源信息:sourceType(数据源):必填(cu),
                    field(从数据源获取的字段),
                    returnField(返回给前端的字段),
                    valueType(返回类型):非必填(int,str,bool,double,float)
                    ',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'source',
                        'nickName'              => '配置的数据源',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '字段列对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'fieldsFuncMap',
                        'nickName'              => '字段列对应方法(多对多)',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'filterFieldsFuncMap',
                        'nickName'              => '筛选字段列对应方法(多对多)，仅筛选时查询',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
//                    [
//                        'attrName'              => 'enableFilterFunc',
//                        'nickName'              => '字段是否展示过滤方法',
//                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
//                    ],
                    [
                        'attrName'              => 'isSelect',
                        'nickName'              => '后台是否默认选择',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'isDisabled',
                        'nickName'              => '后台是否不可更改',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'newFieldType',
                        'nickName'              => '字段分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'secondGroup',
                        'nickName'              => '字段二级分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMap',
                        'nickName'              => '筛选map',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'filterMapFunc',
                        'nickName'              => '动态筛选map:优先级比filterMap高，会覆盖filterMap的配置',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMapMulti',
                        'nickName'              => '多筛选:字段名=>展示名称,枚举值格式:a=1&b=2&c=3,是否多选,关联的前端组件,筛选规则类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'sort',
                        'nickName'              => '字段排序',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                    'attrName'              => 'serviceConfig',
                    'nickName'              => '后端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'fusingRule',
                        'nickName'              => '熔断配置:timeoutWarning : 报警的超时时间，只报警不参与降级 单位：ms，timeoutFusing : 触发降级计数器的超时时间 单位：ms，fusingDuration : 熔断的时长 单位：s，duration : 持续时间 单位：s，durationTimes : 持续次数',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                    'attrName'              => 'feConfig',
                    'nickName'              => '前端配置对象',
                        'attrValueType' => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                ],
                'sort'  => 110,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_NO_COURSE_FIELDS   => [
                'name'  =>  '字段列(无课例子)',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,

                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '字段列key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'performance',
                        'nickName'              => '规则性能级别',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'oriName',
                        'nickName'              => '后台字段名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'customName',
                        'nickName'              => '后台默认名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '字段列维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'info',
                        'nickName'              => '后台hover提示',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'function',
                        'nickName'              => '字段列对应方法',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'isSelect',
                        'nickName'              => '后台是否默认选择',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'isDisabled',
                        'nickName'              => '后台是否不可更改',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'newFieldType',
                        'nickName'              => '字段分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'secondGroup',
                        'nickName'              => '字段二级分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMap',
                        'nickName'              => '筛选map',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'filterMapFunc',
                        'nickName'              => '动态筛选map:优先级比filterMap高，会覆盖filterMap的配置',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                        'isValidate'            => false,
                    ],
                    [
                        'attrName'              => 'filterMapMulti',
                        'nickName'              => '多筛选:字段名=>展示名称,枚举值格式:a=1&b=2&c=3,是否多选,关联的前端组件,筛选规则类型',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'sort',
                        'nickName'              => '字段排序',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                        'attrName'              => 'serviceConfig',
                        'nickName'              => '后端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'feConfig',
                        'nickName'              => '前端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                ],
                'sort'  => 120,
            ],
            AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS   => [
                'name'  =>  '字段列(小鹿编程)(废弃，后续不支持)',
                'attr'  =>  [
                    [
                        'attrName'              => 'apps',
                        'nickName'              => '适用业务范围',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_CHECKBOX,

                    ],
                    [
                        'attrName'              => 'key',
                        'nickName'              => '字段列key',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,

                    ],
                    [
                        'attrName'              => 'oriName',
                        'nickName'              => '后台字段名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'customName',
                        'nickName'              => '后台默认名称',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'type',
                        'nickName'              => '字段列维度',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'info',
                        'nickName'              => '后台hover提示',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
                    ],
                    [
                        'attrName'              => 'fieldsFuncMap',
                        'nickName'              => '字段列对应方法(多对多)',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
//                    [
//                        'attrName'              => 'enableFilterFunc',
//                        'nickName'              => '字段是否展示过滤方法',
//                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_STRING,
//                    ],
                    [
                        'attrName'              => 'isSelect',
                        'nickName'              => '后台是否默认选择',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'isDisabled',
                        'nickName'              => '后台是否不可更改',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_BOOL,
                    ],
                    [
                        'attrName'              => 'newFieldType',
                        'nickName'              => '字段分类',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_SELECT,
                    ],
                    [
                        'attrName'              => 'filterMap',
                        'nickName'              => '筛选map',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'sort',
                        'nickName'              => '字段排序',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_NUMBER,
                    ],
                    [
                        'attrName'              => 'fusingRule',
                        'nickName'              => '熔断配置:timeoutWarning : 报警的超时时间，只报警不参与降级 单位：ms，timeoutFusing : 触发降级计数器的超时时间 单位：ms，fusingDuration : 熔断的时长 单位：s，duration : 持续时间 单位：s，durationTimes : 持续次数',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                    [
                        'attrName'              => 'feConfig',
                        'nickName'              => '前端配置对象',
                        'attrValueType'         => self::RULE_ATTR_VALUE_TYPE_OBJECT,
                    ],
                ],
                'sort'  => 9100,
            ],
        ];
    }

    public static function formatRules($rules, $module, $app = AssistantDesk_Ark_ArkConfig::ARK_APP_TASK_LIST){
        $config = [];
        $arrApp = [];
        if (is_array($app)) {
            $arrApp = $app;
        } else {
            $arrApp = [$app];
        }
        foreach (AssistantDesk_Ark_ArkConfig::$serviceDimension as $value){
            $config[$value] = [];
        }

        if(!$rules){
            return $config;
        }

        foreach($rules as &$item) {
            $item['rule']['remark'] = $item['remark'] ?? '';
        }
        unset($item);

        $rules = array_column($rules, 'rule');
        $rules = Tools_Array::groupByColumn($rules, 'type');

        switch ($module){

            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_TARGET:
                foreach ($rules as $type => $ruleTypeGroup){
                    $ruleTypeGroup = Tools_Array::groupByColumn($ruleTypeGroup, 'taskGroup');

                    foreach ($ruleTypeGroup as $taskGroup => $ruleTaskGroup){
                        $targets = [];
                        foreach ($ruleTaskGroup as $rule){
                            $rule['key']     = intval($rule['taskKey']);
                            $rule['oriName'] = $rule['taskName'];

                            if(!is_array($rule['apps']) || !array_intersect($arrApp, $rule['apps'])){
                                continue;
                            }

                            $targets[] = $rule;
                        }

                        $row['taskType'] = AssistantDesk_Ark_ArkConfig::$arkTaskGroupMap[$taskGroup];
                        $row['targets']  = $targets;

                        $config[AssistantDesk_Ark_ArkConfig::$serviceDimension[$type]][] = $row;
                    }
                }

                break;
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_DATA_PANEL:
                foreach ($rules as $type => $ruleTypeGroup){
                    $ruleTypeGroup = Tools_Array::groupByColumn($ruleTypeGroup, 'group');
                    foreach ($ruleTypeGroup as $group => $ruleTaskGroup){
                        $dataPanels = [];
                        foreach ($ruleTaskGroup as $rule) {
                            $rule['oriName'] = $rule['name'];

                            if (!is_array($rule['apps']) || !array_intersect($arrApp, $rule['apps'])) {
                                continue;
                            }
                            $dataPanels[] = $rule;
                        }

                        $row['type'] = AssistantDesk_Ark_ArkConfig::$arkDataPanelGroupMap[$group];
                        $row['dataPanels']  = $dataPanels;
                        $config[AssistantDesk_Ark_ArkConfig::$serviceDimension[$type]][] = $row;
                    }
                }
                break;
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_OVERVIEW:

                foreach ($rules as $type => $ruleTypeGroup){
                    foreach ($ruleTypeGroup as $rule) {
                        $row['key']      = $rule['key'];
                        $row['name']     = $rule['name'];
                        $row['function'] = $rule['function'];

                        if(!is_array($rule['apps']) || !array_intersect($arrApp, $rule['apps'])){
                            continue;
                        }

                        $config[AssistantDesk_Ark_ArkConfig::$serviceDimension[$type]][] = $rule;
                    }
                }

                break;

            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_FEATURES:
                foreach ($rules as $type => $ruleTypeGroup){
                    foreach ($ruleTypeGroup as $rule) {
                        $rule['isRadio']       = !in_array($rule['componentKey'], ['qrCode', 'newQrcodeTool']) ? $rule['isRadio'] : false;

                        $options = [];
                        foreach ($rule['options'] as $k => $v){
                            $options[] = [
                                'name'  => $v,
                                'value' => $k,
                            ];
                        }
                        $rule['options']       = $options;

                        if(!is_array($rule['apps']) || !array_intersect($arrApp, $rule['apps'])){
                            continue;
                        }

                        $config[AssistantDesk_Ark_ArkConfig::$serviceDimension[$type]][] = $rule;
                    }
                }

                break;

            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_FIELDS:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_LPC_FIELDS:
            case AssistantDesk_Ark_ArkConfig::ARK_MODULE_DEER_PROGRAM_FIELDS:
                foreach ($rules as $type => $ruleTypeGroup){
                    $ruleTypeGroup = Tools_Array::groupByColumn($ruleTypeGroup, 'newFieldType');

                    foreach ($ruleTypeGroup as $fieldType => $ruleTaskGroup){
                        if(!$fieldType){
                            continue;
                        }
                        $fields = [];
                        foreach ($ruleTaskGroup as $rule){
                            if(!is_array($rule['apps']) || !array_intersect($arrApp, $rule['apps'])){
                                continue;
                            }
                            $fields[] = $rule;
                        }

                        $row['fieldType'] = AssistantDesk_Ark_ArkConfig::$arkNewFieldTypeMap[$fieldType];
                        $row['fieldTypeNum'] = $fieldType;
                        $row['fields']  = $fields;

                        $config[AssistantDesk_Ark_ArkConfig::$serviceDimension[$type]][] = $row;
                    }
                }

                break;

            default:
                break;

        }

        return $config;
    }

    /**
     * @return array
     */
    public static function getModuleRuleMap(){
        $map = [];
        foreach (self::getModuleOriRuleMap() as $moduleId => $moduleMap){
            $businessLine = AssistantDesk_Ark_ArkConfig::$arkModuleMapBusinessLine[$moduleId];
            $map[$moduleId]['name'] = $moduleMap['name'];
            foreach ($moduleMap['attr'] as $rule){
                $rule['enum'] = AssistantDesk_Ark_RuleParser::getRuleDynamicEnums($businessLine, $rule['attrName'], true);
                $map[$moduleId]['attr'][] = $rule;
            }
            $map[$moduleId]['sort'] = $moduleMap['sort'] ?? 5000;
        }
        return $map;
    }

    /**
     * @param $businessLine
     * @param $key
     * @param false $format
     * @return array|mixed
     */
    public static function getRuleDynamicEnums($businessLine, $key, $format = false): array {
        if($format){
            $enums = [];
            foreach (AssistantDesk_Ark_RuleDynamicEnums::singleton($businessLine)->$key as $k => $v){
                $enum['text']  = $v;
                $enum['value'] = $k;
                $enums[] = $enum;
            }
            return $enums;
        }
        return AssistantDesk_Ark_RuleDynamicEnums::singleton($businessLine)->$key;
    }

    /**
     * @param $moduleId
     * @param $rule
     * @return array
     */
    public static function checkRule($moduleId, $rule){
        $config = AssistantDesk_Ark_RuleParser::getModuleOriRuleMap()[$moduleId]['attr'] ? :[];
        $businessLine = AssistantDesk_Ark_ArkConfig::$arkModuleMapBusinessLine[$moduleId];
        foreach ($config as $attr){
            if ($attr['isValidate'] === false) {
                continue;
            }
            if(!isset($rule[$attr['attrName']])
                || !self::checkRuleByType(
                    $rule[$attr['attrName']],
                    $attr['attrValueType'],
                    array_keys(AssistantDesk_Ark_RuleParser::getRuleDynamicEnums($businessLine, $attr['attrName']))
                )
            ){
                return [false, $attr['attrName']];
            }
        }
        return [true, []];
    }

    public static function checkRuleByType($value, $type, $enum = []){
        switch ($type){
            case self::RULE_ATTR_VALUE_TYPE_STRING:
                if(!is_string($value)){
                    return false;
                }
                break;
            case self::RULE_ATTR_VALUE_TYPE_NUMBER:
                if(!is_numeric($value)){
                    return false;
                }
                break;
            case self::RULE_ATTR_VALUE_TYPE_OBJECT:
                if(!is_array($value)){
                    return false;
                }
                break;
            case self::RULE_ATTR_VALUE_TYPE_BOOL:
                if(!is_bool($value)){
                    return false;
                }
                break;
            case self::RULE_ATTR_VALUE_TYPE_SELECT:
                if(!in_array($value, $enum, true)){
                    return false;
                }
                break;
            case self::RULE_ATTR_VALUE_TYPE_CHECKBOX:
                if(!is_array($value) || array_diff($value, $enum)){
                    return false;
                }
                break;
            default:
                return false;
        }
        return true;
    }
}
