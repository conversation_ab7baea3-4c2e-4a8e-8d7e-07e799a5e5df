<?php
/**
 * Created by PhpStorm.
 * User: s<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/6/27
 * Time: 8:33 PM
 */
class Assistant_Ds_WxMessageSendRecord extends Assistant_Common_Db_Data{

    protected $daoClassName = 'Assistant_Dao_WxMessageSendRecord';

    const SEND_SUCCESS  = 0;    //成功
    const SEND_FAILED   = 1;    //失败

    const ACTION_CODE_1 = 1; // 单发
    const ACTION_CODE_2 = 2; // 多发

    // 任务类型
    const TASK_TYPE_INSTANT = 1; // 即时任务
    const TASK_TYPE_DELAYED = 2; // 延时任务

    const SEND_TYPE_DEFAULT               = 0;     // 默认状态：群发操作
    const SEND_TYPE_PREVIEW               = 1;     // 催预习
    const SEND_TYPE_PREATTEND             = 2;     // 一键预到课
    const SEND_TYPE_ATTEND                = 3;     // 催到课
    const SEND_TYPE_PLAYBACK              = 4;     // 催回放
    const SEND_TYPE_NOTES                 = 5;     // 发课堂笔记
    const SEND_TYPE_HOMEWORK              = 6;     // 催巩固练习
    const SEND_TYPE_7                     = 7;     // 寒假预习群发

    // 废弃的发送场景
    //const SEND_TYPE_8                     = 8;     // 讲题项目 - 摸底测
    //const SEND_TYPE_9                     = 9;     // 讲题项目 - 阶段测试
    //const SEND_TYPE_10                    = 10;    // 讲题项目 - 预习
    //const SEND_TYPE_11                    = 11;    // 讲题项目 - 堂堂测
    //const SEND_TYPE_12                    = 12;    // 讲题项目 - 巩固练习

    const SEND_TYPE_SUBMIT_HOMEWORK       = 13;    // 催提交巩固练习
    const SEND_TYPE_REVISE_HOMEWORK       = 14;    // 催订正巩固练习
    const SEND_TYPE_15                    = 15;    // 任务清单


    const SEND_TYPE_16                    = 16;    // 家访问卷升级
    const SEND_TYPE_SATISFACTION_QUE      = 17;    // 催满意度问卷
    const SEND_TYPE_CORRECT_FEEDBACK      = 18;    // 批改结果反馈
    const SEND_TYPE_COMPOSITION_REPORT    = 19;    // 作文报告反馈
    const SEND_TYPE_EXCELLENT_HOMEWORK    = 20;    // 优秀习作

    //  单消息发送
    const SEND_TYPE_PCASSISTANT_SINGLE    = 21;        // 批改系统单人发送
    const SEND_TYPE_NOTE_REVIEW_SINGLE    = 22;        // 单消发送：笔记点评
    const SEND_TYPE_AT_SINGLE             = 23;        // 群内@
    const SEND_TYPE_SEARCH_QUESTION       = 24;        // 拍照搜题

    const SEND_TYPE_HX_PRE_CLASS_PRACTICE  = 31;    // 催浣熊课前练习
    const SEND_TYPE_HX_POWER_CHALLENGE     = 32;    // 催浣熊能力挑战
    const SEND_TYPE_ATTEND_OVERVIEW        = 33;    // 跟课群发
    const SEND_TYPE_SUBMIT_ORAL_QUESTION   = 34;    // 催口述题
    const SEND_TYPE_ORAL_QUESTION_FEEDBACK = 35;    // 口述题反馈
    const SEND_TYPE_SUBMIT_MONTHLY_EXAM    = 36;    // 催月考
    const SEND_TYPE_DA_KA                  = 37;    // 催打卡

    const SEND_TYPE_RJYL                   = 38;    // 催日积月累

    const SEND_TYPE_CUOTIBEN_JIEXI         = 39;    // 错题本-解析版
    const SEND_TYPE_CUOTIBEN_ZUODA         = 40;    // 错题本-作答版
    const SEND_TYPE_MRYL                   = 41;    // 催每日一练
    const SEND_TYPE_ADD_NEW_TEACHER        = 42;    // 催加新老师
    const SEND_TYPE_SINGLEFRIEND_ADD_QIWEI = 43;    // 催个微好友加企微

    const SEND_TYPE_XKYY                    = 50;    // 群发学科运营活动海报
    const SEND_TYPE_MONTHLYEXAM_REPORT      = 44;    // 小英月考报告
    const SEND_TYPE_HIGHLIGHT               = 45;    // 群发高光时刻
    const SEND_TYPE_STUDENT_LIST            = 46;    // 学员表现
    const SEND_TYPE_INTRODUCEPOSTER         = 47; //群发转介绍海报
    const SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK    = 48; //伴读跟课群发
    const SEND_TYPE_STAGE_SUBMIT             = 56; //催提交阶段测
    const SEND_TYPE_STAGE_RESULT_FEEDBACK    = 57; //阶段测结果反馈
    const SEND_TYPE_CORRECT_FEEDBACK_DEER    = 58;    // 批改结果反馈（小鹿）

    const SEND_TYPE_LPC_SMS_ADD_WX       = 65;//发送用户加微短信 65
    const SEND_TYPE_LPC_SMS_ADD_WX_APPLY = 66;//发送加用户微信申请 66
    const SEND_TYPE_LPC_SMS_ADD_WX_CARD  = 67;//发送用户名片给我 67
    const SEND_TYPE_LPC_SMS_ATTEND       = 68;//催到课短信 68
    const SEND_TYPE_RSYNC_CONTACT        = 72;//LPC 同步通讯录
    const SEND_TYPE_LPC_SMS_AI_ATTED     = 76;//LPC AI催到课


    const SEND_TYPE_SUBMIT_HOMEWORK_LIKE = 73;    // 催提交巩固练习相似题
    const SEND_TYPE_REVISE_HOMEWORK_LIKE = 74;    // 催订正巩固练习相似题
    const SEND_TYPE_CORRECT_FEEDBACK_LIKE= 75;    // 相似题批改结果反馈

    const SEND_TYPE_QW_ADD_GROUP         = 80; // 快速拉群

    const SEND_TYPE_BYB_TPL_CITATION     = 90; // 表扬榜：个人奖状
    const SEND_TYPE_BYB_TPL_CERTIFICATE  = 91; // 表扬榜：证书



    // ===================跟课sendtype段=================== //
    // 跟课页面群发使用


    const SEND_TYPE_FEEDBACK_IN_CLASS_PERSON = 92; // 单人课中反馈
    const SEND_TYPE_FEEDBACK_IN_CLASS_GROUP  = 93;  // 群发课中反馈
    const SEND_TYPE_REMIND_CLASS             = 94;  // 催到课
    const SEND_TYPE_REMIND_PLAY_BACK        = 95; // 跟课催回放
    const SEND_TYPE_CONFIRM_BEFORE_CLASS    = 96; // 开课前确认到课方式
    const SEND_TYPE_LIVE_ATTEND            = 97; // 直播催到课
    const SEND_TYPE_BAN_XUE_ATTEND          = 98; // 伴学催到课


    // ===================跟课sendtype段=================== //

    const SEND_TYPE_EXERCISE_NOTE_TASK = 101; //错题本任务
    const SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT = 102; //错题本错清报告
    const SEND_TYPE_EXERCISE_NOTE_TASK_JIEXI = 103; //任务错题本解析版
    const SEND_TYPE_EXERCISE_NOTE_TASK_ZUODA = 104; //任务错题本作答版


    
    const SEND_TYPE_PREVIEW_LPC           = 1001;  // lpc 催预习
    const SEND_TYPE_ATTEND_LPC            = 1003;  // lpc 催到课
    const SEND_TYPE_PLAYBACK_LPC          = 1004;  // lpc 催回放
    const SEND_TYPE_NOTES_LPC             = 1005;  // lpc 群发课程笔记
    const SEND_TYPE_BOTTOM_TEST_REPORT    = 1007;  // lpc 群发摸底测报告
    const SEND_TYPE_BOTTOM_TEST           = 1008;  // lpc 催完成 摸底测
    const SEND_TYPE_PERIOD_EXAM           = 1009;  // lpc 催完成阶段测
    const SEND_TYPE_EXERCISE_REPORT       = 1012;  // lpc 群发巩固练习报告
    const SEND_TYPE_SUBMIT_HOMEWORK_LPC   = 1013;  // lpc 催完成 巩固练习
    const SEND_TYPE_REVISE_HOMEWORK_COMMON   = 1014;  // 通用 催订正巩固练习
    const SEND_TYPE_COMPOSITION_REPORT_COMMON = 1015;  // 通用 作文报告反馈
    const SEND_TYPE_ADD_WX_LPC            = 1042;  // lpc 催加微
    const SEND_TYPE_SERVICE_ADD_WX_LPC    = 1043;  // lpc 服务期催加微
    const SEND_TYPE_HIGHLIGHT_LPC         = 1045;  // lpc 群发高光时刻
    const SEND_TYPE_SURVEY                = 1051;  // lpc 群发挖需问卷
    const SEND_TYPE_CLASS_REPORT          = 1052;  // lpc 群发课堂报告
    const SEND_TYPE_LESSON_REPORT         = 1053;  // 群发章节报告
    const SEND_TYPE_COURSE_REPORT         = 1054;  // 群发课程报告（小鹿）
    const SEND_TYPE_WEEKLY_REPORT         = 1055;  // 群发周报告（小鹿）
    const sEND_TYPE_CORRECT_REPORT        = 1056;  // 群发人工批改报告
    const SEND_TYPE_EVALUATE              = 1057;  // 群发调研问卷
    const SEND_TYPE_COURSE_TIME_TABLE     = 1058;  // 群发课表
    const SEND_TYPE_SIGNAL_SOP            = 1059;  // 单发sop
    const SEND_TYPE_SIGNAL_GROUP_SOP      = 1060; // 单发到群sop
    const SEND_TYPE_MULTI_PERSON_SOP      = 1061; // 群发到人
    const SEND_TYPE_MULTI_GROUP_SOP       = 1062; // 群发到群
    const SEND_TYPE_BOTTOM_TEST_FUDAO     = 1070;  // 催完成摸底测（辅导）

    const SEND_TYPE_BOTTOM_TEST_REPORT_FD = 1071;  // 群发摸底测报告（辅导）

    const SEND_TYPE_DEER_ORDER                      = 2001;  // 小鹿 催预约
    const SEND_TYPE_DEER_CONTINUE                   = 2002;  // 小鹿 催续报
    const SEND_TYPE_DEER_PROGRAM_REPORT             = 2003;  // 小鹿编程课堂报告
    const SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG   = 2004;  // 小鹿编程课堂报告(进校)

    const SEND_TYPE_GUANJIA_ATTEND               = 3000;  // 管家 催到课
    const SEND_TYPE_GUANJIA_COMMIT_49            = 3001;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_50            = 3002;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_51            = 3003;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_52            = 3004;  // 管家 催提交
    const SEND_TYPE_GUANJIA_RE_COMMIT_49            = 3005;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_50            = 3006;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_51            = 3007;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_52            = 3008;  // 管家 催订正

    const SEND_TYPE_LEARN_REPORT_FD                  = 4000;  // 辅导 群发课堂报告
    const SEND_TYPE_SPORT_WEEK_REPORT                 = 4001;  // 群发周报告

    const SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR                  = 4002;  // 阶段测结果反馈高中
    const SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD                  = 4003;  // 阶段测结果反馈卡片发送


    const SEND_TYPE_CONTRACT_SEMESTER_REPORT_SENIOR       = 4004;  // 群发高中学期报告
    const SEND_TYPE_RJYL_REPORT = 4005;  // 发送日积月累报告

    const SEND_TYPE_PRONUNCIATION_REPORT      = 4006;  // 群发纠音报告

    const SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH     = 4007;  // 群发课堂报告(剑桥英语)
    const SEND_TYPE_PRONUNCIATION_REPORT_GROUP      = 4008;  // 群发个性化纠音报告(自定义分组)
    const SEND_TYPE_VOICE_REPORT_GROUP      = 4009;  // 群发个性化纠音报告(自定义分组)
    const SEND_TYPE_UNIT_REPORT_DYD                  = 4010;  // 群发单元报告(大阅读)

    const SEND_TYPE_CONTRACT_SMS_ADD_WX              = 5001;  // 合约，催加微
    const SEND_TYPE_CONTRACT_ATTEND                  = 5002;  // 合约，催到课
    const SEND_TYPE_CONTRACT_COURSE_REPORT           = 5003;  // 群发课堂报告
    const SEND_TYPE_CONTRACT_LESSON_REPORT           = 5004;  // 群发章节报告
    const SEND_TYPE_CONTRACT_AI_OVER_CLASS           = 5005;  // AI催到课
    const SEND_TYPE_CONTRACT_REMEDIAL_CLASS          = 5006;  // 催补课
    const SEND_TYPE_CONTRACT_CORRECT_LESSON_REPORT   = 5007;  // 人工批改报告

    const SEND_TYPE_LEARNING_REPORT                  = 6000;  // 学情测评
    const SEND_TYPE_PROBE_REPORT_QUERY = 6001;  // 测评报告查询
    const SEND_TYPE_EDU_PROBE_PDF = 6002;  // 群发学情报告
    const SEND_TYPE_SUBJECT_DIAG = 6003;  // 学科诊断
    const SEND_TYPE_SUBJECT_DIAG_REPORT = 6004;  // 学科诊断报告
    const SEND_TYPE_PERSONAL_LEARN_FEEDBACK = 6005;  // 个性化学情反馈

    const SEND_TYPE_ASSISTANT_COURSE_APPOINT = 7001; //小灶课预约推送

    const SEND_TYPE_ASSISTANT_COURSE_INCLASS = 7002; //小灶课上课提醒
    const SEND_TYPE_ASSISTANT_COURSE_QUICK_TASK = 7003; //小灶课上课提醒

    const MULTI_MESSAGE_TO_PERSON              = 1; //1v1群发到人
    const MULTI_MESSAGE_TO_PERSON_BY_ASSISTANT = 2; //群发小助手到人
    const MULTI_MESSAGE_TO_GROUP               = 3; //群发到群
    const MULTI_MESSAGE_TO_GROUP_BY_ASSISTANT  = 4; //群发小助手到群
    const SINGLE_MESSAGE_TO_PERSON             = 5; //单发到人
    const SINGLE_MESSAGE_TO_GROUP              = 6; //单发到群
    const MULTI_ADD_GROUP                      = 7; //鲲鹏拉群任务
    public static $allSceneButton = [
        self::SEND_TYPE_PREVIEW,
        self::SEND_TYPE_PREATTEND,
        self::SEND_TYPE_ATTEND,
        self::SEND_TYPE_PLAYBACK,
        self::SEND_TYPE_NOTES,
        self::SEND_TYPE_HOMEWORK,
        self::SEND_TYPE_SUBMIT_HOMEWORK,
        self::SEND_TYPE_REVISE_HOMEWORK,
        self::SEND_TYPE_HX_PRE_CLASS_PRACTICE,
        self::SEND_TYPE_HX_POWER_CHALLENGE,
        self::SEND_TYPE_ATTEND_OVERVIEW,
        self::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK,
        self::SEND_TYPE_16,
        self::SEND_TYPE_SATISFACTION_QUE,
        self::SEND_TYPE_SUBMIT_ORAL_QUESTION,
        self::SEND_TYPE_ORAL_QUESTION_FEEDBACK,
        self::SEND_TYPE_CORRECT_FEEDBACK,
        self::SEND_TYPE_SUBMIT_MONTHLY_EXAM,
        self::SEND_TYPE_COMPOSITION_REPORT,
        self::SEND_TYPE_DA_KA,
        self::SEND_TYPE_RJYL,
        self::SEND_TYPE_CUOTIBEN_JIEXI,
        self::SEND_TYPE_CUOTIBEN_ZUODA,
        self::SEND_TYPE_MRYL,
        self::SEND_TYPE_ADD_NEW_TEACHER,
        self::SEND_TYPE_SINGLEFRIEND_ADD_QIWEI,
        self::SEND_TYPE_XKYY,
        self::SEND_TYPE_MONTHLYEXAM_REPORT,
        self::SEND_TYPE_HIGHLIGHT,
        self::SEND_TYPE_STUDENT_LIST,
        self::SEND_TYPE_INTRODUCEPOSTER,
        self::SEND_TYPE_STAGE_SUBMIT,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK,
        self::SEND_TYPE_SUBMIT_HOMEWORK_LIKE,
        self::SEND_TYPE_REVISE_HOMEWORK_LIKE,
        self::SEND_TYPE_CORRECT_FEEDBACK_LIKE,
        self::SEND_TYPE_CORRECT_FEEDBACK_DEER,
        self::SEND_TYPE_QW_ADD_GROUP,
        self::SEND_TYPE_BYB_TPL_CITATION,
        self::SEND_TYPE_BYB_TPL_CERTIFICATE,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR,
    ];
    // 场景化发送与群发功能 对应的发送按钮御类型

    public static $newSceneButton = [
        self::SEND_TYPE_SATISFACTION_QUE,
        self::SEND_TYPE_PREVIEW,
        self::SEND_TYPE_PREATTEND,
        self::SEND_TYPE_PLAYBACK,
        self::SEND_TYPE_NOTES,
        self::SEND_TYPE_HOMEWORK,
        self::SEND_TYPE_SUBMIT_HOMEWORK,
        self::SEND_TYPE_SUBMIT_ORAL_QUESTION,
        self::SEND_TYPE_ORAL_QUESTION_FEEDBACK,
        self::SEND_TYPE_RJYL,
        self::SEND_TYPE_MRYL,
        self::SEND_TYPE_HIGHLIGHT,
        self::SEND_TYPE_INTRODUCEPOSTER,
        self::SEND_TYPE_PREVIEW_LPC,
        self::SEND_TYPE_BOTTOM_TEST,
        self::SEND_TYPE_BOTTOM_TEST_FUDAO,
        self::SEND_TYPE_PERIOD_EXAM,
        self::SEND_TYPE_EXERCISE_REPORT,
        self::SEND_TYPE_SERVICE_ADD_WX_LPC,
        self::SEND_TYPE_ATTEND,
        self::SEND_TYPE_7,
        self::SEND_TYPE_ATTEND_OVERVIEW,
        self::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK,
        self::SEND_TYPE_REVISE_HOMEWORK,
        self::SEND_TYPE_16,
        self::SEND_TYPE_CORRECT_FEEDBACK,
        self::SEND_TYPE_COMPOSITION_REPORT,
        self::SEND_TYPE_SUBMIT_MONTHLY_EXAM,
        self::SEND_TYPE_CUOTIBEN_JIEXI,
        self::SEND_TYPE_CUOTIBEN_ZUODA,
        self::SEND_TYPE_MONTHLYEXAM_REPORT,
        self::SEND_TYPE_ATTEND_LPC,
        self::SEND_TYPE_PLAYBACK_LPC,
        self::SEND_TYPE_NOTES_LPC,
        self::SEND_TYPE_BOTTOM_TEST_REPORT,
        self::SEND_TYPE_SUBMIT_HOMEWORK_LPC,
        self::SEND_TYPE_ADD_WX_LPC,
        self::SEND_TYPE_HIGHLIGHT_LPC,
        self::SEND_TYPE_SURVEY,
        self::SEND_TYPE_CLASS_REPORT,
        self::SEND_TYPE_STAGE_SUBMIT,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK,
        self::SEND_TYPE_SUBMIT_HOMEWORK_LIKE,
        self::SEND_TYPE_REVISE_HOMEWORK_LIKE,
        self::SEND_TYPE_CORRECT_FEEDBACK_LIKE,
        self::SEND_TYPE_CORRECT_FEEDBACK_DEER,
        self::SEND_TYPE_QW_ADD_GROUP,
        self::SEND_TYPE_BYB_TPL_CITATION,
        self::SEND_TYPE_BYB_TPL_CERTIFICATE,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR
    ];

    // 消息发送（单发）按钮MAP
    public static $singleSendButtonMap = [
        self::SEND_TYPE_PCASSISTANT_SINGLE,
        self::SEND_TYPE_AT_SINGLE,
        self::SEND_TYPE_NOTE_REVIEW_SINGLE,
        self::SEND_TYPE_SEARCH_QUESTION,
    ];

    //发送类型对应名称
    public static $sendTypeNameMap = [
        self::SEND_TYPE_CORRECT_FEEDBACK => '批改结果反馈',
    ];

    public static function getSendMethodMap($sendType) {
        return self::$sendMethodMap[$sendType] ?? (20000 + $sendType);
    }
    // 按照鲲鹏侧的需求，他们需要一个sendMethod值，需要在发送消息的时候带过去，用于记录我们的发送场景
    public static $sendMethodMap = [
        // 多消息发送
        self::SEND_TYPE_DEFAULT                => 30000,
        self::SEND_TYPE_PREVIEW                => 20001,
        self::SEND_TYPE_PREATTEND              => 20002,
        self::SEND_TYPE_ATTEND                 => 20003,
        self::SEND_TYPE_PLAYBACK               => 20004,
        self::SEND_TYPE_NOTES                  => 20005,
        self::SEND_TYPE_HOMEWORK               => 20006,
        self::SEND_TYPE_7                      => 20007,
        self::SEND_TYPE_SUBMIT_HOMEWORK        => 20013,
        self::SEND_TYPE_REVISE_HOMEWORK        => 20014,
        self::SEND_TYPE_15                     => 20015,
        self::SEND_TYPE_16                     => 20016,
        self::SEND_TYPE_SATISFACTION_QUE       => 20017,
        self::SEND_TYPE_CORRECT_FEEDBACK       => 20018,
        self::SEND_TYPE_COMPOSITION_REPORT     => 20019,
        self::SEND_TYPE_EXCELLENT_HOMEWORK     => 20020,
        self::SEND_TYPE_HX_PRE_CLASS_PRACTICE  => 20031,
        self::SEND_TYPE_HX_POWER_CHALLENGE     => 20032,
        self::SEND_TYPE_ATTEND_OVERVIEW        => 20033,
        self::SEND_TYPE_SUBMIT_ORAL_QUESTION   => 20034,
        self::SEND_TYPE_ORAL_QUESTION_FEEDBACK => 20035,
        self::SEND_TYPE_SUBMIT_MONTHLY_EXAM    => 20036,
        self::SEND_TYPE_DA_KA                  => 20037,
        self::SEND_TYPE_RJYL                   => 20038,
        self::SEND_TYPE_CUOTIBEN_JIEXI         => 20039,
        self::SEND_TYPE_CUOTIBEN_ZUODA         => 20040,
        self::SEND_TYPE_MRYL                   => 20041,
        self::SEND_TYPE_ADD_NEW_TEACHER        => 20042,
        self::SEND_TYPE_SINGLEFRIEND_ADD_QIWEI => 20043,
        self::SEND_TYPE_XKYY                   => 20050,
        self::SEND_TYPE_MONTHLYEXAM_REPORT     => 20044,
        self::SEND_TYPE_HIGHLIGHT              => 20045,
        self::SEND_TYPE_STUDENT_LIST           => 20046,
        self::SEND_TYPE_INTRODUCEPOSTER        => 20047,
        self::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK => 20048,
        self::SEND_TYPE_STAGE_SUBMIT           => 20049,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK  => 20050,
        self::SEND_TYPE_SUBMIT_HOMEWORK_LIKE   => 20073,
        self::SEND_TYPE_REVISE_HOMEWORK_LIKE   => 20074,
        self::SEND_TYPE_CORRECT_FEEDBACK_LIKE  => 20075,
        self::SEND_TYPE_CORRECT_FEEDBACK_DEER  => 20058,
        self::SEND_TYPE_QW_ADD_GROUP           => 20080,
        self::SEND_TYPE_BYB_TPL_CITATION       => 20090,
        self::SEND_TYPE_BYB_TPL_CERTIFICATE    => 20091,
        self::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR    => 20092,
    ];

    /**
     * Notes:添加记录
     * @param array | $arrParams
     * @return int| bool 插入数据的id
     */
    public function insertRecord($arrParams){
        if (empty($arrParams)) {
            return false;
        }

        $ret =  $this->insert($arrParams);
        if($ret){
            $ret = $this->getInsertId();
        }
        return $ret;
    }
}
